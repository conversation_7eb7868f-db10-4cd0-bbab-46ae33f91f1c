import React, { useState, useCallback } from 'react';
import { Button, message, Space } from 'antd';
import { CopyOutlined, DownOutlined, RightOutlined, ExpandOutlined, ShrinkOutlined } from '@ant-design/icons';

interface JsonViewerProps {
  data: any;
  title?: string;
}

interface JsonNodeProps {
  data: any;
  keyName?: string;
  level?: number;
  isLast?: boolean;
  globalCollapsedLevels?: Set<number>;
  onToggleLevel?: (level: number) => void;
}

const JsonNode: React.FC<JsonNodeProps> = ({
  data,
  keyName,
  level = 0,
  isLast = true,
  globalCollapsedLevels = new Set(),
  onToggleLevel
}) => {
  const [localCollapsed, setLocalCollapsed] = useState(false);

  // 检查当前层级是否被全局折叠
  const isGloballyCollapsed = globalCollapsedLevels.has(level);
  const collapsed = isGloballyCollapsed || localCollapsed;
  
  const indent = level * 20;
  const isObject = typeof data === 'object' && data !== null && !Array.isArray(data);
  const isArray = Array.isArray(data);
  const isExpandable = isObject || isArray;
  
  const getValueColor = (value: any) => {
    if (value === null) return '#808080';
    if (typeof value === 'string') return '#4ECDC4';
    if (typeof value === 'number') return '#FFEAA7';
    if (typeof value === 'boolean') return '#A29BFE';
    return '#f8f8f2';
  };
  
  const renderValue = (value: any) => {
    if (value === null) return 'null';
    if (typeof value === 'string') return `"${value}"`;
    return String(value);
  };
  
  const copyValue = (value: any) => {
    const text = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    });
  };

  const handleToggleCollapse = () => {
    if (isGloballyCollapsed && onToggleLevel) {
      // 如果是全局折叠状态，点击时取消该层级的全局折叠
      onToggleLevel(level);
    } else {
      // 否则切换本地折叠状态
      setLocalCollapsed(!localCollapsed);
    }
  };
  
  if (!isExpandable) {
    return (
      <div style={{ marginLeft: indent, fontFamily: 'Monaco, Consolas, monospace', fontSize: '12px' }}>
        {keyName && (
          <span style={{ color: '#FF6B6B' }}>"{keyName}": </span>
        )}
        <span 
          style={{ 
            color: getValueColor(data), 
            cursor: 'pointer',
            userSelect: 'text'
          }}
          onClick={() => copyValue(data)}
          title="点击复制"
        >
          {renderValue(data)}
        </span>
        {!isLast && <span style={{ color: '#DDA0DD' }}>,</span>}
      </div>
    );
  }
  
  const entries = isArray ? data.map((item: any, index: number) => [index, item]) : Object.entries(data);
  
  return (
    <div style={{ marginLeft: indent, fontFamily: 'Monaco, Consolas, monospace', fontSize: '12px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <Button
          type="text"
          size="small"
          icon={collapsed ? <RightOutlined /> : <DownOutlined />}
          onClick={handleToggleCollapse}
          style={{
            padding: '0 4px',
            minWidth: 'auto',
            height: '16px',
            backgroundColor: isGloballyCollapsed ? '#1890ff20' : 'transparent'
          }}
          title={isGloballyCollapsed ? '该层级已全局折叠，点击取消' : '点击折叠/展开'}
        />
        {keyName && (
          <span style={{ color: '#FF6B6B' }}>"{keyName}": </span>
        )}
        <span style={{ color: '#DDA0DD' }}>
          {isArray ? '[' : '{'}
          {collapsed && (
            <span style={{ color: '#6C7B7F', fontStyle: 'italic' }}>
              {isArray ? ` ${data.length} items ` : ` ${entries.length} keys `}
            </span>
          )}
          {collapsed && (isArray ? ']' : '}')}
        </span>
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={() => copyValue(data)}
          style={{ padding: '0 4px', minWidth: 'auto', height: '16px' }}
          title="复制此对象"
        />
      </div>
      
      {!collapsed && (
        <>
          {entries.map(([key, value], index) => (
            <JsonNode
              key={key}
              data={value}
              keyName={isArray ? undefined : String(key)}
              level={level + 1}
              isLast={index === entries.length - 1}
              globalCollapsedLevels={globalCollapsedLevels}
              onToggleLevel={onToggleLevel}
            />
          ))}
          <div style={{ marginLeft: indent + 20, color: '#DDA0DD' }}>
            {isArray ? ']' : '}'}
            {!isLast && ','}
          </div>
        </>
      )}
    </div>
  );
};

const JsonViewer: React.FC<JsonViewerProps> = ({ data, title }) => {
  const [collapsedLevels, setCollapsedLevels] = useState<Set<number>>(new Set());

  const copyAll = () => {
    const text = JSON.stringify(data, null, 2);
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制完整JSON到剪贴板');
    });
  };

  // 获取JSON的最大深度
  const getMaxDepth = useCallback((obj: any, currentDepth = 0): number => {
    if (obj === null || typeof obj !== 'object') {
      return currentDepth;
    }

    const values = Array.isArray(obj) ? obj : Object.values(obj);
    if (values.length === 0) {
      return currentDepth;
    }

    return Math.max(...values.map(value => getMaxDepth(value, currentDepth + 1)));
  }, []);

  const maxDepth = getMaxDepth(data);

  // 折叠指定层级及其以下所有层级
  const collapseFromLevel = (level: number) => {
    const newCollapsedLevels = new Set<number>();
    for (let i = level; i <= maxDepth; i++) {
      newCollapsedLevels.add(i);
    }
    setCollapsedLevels(newCollapsedLevels);
  };

  // 展开指定层级及其以下所有层级
  const expandToLevel = (level: number) => {
    const newCollapsedLevels = new Set(collapsedLevels);
    for (let i = 0; i <= level; i++) {
      newCollapsedLevels.delete(i);
    }
    setCollapsedLevels(newCollapsedLevels);
  };

  // 切换指定层级的折叠状态
  const toggleLevel = (level: number) => {
    const newCollapsedLevels = new Set(collapsedLevels);
    if (newCollapsedLevels.has(level)) {
      newCollapsedLevels.delete(level);
    } else {
      newCollapsedLevels.add(level);
    }
    setCollapsedLevels(newCollapsedLevels);
  };

  // 全部展开
  const expandAll = () => {
    setCollapsedLevels(new Set());
  };

  // 全部折叠（只显示第一层）
  const collapseAll = () => {
    const newCollapsedLevels = new Set<number>();
    for (let i = 1; i <= maxDepth; i++) {
      newCollapsedLevels.add(i);
    }
    setCollapsedLevels(newCollapsedLevels);
  };
  
  return (
    <div style={{ 
      background: '#1a1a1a', 
      border: '1px solid #3b3a32',
      borderRadius: '4px',
      padding: '12px',
      maxHeight: '400px',
      overflow: 'auto'
    }}>
      {title && (
        <div style={{
          marginBottom: '12px',
          paddingBottom: '12px',
          borderBottom: '1px solid #3b3a32'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '8px'
          }}>
            <span style={{ color: '#f8f8f2', fontWeight: 'bold', fontSize: '14px' }}>{title}</span>
            <Button
              type="primary"
              size="small"
              icon={<CopyOutlined />}
              onClick={copyAll}
            >
              复制全部
            </Button>
          </div>

          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', alignItems: 'center' }}>
            <span style={{ color: '#999', fontSize: '12px' }}>层级控制:</span>
            <Space size="small">
              <Button
                size="small"
                icon={<ExpandOutlined />}
                onClick={expandAll}
                title="展开所有层级"
              >
                全部展开
              </Button>
              <Button
                size="small"
                icon={<ShrinkOutlined />}
                onClick={collapseAll}
                title="折叠到第一层"
              >
                全部折叠
              </Button>
            </Space>

            <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>快速展开到:</span>
            <Space size="small">
              {Array.from({ length: Math.min(maxDepth + 1, 5) }, (_, i) => (
                <Button
                  key={i}
                  size="small"
                  type={!collapsedLevels.has(i + 1) && collapsedLevels.has(i + 2) ? 'primary' : 'default'}
                  onClick={() => {
                    if (i === 0) {
                      collapseFromLevel(1);
                    } else {
                      expandToLevel(i);
                      collapseFromLevel(i + 1);
                    }
                  }}
                  title={`展开到第${i + 1}层`}
                >
                  L{i + 1}
                </Button>
              ))}
            </Space>
          </div>
        </div>
      )}
      <JsonNode
        data={data}
        globalCollapsedLevels={collapsedLevels}
        onToggleLevel={toggleLevel}
      />
    </div>
  );
};

export default JsonViewer;
