import React, { useState } from 'react';
import { Button, message } from 'antd';
import { CopyOutlined, DownOutlined, RightOutlined } from '@ant-design/icons';

interface JsonViewerProps {
  data: any;
  title?: string;
}

interface JsonNodeProps {
  data: any;
  keyName?: string;
  level?: number;
  isLast?: boolean;
}

const JsonNode: React.FC<JsonNodeProps> = ({ data, keyName, level = 0, isLast = true }) => {
  const [collapsed, setCollapsed] = useState(false);
  
  const indent = level * 20;
  const isObject = typeof data === 'object' && data !== null && !Array.isArray(data);
  const isArray = Array.isArray(data);
  const isExpandable = isObject || isArray;
  
  const getValueColor = (value: any) => {
    if (value === null) return '#808080';
    if (typeof value === 'string') return '#4ECDC4';
    if (typeof value === 'number') return '#FFEAA7';
    if (typeof value === 'boolean') return '#A29BFE';
    return '#f8f8f2';
  };
  
  const renderValue = (value: any) => {
    if (value === null) return 'null';
    if (typeof value === 'string') return `"${value}"`;
    return String(value);
  };
  
  const copyValue = (value: any) => {
    const text = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    });
  };
  
  if (!isExpandable) {
    return (
      <div style={{ marginLeft: indent, fontFamily: 'Monaco, Consolas, monospace', fontSize: '12px' }}>
        {keyName && (
          <span style={{ color: '#FF6B6B' }}>"{keyName}": </span>
        )}
        <span 
          style={{ 
            color: getValueColor(data), 
            cursor: 'pointer',
            userSelect: 'text'
          }}
          onClick={() => copyValue(data)}
          title="点击复制"
        >
          {renderValue(data)}
        </span>
        {!isLast && <span style={{ color: '#DDA0DD' }}>,</span>}
      </div>
    );
  }
  
  const entries = isArray ? data.map((item: any, index: number) => [index, item]) : Object.entries(data);
  
  return (
    <div style={{ marginLeft: indent, fontFamily: 'Monaco, Consolas, monospace', fontSize: '12px' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <Button
          type="text"
          size="small"
          icon={collapsed ? <RightOutlined /> : <DownOutlined />}
          onClick={() => setCollapsed(!collapsed)}
          style={{ padding: '0 4px', minWidth: 'auto', height: '16px' }}
        />
        {keyName && (
          <span style={{ color: '#FF6B6B' }}>"{keyName}": </span>
        )}
        <span style={{ color: '#DDA0DD' }}>
          {isArray ? '[' : '{'}
          {collapsed && (
            <span style={{ color: '#6C7B7F', fontStyle: 'italic' }}>
              {isArray ? ` ${data.length} items ` : ` ${entries.length} keys `}
            </span>
          )}
          {collapsed && (isArray ? ']' : '}')}
        </span>
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={() => copyValue(data)}
          style={{ padding: '0 4px', minWidth: 'auto', height: '16px' }}
          title="复制此对象"
        />
      </div>
      
      {!collapsed && (
        <>
          {entries.map(([key, value], index) => (
            <JsonNode
              key={key}
              data={value}
              keyName={isArray ? undefined : String(key)}
              level={level + 1}
              isLast={index === entries.length - 1}
            />
          ))}
          <div style={{ marginLeft: indent + 20, color: '#DDA0DD' }}>
            {isArray ? ']' : '}'}
            {!isLast && ','}
          </div>
        </>
      )}
    </div>
  );
};

const JsonViewer: React.FC<JsonViewerProps> = ({ data, title }) => {
  const copyAll = () => {
    const text = JSON.stringify(data, null, 2);
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制完整JSON到剪贴板');
    });
  };
  
  return (
    <div style={{ 
      background: '#1a1a1a', 
      border: '1px solid #3b3a32',
      borderRadius: '4px',
      padding: '12px',
      maxHeight: '400px',
      overflow: 'auto'
    }}>
      {title && (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '8px',
          paddingBottom: '8px',
          borderBottom: '1px solid #3b3a32'
        }}>
          <span style={{ color: '#f8f8f2', fontWeight: 'bold', fontSize: '14px' }}>{title}</span>
          <Button
            type="primary"
            size="small"
            icon={<CopyOutlined />}
            onClick={copyAll}
          >
            复制全部
          </Button>
        </div>
      )}
      <JsonNode data={data} />
    </div>
  );
};

export default JsonViewer;
