import React, {useEffect, useMemo, useRef, useState} from "react";
import {Button, Card, Col, Image, message, Modal, Radio, Row, Tag, Tooltip, Tree} from "antd";
import TextArea from "antd/es/input/TextArea";
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import {KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import Link from "antd/es/typography/Link";
import {RELEVANT_MAP, TAG_COLOR_MAP} from "@/pages/AgentLiveV2/constant";
import {Frame} from "@/interfaces/Frame";
import {LabelResultItem} from "@/interfaces/LabelResultItem";
import LabelGoodCardSmall from "@/components/Cards/AgentLiveV2Cards/LabelGoodCardSmall";
import * as API from "@/common/API/AgentLiveV2API";

interface LabelFrameListProps {
    frameList: Frame[];
    liveStreamId?: number; // 添加liveStreamId属性
    labelResult: LabelResultItem[] | null;
    replayId:number;
}

const LabelFrameListRowCard: React.FC<LabelFrameListProps> = ({ frameList,labelResult, replayId,liveStreamId }) => {

    // Ref：视频播放器
    const playerRef1 = useRef<KwaiPlayer | null>(null);
    // State：是否打开 JSON Modal
    const [openVideoModal, setOpenVideoModal] = useState<{ [key: number]: boolean }>({});
    // State：当前正在播放的视频 URL
    const [currentVideoUrl, setCurrentVideoUrl] = useState<string>('');
    // State：哪个 frame 的 JSON Modal 正在打开
    const [jsonFrame, setJsonFrame] = useState<Frame | null>(null);
    // Memo：倒序的 frameList
    const reversedFrames = useMemo(() => frameList.slice().reverse(), [frameList]);
    // State：标注结果
    const [labels, setLabels] = useState<{ [itemId: number]: string }>({});
    // State：是否处于编辑模式
    const [editMode, setEditMode] = useState<boolean>(true);
    // State：是否禁用Radio
    const [radioDisabled, setRadioDisabled] = useState<boolean>(false);
    // State：标注人信息
    const [labelUserInfo, setLabelUserInfo] = useState<any>(null);

    // 获取标注人信息的函数
    const fetchLabelUserInfo = async (replayId: number) => {
        try {
            const response = await API.QueryLabelUserInfoForReplay({replayId: replayId});
            if (response.code === "0") {
                const labelUserInfo = JSON.parse(response.data);
                setLabelUserInfo(labelUserInfo);
            } else {
                console.error("获取标注人信息失败", response.error_msg);
            }
        } catch (error) {
            console.error("获取标注人信息失败", error);
        }
    };

    // 初始化（回显已有标注结果）
    useEffect(() => {
        if(labelResult === null){
            // 没标注 → 标注模式
            setLabels({});
            setEditMode(true);
            setLabelUserInfo(null);
            setRadioDisabled(false); // 启用 Radio
        }else if(labelResult.length === 0){
            // 如果是空数组，表示选择了“不标注”
            setLabels({});
            setEditMode(false); // 设置为展示模式
            setRadioDisabled(true); // 禁用 Radio
            if (replayId) {
                console.log("labelResult.length === 0", replayId);
                fetchLabelUserInfo(replayId); // 请求标注人信息
            }
        }else {
            const init: { [itemId: number]: string } = {};
            labelResult.forEach(labelResultItem => {
                init[labelResultItem.itemId] = labelResultItem.relevant;
            });
            setLabels(init);
            setRadioDisabled(false); // 启用 Radio
            setEditMode(false); // 有标注 → 展示模式
            if (replayId) {
                console.log("labelResult.length !== 0", replayId);
                fetchLabelUserInfo(replayId); // 请求标注人信息
            }
        }
    }, [labelResult]);

    // 选择标注
    const handleSelectLabel = (itemId: number, value: string) => {
        setLabels(prev => ({
            ...prev,
            [itemId]: value,
        }));
    };

    // 是否所有图片都已标注
    const allLabeled = frameList.every((frame) =>
        (frame.topSimilarItems || []).slice(0, 15).every((item) => labels[item.itemId] !== undefined)
    );

    // 标注结果提交
    const handleSubmit = async (isNotLabeled: boolean = false) => {
        let payload: LabelResultItem[] = [];
        if (isNotLabeled) {
            payload = []; // 选择“不标注”
            setLabels({});
        } else {
            payload = (frameList?.[0]?.topSimilarItemIds || []).map(item => ({
                itemId: item.itemId,
                score: item.fineTuneScore,
                bestImagePath: item.imgUrl ?? null, // 这里兼容一下 null
                relevant: labels[item.itemId] ?? "0",       // 默认给 "0"
            }));

        }

        const params = {
            replayId,       // 参数里传入的 replayId
            labelResult: payload,
            labelStatus: isNotLabeled ? 0 : 1,  // 如果是“不标注”，labelStatus 为 0
        };

        const result = await API.UpdateLabelForReplay(params);
        if (result.code === '0') {
            const msg = result.result === 1 ? '当前标注提交成功'
                : result.result === 0 ? '当前标注更新成功'
                : '';
            msg && message.success(msg);
        } else {
            message.error(`当前标注提交失败: ${result.error_msg}`);
        }
        setEditMode(false);
    };

    // 标注结果提交确认
    const onSubmitClick = () => {
        Modal.confirm({
            title: "确认提交本次标注？",
            content: (
                <span>
                标注辛苦啦(≧◡≦) ♡，提交前请再仔细检查一下标注结果哦，提交后
                <span style={{ color: 'red' }}>非必要情况下，尽量不要反复修改～</span>
            </span>
            ),
            okText: "确认提交",
            cancelText: "取消",
            onOk: () => handleSubmit(),
        });
    };

    // 不标注按钮点击事件
    const onNoLabelClick = () => {
        Modal.confirm({
            title: "确认选择不标注？",
            content: "当前抽帧质量太差，选择不标注。确认后将提交不标注信息。",
            okText: "确认提交",
            cancelText: "取消",
            onOk: () => handleSubmit(true),  // 传递 true 表示不标注
        });
    };

    // 打开 抽帧视频 Modal 时，传入 index 和视频 URL
    const handleOpenVideoModal = (index: number, videoUrl: string) => {
        setOpenVideoModal(prev => ({ ...prev, [index]: true }));
        setCurrentVideoUrl(videoUrl.replace(/^http:/, 'https:'));
    };

    // 关闭 抽帧视频 Modal 时，传入 index
    const handleCloseVideoModal = (index: number) => {
        setOpenVideoModal(prev => ({ ...prev, [index]: false }));
        setCurrentVideoUrl('');
    };

    // 将 JSON 数据转换为 Tree 结构
    const jsonToTreeData = (data: any, path = ""): any[] => {
        if (data === null || typeof data !== "object") {
            return [{ title: `${path}: ${String(data)}`, key: path }];
        }
        if (Array.isArray(data)) {
            return data.map((item, idx) => ({
                title:
                    typeof item === "object"
                        ? `[${idx}]`
                        : `[${idx}]: ${String(item)}`,
                key: `${path}[${idx}]`,
                children:
                    typeof item === "object" && item !== null
                        ? jsonToTreeData(item, `${path}[${idx}]`)
                        : undefined,
            }));
        }
        return Object.entries(data).map(([k, v]) => ({
            title:
                typeof v === "object" && v !== null
                    ? k
                    : `${k}: ${String(v)}`,
            key: path ? `${path}.${k}` : k,
            children:
                typeof v === "object" && v !== null
                    ? jsonToTreeData(v, path ? `${path}.${k}` : k)
                    : undefined,
        }));
    };

    // 复制 JSON 数据到剪切板
    const handleCopyJson = () => {
        if (!jsonFrame) return;
        const text = JSON.stringify(jsonFrame, null, 2);
        navigator.clipboard
            ?.writeText(text)
            .then(() => message.success("已复制 JSON 数据到剪切板"))
            .catch(() => {
                // 兼容老浏览器
                const textarea = document.createElement("textarea");
                textarea.value = text;
                textarea.style.position = "fixed";
                textarea.style.opacity = "0";
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                document.body.removeChild(textarea);
                message.success("已复制 JSON 数据到剪切板");
            });
    };

    return (
        <div>
            {reversedFrames.map((frame) => (
                <Row key={frame.index} gutter={[6, 6]} style={{ marginBottom: 4 }}>
                    <Col>
                        <div style={{ display: "flex", flexDirection: "column", gap: 2 }}>
                        <Card
                            title={`直播抽帧`}
                            size="small"
                            style={{ width: 240, height: 500, padding: 0 }}
                            bodyStyle={{ padding: 0 }}
                        >
                            {/* 上半部分：图片 */}
                            <div style={{ 
                                width: "100%", 
                                height: 380,
                                overflow: "hidden", 
                                marginBottom: 6, 
                                marginTop: 6, 
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center"
                            }}>
                                <Image
                                    src={frame.screenSnapshotUrls[0]}
                                    alt="直播抽帧"
                                    width={180}
                                    height={380}
                                    style={{ objectFit: 'contain' }}
                                />
                            </div>

                            {/* 下半部分：左右布局 */}
                            <div
                                style={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    padding: "15px",
                                    borderTop: '1px dashed #eee'
                                }}
                            >
                                <Link
                                    onClick={() => handleOpenVideoModal(frame.index, frame.clipVideoUrl)}
                                >
                                    抽帧视频
                                </Link>
                                <Link onClick={() => setJsonFrame(frame)}>
                                    JSON 数据
                                </Link>
                            </div>
                        </Card>

                    {/* 中卡：ASR */}
                        <Card
                            size="small"
                            title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>ASR 文本</span>}
                            style={{ height: 300, overflowY: 'auto', width: 240 }}
                        >
                            <TextArea
                                style={{ width: '100%',fontSize:14 }}
                                value={frame.asrText}
                                rows={10}
                                readOnly
                                placeholder="ASR 文本将在此处展示"
                            />
                        </Card>
                    </div>
                    </Col>

                    {/* 右卡：最多五张商品图片 */}
                    <Col >
                        <Card
                            title={
                                <span> TOP15 相似商品相关性标注 <span style={{ color: 'red' }}>（顺序已随机打乱）</span></span>
                            }
                            size="small"
                            style={{ height: 1300, overflowY: 'auto', width: 1000 }}
                        >
                            <div style={{ display: "flex", flexWrap: "wrap",gap: 8 }}>
                                {(frame.topSimilarItems || []).slice(0, 15).map((item, idx) => {
                                    const bestImgUrl = frame.topSimilarItemIds?.[idx]?.imgUrl;
                                    return (
                                        <div key={item.itemId || idx} style={{ width: "19%", marginBottom: 16,}}>
                                            <LabelGoodCardSmall
                                                ribbonText={`匹配分数 ${item?.score ? item.score.toFixed(4) : "-"}`}
                                                ribbonColor="orange"
                                                item={item}
                                                bestImgUrl = {bestImgUrl}
                                                style={{ width: 200, height: 200, overflow: "hidden" }}
                                            />
                                            <div style={{ marginTop: 6, textAlign: "center" }}>
                                                {/* 编辑模式 → 显示 Radio 选择 */}
                                                {editMode ? (
                                                    <Radio.Group
                                                        value={labels[item.itemId]}
                                                        onChange={e => handleSelectLabel(Number(item.itemId), e.target.value)}
                                                        optionType="default"
                                                        disabled={radioDisabled}
                                                    >
                                                        {Object.entries(RELEVANT_MAP)
                                                            .sort((a, b) => ["2","1","0"].indexOf(a[0]) - ["2","1","0"].indexOf(b[0]))
                                                            .map(([value, label]) => (
                                                            <Radio key={value} value={value} style={{ display: "flex", alignItems: "center", marginBottom: 4 }}>
                                                                <Tag color={TAG_COLOR_MAP[value]}>{label}</Tag>
                                                            </Radio>
                                                        ))}
                                                    </Radio.Group>
                                                ) : (
                                                    // 展示模式 → 只显示文字
                                                    <div style={{ marginTop: 8 }}>
                                                        <Tag color={TAG_COLOR_MAP[labels[item.itemId] ?? "0"]}>
                                                            {RELEVANT_MAP[labels[item.itemId] ?? ""]}
                                                        </Tag>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                            {/* 底部按钮：有标注 → 修改，无标注 → 提交 */}
                            <div style={{ textAlign: "right", marginTop: 20,marginRight:20 }}>
                                {!editMode && labelUserInfo ? (
                                    <div style={{ marginBottom: 10, fontSize: 14 , color: "grey", fontWeight: "bold"}}>
                                        <span>标注人：</span>
                                        <span>{labelUserInfo.labelUserName}</span>
                                        <span style={{ marginLeft: 20}}>标注时间：</span>
                                        <span>{labelUserInfo.labelTime}</span>
                                    </div>
                                ) : null}
                                {editMode ? (
                                    <Tooltip title={!allLabeled ? "请先将「所有图片」都标注完" : ""} placement="top">
                                        <span>
                                            <Button type="primary" disabled={!allLabeled} onClick={onSubmitClick} >
                                              提交
                                            </Button>
                                        </span>
                                    </Tooltip>
                                ) : (
                                    <Button type="default" onClick={() => {
                                        setEditMode(true); // 进入编辑模式
                                        setRadioDisabled(false); // 启用 Radio 按钮
                                    }}>
                                        修改
                                    </Button>
                                )}
                                {/* 不标注按钮 */}
                                <Tooltip title="抽帧质量太差，选择不标注" placement="top">
                                    <Button variant="outlined" color="danger" onClick={onNoLabelClick} style={{ marginLeft: 10 }}>
                                        不标注
                                    </Button>
                                </Tooltip>
                            </div>
                            {/* 相关性说明文字 */}
                            <div style={{ marginTop: 10, fontSize: 13, color: "#666", lineHeight: 0.8 }}>
                                <p><b>—— 强相关：</b>与视频帧视觉上一致，肉眼看基本符合，那么标注<strong>强相关</strong>。</p>
                                <p><b>—— 弱相关：</b>商品信息与视频帧信息相似，如都是上衣/裤子，且款式相近，但又不完全是，则标注<strong>弱相关</strong>。</p>
                                <p><b>—— 不相关：</b>商品信息和视频帧信息完全不一样，那么标注<strong>不相关</strong>。</p>

                            </div>
                        </Card>
                    </Col>
                </Row>
            ))}

            {/* 统一的视频播放Modal */}
            {reversedFrames.map((frame) => (
                <Modal
                    key={`modal-${frame.index}`}
                    title={`直播抽帧-${frame.index} 视频片段`}
                    open={openVideoModal[frame.index] || false}
                    footer={null}
                    onCancel={() => handleCloseVideoModal(frame.index)}
                    width={550}
                    bodyStyle={{ padding: 10, textAlign: "center", background: "#f5f5f5" }}
                    destroyOnClose
                >
                    <div  style={{
                        background: "#000",
                        borderRadius: 8,
                        width: 480,
                        height: 640,
                    }}>
                        <KwaiPlayerReact
                            ref={playerRef1}
                            src={currentVideoUrl}
                            controls
                            autoPlay={true}
                            preload="auto"
                            style={{
                                width: '100%',
                                height: '100%',
                            }}
                        />
                    </div>
                </Modal>
            ))}

            {/* JSON Modal，只展示被点击的那个 frame */}
            {jsonFrame && (
                <Modal
                    open={true}
                    width={800}
                    footer={null}
                    onCancel={() => setJsonFrame(null)}
                    bodyStyle={{ maxHeight: 650, overflowY: "auto" }}
                    title={
                        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                            <span>该帧原始数据</span>
                            <Link style={{ marginRight: 40 }}  onClick={handleCopyJson}>一键复制</Link>
                        </div>
                    }
                >
                    <Tree
                        treeData={jsonToTreeData(jsonFrame)}
                        defaultExpandAll
                        showLine
                    />
                </Modal>
            )}
        </div>
    );
};

export default LabelFrameListRowCard;
