import React, {useRef, useState} from "react";
import {Card, Col, Image, message, Modal, Row, Tooltip, Tree} from "antd";
import TextArea from "antd/es/input/TextArea";
import {MatchedGood} from "@/components/Cards/MatchedGoodsCard";
import GoodCardSmall from "@/components/Cards/GoodCardSmall";
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import {KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import Link from "antd/es/typography/Link";
import {formatMillSecTime} from "@/pages/AgentLive/constant";
import GoodCardAlgoSmall from "@/components/Cards/GoodCardAlgoSmall";
import {Frame} from "@/interfaces/Frame";


export interface windowTracInfo {
    windowTimestamp:number;
    windowRelatedTime:number;
    matchedGoodsAlgo: MatchedGood;
    agentRequest:any;
    agentResponse:any;
    frameTraceInfoList: Frame[];
}

interface WindowTraceInfoListRowCardProps {
    playerRef: React.RefObject<KwaiPlayer | null>;
    windowTracInfoList: windowTracInfo[];
    liveStreamId?: number; // 添加liveStreamId属性
}



const WindowTraceInfoListRowCard: React.FC<WindowTraceInfoListRowCardProps> = ({ windowTracInfoList ,playerRef}) => {


    // Ref：视频播放器
    const playerRef1 = useRef<KwaiPlayer | null>(null);
    // State：是否打开 JSON Modal
    // const [openVideoModal, setOpenVideoModal] = useState<{ [key: number]: boolean }>({});
    const [openVideoModal, setOpenVideoModal] = useState<Record<string, boolean>>({});
    // State：当前正在播放的视频 URL
    const [currentVideoUrl, setCurrentVideoUrl] = useState<string>('');
    // State：哪个 frame 的 JSON Modal 正在打开
    const [jsonFrame, setJsonFrame] = useState<Frame | null>(null);

    const handleJump = (time: number) => {
        if (playerRef.current) {
            console.log("handleJump",time);
            playerRef.current.currentTime = time;
        }
    };

    /**
     * 打开 抽帧视频 Modal 时，传入 index 和视频 URL
     * @param index
     * @param videoUrl
     */
    const handleOpenVideoModal = (traceIdx: number, frameIdx: number, videoUrl: string) => {
        const k = `${traceIdx}-${frameIdx}`;
        setOpenVideoModal(prev => ({ ...prev, [k]: true }));
        setCurrentVideoUrl(videoUrl.replace(/^http:/, 'https:'));
    };

    /**
     * 关闭 抽帧视频 Modal 时，传入 index
     * @param index
     */

    const handleCloseVideoModal = (traceIdx: number, frameIdx: number) => {
        const k = `${traceIdx}-${frameIdx}`;
        setOpenVideoModal(prev => ({ ...prev, [k]: false }));
        setCurrentVideoUrl('');
    };

    /**
     * 将 JSON 数据转换为 Tree 结构
     */
    const jsonToTreeData = (data: any, path = ""): any[] => {
        if (data === null || typeof data !== "object") {
            return [{title: `${path}: ${String(data)}`, key: path}];
        }
        if (Array.isArray(data)) {
            return data.map((item, idx) => ({
                title:
                    typeof item === "object"
                        ? `[${idx}]`
                        : `[${idx}]: ${String(item)}`,
                key: `${path}[${idx}]`,
                children:
                    typeof item === "object" && item !== null
                        ? jsonToTreeData(item, `${path}[${idx}]`)
                        : undefined,
            }));
        }
        return Object.entries(data).map(([k, v]) => ({
            title:
                typeof v === "object" && v !== null
                    ? k
                    : `${k}: ${String(v)}`,
            key: path ? `${path}.${k}` : k,
            children:
                typeof v === "object" && v !== null
                    ? jsonToTreeData(v, path ? `${path}.${k}` : k)
                    : undefined,
        }));
    };

    /**
     * 复制 JSON 数据到剪切板
     */
    const handleCopyJson = () => {
        if (!jsonFrame) return;
        const text = JSON.stringify(jsonFrame, null, 2);
        navigator.clipboard
            ?.writeText(text)
            .then(() => message.success("已复制 JSON 数据到剪切板"))
            .catch(() => {
                // 兼容老浏览器
                const textarea = document.createElement("textarea");
                textarea.value = text;
                textarea.style.position = "fixed";
                textarea.style.opacity = "0";
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                document.body.removeChild(textarea);
                message.success("已复制 JSON 数据到剪切板");
            });
    };

    return (
        <div>
            {windowTracInfoList.map((traceInfo, traceIdx) => {
                const reversedFrames = Array.isArray(traceInfo.frameTraceInfoList)
                    ? traceInfo.frameTraceInfoList.slice().reverse()
                    : [];
                return reversedFrames.map((frame) => (
                    <Row key={`${traceIdx}-${frame.index}`} gutter={[2, 2]} style={{marginBottom: 2}}>
                        {/* 新增：最左边展示 matchedGoodsAlgo */}
                        <Col>
                            <Card
                                title={
                                    <>
                                        {/*<Tooltip title="点击定位">*/}
                                        {/*    <a onClick={() => handleJump((traceInfo.windowRelatedTime - 10000)/1000)}>*/}
                                        {/*        {formatMillSecTime(traceInfo.windowRelatedTime - 10000)}*/}
                                        {/*    </a>*/}
                                        {/*</Tooltip>-*/}
                                        <span style={{ fontSize: 10, color: '#666', marginRight:4}}>
                                            {traceInfo.windowTimestamp}
                                        </span>
                                        <Tooltip title="点击定位">
                                            <a onClick={() => handleJump((traceInfo.windowRelatedTime/1000))} style={{ fontSize: 12}}>
                                                {formatMillSecTime(traceInfo.windowRelatedTime)}
                                            </a>
                                        </Tooltip>
                                    </>
                                }
                                size="small"
                                style={{ height: 310, width:180}}
                            >
                                {Array.isArray(traceInfo.matchedGoodsAlgo) && traceInfo.matchedGoodsAlgo.length > 0 ? (
                                    <GoodCardAlgoSmall
                                        ribbonText={
                                            traceInfo.matchedGoodsAlgo?.[0]?.score
                                                ? `匹配分数 ${traceInfo.matchedGoodsAlgo[0].score.toFixed(4)}`
                                                : ''
                                        }
                                        item={traceInfo.matchedGoodsAlgo[0]}
                                        agentRequest={traceInfo.agentRequest}
                                        agentResponse={traceInfo.agentResponse}
                                        style={{ width: 150, height: 250, overflow: 'hidden' }}
                                    />
                                ) : (
                                    <div style={{
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#999'
                                    }}>
                                        暂无识别商品
                                    </div>
                                )}
                            </Card>
                        </Col>

                        {/* 左卡：图片 */}
                        <Col>
                            <Card
                                title={`直播抽帧`}
                                size="small"
                                style={{width: 160, height: 310, padding: 0}}
                                styles={{ body: { padding: 0 } }}
                            >
                                <div style={{
                                    width: "100%",
                                    height: 200,
                                    overflow: "hidden",
                                    marginBottom: 6,
                                    marginTop: 6,
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center"
                                }}>
                                    <Image
                                        src={frame.screenSnapshotUrls[0]}
                                        alt="直播抽帧"
                                        width={160}
                                        height={200}
                                        style={{objectFit: 'contain'}}
                                    />
                                </div>
                                <div
                                    style={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        padding: "15px",
                                        borderTop: '1px dashed #eee'
                                    }}
                                >
                                    <Link onClick={() => handleOpenVideoModal(traceIdx, frame.index, frame.clipVideoUrl)}>
                                        抽帧视频
                                    </Link>
                                    <Link onClick={() => setJsonFrame(frame)}>JSON 数据</Link>
                                </div>
                            </Card>
                        </Col>

                        {/* 中卡：ASR */}
                        <Col>
                            <Card
                                size="small"
                                title={<span style={{fontSize: '14px', fontWeight: 'bold'}}>ASR 文本</span>}
                                style={{height: 310, overflowY: 'auto', width: 160}}
                            >
                                <TextArea
                                    style={{width: '100%',fontSize:12}}
                                    value={frame.asrText}
                                    rows={12}
                                    readOnly
                                    placeholder="ASR 文本将在此处展示"
                                />
                            </Card>
                        </Col>

                        {/* 右卡：最多五张商品图片 */}
                        <Col>
                            <Card
                                title="TOP5 匹配相似商品"
                                size="small"
                                style={{height: 310, overflowY: 'auto', width:750}}
                            >
                                <div style={{display: "flex", gap: 2}}>
                                    {(frame.topSimilarItems || []).slice(0, 5).map((item, idx) => (
                                        <GoodCardSmall
                                            key={idx}
                                            ribbonText={`匹配分数 ${item?.score ? item.score.toFixed(4) : '-'}`}
                                            ribbonColor="orange"
                                            item={item}
                                            style={{width: 120, height: 200, overflow: 'hidden'}}
                                        />
                                    ))}
                                </div>
                            </Card>
                        </Col>
                    </Row>
                ));
            })}

            {/* 视频 Modal */}
            {windowTracInfoList.flatMap((traceInfo, traceIdx) =>
                Array.isArray(traceInfo.frameTraceInfoList)
                    ? traceInfo.frameTraceInfoList.map(frame => (
                        <Modal
                            key={`modal-${traceIdx}-${frame.index}-${frame.screenSnapshotTimeStamp}`}
                            title={`直播抽帧-${frame.index} 视频片段`}
                            open={openVideoModal[`${traceIdx}-${frame.index}`] || false}
                            onCancel={() => handleCloseVideoModal(traceIdx, frame.index)}
                            footer={null}
                            width={550}
                            styles={{ body: { padding: 10, textAlign: "center", background: "#f5f5f5" } }}
                            destroyOnClose
                        >
                            <div
                                style={{
                                    background: "#000",
                                    borderRadius: 8,
                                    width: 480,
                                    height: 640
                                }}
                            >
                                <KwaiPlayerReact
                                    ref={playerRef1}
                                    src={currentVideoUrl}
                                    controls
                                    autoPlay={true}
                                    preload="auto"
                                    style={{ width: "100%", height: "100%" }}
                                />
                            </div>
                        </Modal>
                    ))
                    : []
            )}

            {/* JSON Modal */}
            {jsonFrame && (
                <Modal
                    mask={false}
                    open={true}
                    width={800}
                    footer={null}
                    onCancel={() => setJsonFrame(null)}
                    bodyStyle={{maxHeight: 650, overflowY: "auto"}}
                    title={
                        <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                            <span>该帧原始数据</span>
                            <Link style={{marginRight: 40}} onClick={handleCopyJson}>一键复制</Link>
                        </div>
                    }
                >
                    <Tree
                        treeData={jsonToTreeData(jsonFrame)}
                        defaultExpandAll
                        showLine
                    />
                </Modal>
            )}
        </div>
    );
};
export default WindowTraceInfoListRowCard;
