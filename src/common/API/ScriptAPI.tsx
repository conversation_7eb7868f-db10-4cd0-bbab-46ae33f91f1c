export const ScriptDebug = async (params: any) => async (params: any) => {
    const response = await fetch('/gateway/live/control/common/api/invoke', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            api: "api/script/debug",
            param: JSON.stringify(params)
        }),
    });
    const result = await response.json();
    console.log("params:", params)
    console.log("ScriptDebug：", result);
    return JSON.parse(result.data);
}