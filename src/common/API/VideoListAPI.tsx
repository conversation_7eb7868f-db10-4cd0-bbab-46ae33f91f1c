import { jsonStringify } from 'safe-json-parse-and-stringify';

// 获取视频列表
//查询标注列表记录
export const LabelListQuery = async (params: any) => {
  console.log('params:', params);
  const response = await fetch('gateway/kwaishop/slice/query', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: '/slice/label/list/query',
      param: jsonStringify(params),
    }),
  });
  const result = await response.json();
  console.log('---queryList:', result);
  return result;
};
// 删除视频
export const DeleteVideo = async (params: { id: string }) => {
  console.log('DeleteVideo params:', params);
  const response = await fetch('gateway/kwaishop/slice/mutation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: '/slice/video/delete',
      param: jsonStringify(params),
    }),
  });
  const result = await response.json();
  console.log('---DeleteVideo:', result);
  return result;
};

// 批量操作
export const BatchOperation = async (params: {
  action: 'delete' | 'update_status';
  ids: string[];
  status?: number;
}) => {
  console.log('BatchOperation params:', params);
  const response = await fetch('gateway/kwaishop/slice/mutation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: jsonStringify({
      api: '/slice/video/batch/operation',
      param: jsonStringify(params),
    }),
  });
  const result = await response.json();
  console.log('---BatchOperation:', result);
  return result;
};
