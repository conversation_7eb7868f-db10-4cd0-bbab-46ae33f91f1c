// @ts-nocheck
// scheduleList
export const scheduleList = async (params) => {
  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    // 将对象转换为 JSON 字符串
    body: JSON.stringify(
      {
        api: "api/team/schedule/list",
        param: JSON.stringify(params)
      })
  });
  const result = await response.json();
  console.log("scheduleList:", result);
  return JSON.parse(result.data);

  // return [{"id":46,"key":"46","teamGroup":"xhc","category":"稳定性&架构","shortName":"小黄车耗时监控-V2","detail":"[{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/31549/detail?currentEnv=prod&amp;activeTab=2\",\"type\":\"监控\",\"title\":\"新版耗时监控\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["魏裕彬"],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":47,"key":"47","teamGroup":"xhc","category":"稳定性&架构","shortName":"AI试衣的直播间下值不正确","detail":"[{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/aiTryClothesPolluteConfigSwitch\",\"type\":\"Kconf\",\"title\":\"aiTryClothesPolluteConfigSwitch\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/aiTryClothesBottomBackgroundConfig\",\"type\":\"Kconf\",\"title\":\"aiTryClothesBottomBackgroundConfig\"},{\"link\":\"https://kswitch.corp.kuaishou.com/#/flag/targeting?key=liveCommodityCardCommonConfig&project=merchant&environment=production\",\"type\":\"Kswitch\",\"title\":\"liveCommodityCardCommonConfig\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/24238/detail?currentEnv=prod&activeTab=11\",\"type\":\"监控\"}]","teamLink":"https://team.corp.kuaishou.com/task/B2211220","teamTitle":"【PreOnline流量回放】看讲解和求讲解按钮的颜色和宽度在非AI试衣的直播间下值不正确","teamInfo":{"id":8834233,"taskId":"B2211220","title":"【PreOnline流量回放】看讲解和求讲解按钮的颜色和宽度在非AI试衣的直播间下值不正确","taskTypeId":3455,"taskTypeName":"缺陷","taskClassId":3754,"taskClassName":"缺陷","sectionName":"流量回放召回问题","sectionId":"S133393","statusPhase":"END","projectId":"P1063","taskTypeGroup":"bug","createdAt":"raofugui","createdName":"饶福贵","teamStatusLog":null,"执行人":"张德伟","任务状态":"延后解决"},"owner":["管盛灵"],"schedule":{"dev":{"startDate":"2025-04-17 00:00:00.0","endDate":"2025-04-18 23:59:59.0"},"test":{"startDate":"2025-04-24 00:00:00.0","endDate":"2025-04-25 23:59:59.0"},"publish":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-13 23:59:59.0"},"gray":{"startDate":"2025-05-13 00:00:00.0","endDate":"2025-05-14 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://ksurl.cn/QHjiqNxI","kimGroups":null,"extraTime":[],"status":"放量中"},{"id":67,"key":"67","teamGroup":"xhc","category":"稳定性&架构","shortName":"平台标签-监控","detail":null,"teamLink":"","teamTitle":null,"teamInfo":null,"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":71,"key":"71","teamGroup":"xhc","category":"稳定性&架构","shortName":"主播维度开关","detail":null,"teamLink":"","teamTitle":null,"teamInfo":null,"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":72,"key":"72","teamGroup":"xhc","category":"稳定性&架构","shortName":"流量回放-traceTag校验","detail":null,"teamLink":"","teamTitle":null,"teamInfo":null,"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":119,"key":"119","teamGroup":"xhc","category":"小黄车","shortName":"银行直连-信用卡分期支付","detail":"[{\"type\":\"PRD\",\"link\":\"https://docs.corp.kuaishou.com/k/home/<USER>/fcACgz_HnoANPih2DlDI6Xk_F\",\"title\":\"【收银台】新增接入直连建行卡分期\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["管盛灵"],"schedule":null,"scheduleNote":"PD资源进展，暂缓详评","codeReviewLink":null,"kimGroups":[{"value":5676095827781960,"label":"建行卡分期","avatar":"https://static.yximgs.com/bs2/kimAvatar/202f7bb2b25445ae97463b453a3d5f84?type=create","kimLink":"kim://thread?type=4&id=5676095827781960"}],"extraTime":[],"status":"待详评"},{"id":122,"key":"122","teamGroup":"xhc","category":"稳定性&架构","shortName":"日志白名单优化printVisitor","detail":null,"teamLink":"","teamTitle":null,"teamInfo":null,"owner":["魏裕彬"],"schedule":{"dev":{"startDate":"2025-04-17 00:00:00.0","endDate":"2025-04-18 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"开发中"},{"id":123,"key":"123","teamGroup":"xhc","category":"小黄车","shortName":"发货时效BC全链路表达优化","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"【结算率专项】履约时效(发货/送达)BC全链路表达优化\"},{\"link\":\"https://docs.corp.kuaishou.com/s/home/<USER>",\"type\":\"PRD\",\"title\":\"排期表「结算率专项」履约时效BC表达\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLive/shipEfficiencyEnhanceCode\",\"type\":\"Kconf\",\"title\":\"shipEfficiencyEnhanceCode\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/31549/detail?currentEnv=prod&activeTab=2\",\"type\":\"监控\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/8977/detail?a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E3%80%8C%E5%BC%80%E6%92%AD+/+%E5%B0%8F%E9%BB%84%E8%BD%A6%E7%83%AD%E7%82%B9%E3%80%8D%E4%BA%8B%E4%BB%B6=0&a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E5%85%B3%E6%92%AD=0&activeTab=9&currentEnv=prod&viewPanel=5272825080&changeEvent=true\",\"type\":\"监控\"}]","teamLink":"https://team.corp.kuaishou.com/task/T8746636","teamTitle":"直播间小黄车发货时效强化表达","teamInfo":{"id":10757964,"taskId":"T8746636","title":"直播间小黄车发货时效强化表达","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"直播间","sectionId":"S46005","statusPhase":"END","projectId":"P1063","taskTypeGroup":"demand","createdAt":"linyu06","createdName":"林瑜","teamStatusLog":null,"提需组织":"B端产品","是否需要AB测试":"需要AB测试","开发负责人":"管盛灵","实际开发开始时间":"2025-05-06 15:11:35","提需人":"邓一然","执行人":"林瑜","任务状态":"已发布"},"owner":["管盛灵"],"schedule":{"dev":{"startDate":"2025-04-17 00:00:00.0","endDate":"2025-04-17 23:59:59.0"},"integration":{"startDate":"2025-04-18 00:00:00.0","endDate":"2025-04-18 23:59:59.0"},"test":{"startDate":"2025-04-29 00:00:00.0","endDate":"2025-05-07 23:59:59.0"},"publish":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-08 23:59:59.0"},"gray":{"startDate":"2025-05-10 00:00:00.0","endDate":"2025-05-13 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://ksurl.cn/lpLOKSHe","kimGroups":[{"value":5715067458003266,"label":"「已详评」发货时效BC全链路表达优化","avatar":"https://ali-kim-file.static.yximgs.com/bs2/kimPic/fa4owq1l1sojb8qk7y1xbn59gbffg34ds672qb4mcl1cdyg7f3sa9ky.","kimLink":"kim://thread?type=4&id=5715067458003266"}],"extraTime":[],"status":"已放量"},{"id":124,"key":"124","teamGroup":"xhc","category":"稳定性&架构","shortName":"小黄车入口日志-接入端到端sdk","detail":null,"teamLink":"","teamTitle":null,"teamInfo":null,"owner":["叶梦萦"],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":125,"key":"125","teamGroup":"xhc","category":"稳定性&架构","shortName":"Finops治理","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"其他\",\"title\":\"Finops健康分治理\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["魏裕彬"],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":127,"key":"127","teamGroup":"xhc","category":"小黄车","shortName":"店铺直播元素拓展深度","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"店铺直播元素拓展深度（直播卡拯救计划二期）\"},{\"link\":\"https://docs.corp.kuaishou.com/k/home/<USER>/fcACUwIqp1yPIqLGrI6C91Tdv?ro=false\",\"type\":\"Doc\",\"title\":\"电商直播广场链接接口文档\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"技术方案\",\"title\":\"直播内流上下滑接口链路梳理\"}]","teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"店铺直播元素拓展深度（直播卡拯救计划二期）","teamInfo":{"id":10639134,"taskId":"********","title":"店铺直播元素拓展深度（直播卡拯救计划二期）","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"数据工厂天河开发","sectionId":"S216396","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"zeshenghao","createdName":"迮圣皓","teamStatusLog":{"toStatusId":"17864","toStatusName":"开发中/技术方案中","flowedAt":"2025-04-21 14:54:20","flowdBy":"system","fromStatusName":"待开发","fromStatusId":"17863"},"提需组织":"商城产品","是否需要AB测试":"需要AB测试","开发负责人":"曾连杰","实际开发开始时间":"2025-04-17 11:17:08","提需人":"邬佳锜","执行人":"迮圣皓","任务状态":"待详评"},"owner":["刘泉晟"],"schedule":{"dev":{"startDate":"2025-04-21 00:00:00.0","endDate":"2025-04-22 23:59:59.0"},"integration":{"startDate":"2025-04-23 00:00:00.0","endDate":"2025-04-23 23:59:59.0"},"test":{"startDate":"2025-04-24 00:00:00.0","endDate":"2025-05-06 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5789838923502681,"label":"店铺直播元素拓展深度（直播卡拯救计划二期） - ********","avatar":"https://static.yximgs.com/bs2/kimAvatar/139d48832a294fd2bf5660168a865186?type=default","kimLink":"kim://thread?type=4&id=5789838923502681"}],"extraTime":[],"status":"测试中"},{"id":133,"key":"133","teamGroup":"xhc","category":"稳定性&架构","shortName":"自动化演练中台","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"技术方案\",\"title\":\"自动故障演练一期技术方案\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["管盛灵"],"schedule":{"dev":{"startDate":"2025-04-14 00:00:00.0","endDate":"2025-04-17 23:59:59.0"},"integration":{"startDate":"2025-04-18 00:00:00.0","endDate":"2025-04-23 23:59:59.0"},"test":{"startDate":"2025-04-25 00:00:00.0","endDate":"2025-04-29 23:59:59.0"},"publish":{"startDate":"2025-05-29 00:00:00.0","endDate":"2025-05-30 23:59:59.0"}},"scheduleNote":"QA负责开发的部分进展缓慢","codeReviewLink":null,"kimGroups":[{"value":5718313756037445,"label":"故障演练自动化-营销群","avatar":"https://ali-kim-file.static.yximgs.com/bs2/kimPic/25nfkro4p2zcibj4hj11evgbdl83axqek7rxccggik1xzyuzaftuyp.","kimLink":"kim://thread?type=4&id=5718313756037445"}],"extraTime":[],"status":"测试中"},{"id":140,"key":"140","teamGroup":"xhc","category":"稳定性&架构","shortName":"小黄车成本优化-字段回迁","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"提测文档\",\"title\":\"测试范围\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"技术方案\",\"title\":\"小黄车营销报价改动\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"技术方案\",\"title\":\"小黄车氛围中心依赖字段梳理\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["闫浩"],"schedule":{"dev":{"startDate":"2025-04-16 00:00:00.0","endDate":"2025-04-22 23:59:59.0"},"integration":{"startDate":"2025-04-23 00:00:00.0","endDate":"2025-04-23 23:59:59.0"},"test":{"startDate":"2025-04-24 00:00:00.0","endDate":"2025-04-30 23:59:59.0"},"publish":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-08 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"已发布"},{"id":141,"key":"141","teamGroup":"xhc","category":"小黄车","shortName":"直播好友推荐","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"【PRD】电商好友推荐--直播间&amp;讲解回放场景\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLive/sellingPointTypeOrderConfig\",\"type\":\"Kconf\",\"title\":\"sellingPointTypeOrderConfig\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLive/sellingPointTextPayloadTemplate\",\"type\":\"Kconf\",\"title\":\"sellingPointTextPayloadTemplate\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLive/flowLiveBarrageConfig\",\"type\":\"Kconf\",\"title\":\"flowLiveBarrageConfig\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/friendRecommendConfigTemplate\",\"type\":\"Kconf\",\"title\":\"friendRecommendConfigTemplate\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/insideLiveStreamSceneSet\",\"type\":\"Kconf\",\"title\":\"insideLiveStreamSceneSet\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/flowLiveRecommendSwitch\",\"type\":\"Kconf\",\"title\":\"flowLiveRecommendSwitch\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/8977/detail?a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E3%80%8C%E5%BC%80%E6%92%AD+/+%E5%B0%8F%E9%BB%84%E8%BD%A6%E7%83%AD%E7%82%B9%E3%80%8D%E4%BA%8B%E4%BB%B6=0&a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E5%85%B3%E6%92%AD=0&activeTab=9&currentEnv=prod&viewPanel=5975513784&changeEvent=true\",\"type\":\"监控\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/8977/detail?a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E3%80%8C%E5%BC%80%E6%92%AD+/+%E5%B0%8F%E9%BB%84%E8%BD%A6%E7%83%AD%E7%82%B9%E3%80%8D%E4%BA%8B%E4%BB%B6=0&a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E5%85%B3%E6%92%AD=0&activeTab=9&currentEnv=prod&viewPanel=5272825080&changeEvent=true\",\"type\":\"监控\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/24991/detail?fromShare=1&viewPanel=8355133483&dashboardShowAlarm=false\",\"type\":\"监控\"}]","teamLink":"https://team.corp.kuaishou.com/task/T8746123","teamTitle":"【PRD】电商好友推荐--直播间&讲解回放场景-讲解回放","teamInfo":{"id":10757286,"taskId":"T8746123","title":"【PRD】电商好友推荐--直播间&讲解回放场景-讲解回放","taskTypeId":0,"taskTypeName":"Server子任务","taskClassId":41370,"taskClassName":"Server子任务","sectionName":"社区电商","sectionId":"S111694","statusPhase":"END","projectId":"P1063","taskTypeGroup":"child_task","createdAt":"liuquansheng","createdName":"刘泉晟","teamStatusLog":null,"开发负责人":"刘泉晟","执行人":"刘泉晟","任务状态":"已上线"},"owner":["闫浩","刘泉晟"],"schedule":{"dev":{"startDate":"2025-04-21 00:00:00.0","endDate":"2025-04-23 23:59:59.0"},"integration":{"startDate":"2025-04-24 00:00:00.0","endDate":"2025-04-25 23:59:59.0"},"test":{"startDate":"2025-04-27 00:00:00.0","endDate":"2025-05-06 23:59:59.0"},"publish":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-08 23:59:59.0"},"gray":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-12 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/merge_requests?mrId=2292&repoId=26229&tabKey=baseInfo","kimGroups":[{"value":5769720507241807,"label":"内部-直播好友推荐","avatar":"https://static.yximgs.com/bs2/kimAvatar/ba4357be1a214ef195dc9aff1cfff44b?type=create","kimLink":"kim://thread?type=4&id=5769720507241807"}],"extraTime":[],"status":"已放量"},{"id":147,"key":"147","teamGroup":"xhc","category":"小黄车","shortName":"618互动玩法","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"618互动玩法\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/shoppingGoldPendentSwitch\",\"type\":\"Kconf\",\"title\":\"shoppingGoldPendentSwitch\"}]","teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"直播间 618 互动玩法","teamInfo":{"id":10678415,"taskId":"********","title":"直播间 618 互动玩法","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"社区电商","sectionId":"S111694","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"fankequan","createdName":"范可泉","teamStatusLog":{"toStatusId":"17864","toStatusName":"开发中/技术方案中","flowedAt":"2025-05-09 14:11:07","flowdBy":"system","fromStatusName":"待开发","fromStatusId":"17863"},"提需组织":"C端产品-营销","是否需要AB测试":"需要AB测试","开发负责人":"闫浩","实际开发开始时间":"2025-05-09 14:11:08","提需人":"吴靓","执行人":"范可泉","任务状态":"发布中"},"owner":["闫浩","祁汉仕"],"schedule":{"dev":{"startDate":"2025-04-23 00:00:00.0","endDate":"2025-04-25 23:59:59.0"},"integration":{"startDate":"2025-05-06 00:00:00.0","endDate":"2025-05-08 23:59:59.0"},"test":{"startDate":"2025-05-09 00:00:00.0","endDate":"2025-05-13 23:59:59.0"},"publish":{"startDate":"2025-05-14 00:00:00.0","endDate":"2025-05-15 23:59:59.0"},"gray":{"startDate":"2025-05-16 00:00:00.0","endDate":"2025-05-16 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5772186584191015,"label":"618互动玩法","avatar":"https://static.yximgs.com/bs2/kimAvatar/b4126ed093e64f51bebf94925c39f3ee?type=create","kimLink":"kim://thread?type=4&id=5772186584191015"}],"extraTime":["2025-05-11"],"status":"发布中"},{"id":150,"key":"150","teamGroup":"xhc","category":"小黄车","shortName":"直播间设计大基建需求群","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"设计大基建-小黄车改色\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"提测文档\",\"title\":\"设计大基建提测模版 （含开关）\"}]","teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"设计大基建组件接入","teamInfo":{"id":10766213,"taskId":"********","title":"设计大基建组件接入","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"直播间","sectionId":"S46005","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"zeshenghao","createdName":"迮圣皓","teamStatusLog":{"toStatusId":"17864","toStatusName":"开发中/技术方案中","flowedAt":"2025-05-14 16:39:39","flowdBy":"system","fromStatusName":"待测试/开发完成","fromStatusId":"17865"},"提需组织":"设计","是否需要AB测试":"需要AB测试","开发负责人":"韩显明","实际开发开始时间":"2025-04-22 16:27:35","提需人":"丁洁","执行人":"迮圣皓","任务状态":"开发中/技术方案中"},"owner":["叶梦萦"],"schedule":{"dev":{"startDate":"2025-04-23 00:00:00.0","endDate":"2025-04-25 23:59:59.0"},"integration":{"startDate":"2025-05-06 00:00:00.0","endDate":"2025-05-07 23:59:59.0"},"test":{"startDate":"2025-05-13 00:00:00.0","endDate":"2025-05-16 23:59:59.0"},"publish":{"startDate":"2025-05-19 00:00:00.0","endDate":"2025-05-20 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5797202834073904,"label":"直播间设计大基建需求群","avatar":"https://static.yximgs.com/bs2/kimAvatar/de152ef0ca4c4a31aa260a93d5fcbbaa?type=create","kimLink":"kim://thread?type=4&id=5797202834073904"}],"extraTime":[],"status":"测试中"},{"id":151,"key":"151","teamGroup":"xhc","category":"小黄车","shortName":"618大促混资膨胀券","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/s/home/<USER>",\"type\":\"PRD\",\"title\":\"商家膨胀券排期文档\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"【PRD】618大促混资膨胀券-WIP\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/inflateCouponSwitch\",\"type\":\"Kconf\",\"title\":\"inflateCouponSwitch\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"技术方案\",\"title\":\"小黄车券楼层膨胀券\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/8977/detail?a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E3%80%8C%E5%BC%80%E6%92%AD+/+%E5%B0%8F%E9%BB%84%E8%BD%A6%E7%83%AD%E7%82%B9%E3%80%8D%E4%BA%8B%E4%BB%B6=0&a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E5%85%B3%E6%92%AD=0&activeTab=9&currentEnv=prod&fromShare=1&viewPanel=4417011091&dashboardShowAlarm=false\",\"type\":\"监控\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"提测文档\",\"title\":\"商家膨胀券测试手册\"}]","teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"618 大促混资膨胀券","teamInfo":{"id":10818385,"taskId":"********","title":"618 大促混资膨胀券","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"社区电商","sectionId":"S111694","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"fankequan","createdName":"范可泉","teamStatusLog":{"toStatusId":"17864","toStatusName":"开发中/技术方案中","flowedAt":"2025-05-12 19:58:06","flowdBy":"system","fromStatusName":"已排资源","fromStatusId":"17858"},"提需组织":"C端产品-营销","是否需要AB测试":"需要AB测试","开发负责人":"刘泉晟","实际开发开始时间":"2025-05-12 19:58:06","提需人":"王欣萍","执行人":"范可泉","任务状态":"发布中"},"owner":["刘泉晟"],"schedule":{"dev":{"startDate":"2025-04-27 00:00:00.0","endDate":"2025-04-29 23:59:59.0"},"integration":{"startDate":"2025-04-30 00:00:00.0","endDate":"2025-05-06 23:59:59.0"},"test":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-10 23:59:59.0"},"publish":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-13 23:59:59.0"},"gray":{"startDate":"2025-05-14 00:00:00.0","endDate":"2025-05-15 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/merge_requests?mrId=2302&repoId=26229","kimGroups":[{"value":5814332692403254,"label":"【需求群】直播间 618 膨胀券玩法","avatar":"https://static.yximgs.com/bs2/kimAvatar/cda36913ad024b68a2be05a3db169b10?type=create","kimLink":"kim://thread?type=4&id=5814332692403254"},{"value":5773157876082763,"label":"618混资膨胀券方案沟通","avatar":"https://static.yximgs.com/bs2/kimAvatar/703e11dfc1b942978b43f04aeb26a1b9?type=create","kimLink":"kim://thread?type=4&id=5773157876082763"}],"extraTime":["2025-05-10"],"status":"放量中"},{"id":152,"key":"152","teamGroup":"xhc","category":"小黄车","shortName":"国补专项-直播间","detail":"[{\"link\":\"https://kconf.corp.kuaishou.com/#/_serviceConfig/kwsConfig?ksn=kwaishop-flow-live-service&key=platecoDev.kwaishopFlowLiveService.stateSubsidyStyleSwitch\",\"type\":\"Kconf\",\"title\":\"kwsConfig\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"提测文档\",\"title\":\"国补需求提测文档\"},{\"link\":\"https://docs.corp.kuaishou.com/k/home/<USER>/fcABSuQ2vMbytKX3fN-A9CJch\",\"type\":\"PRD\",\"title\":null},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/8977/detail?a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E3%80%8C%E5%BC%80%E6%92%AD+/+%E5%B0%8F%E9%BB%84%E8%BD%A6%E7%83%AD%E7%82%B9%E3%80%8D%E4%BA%8B%E4%BB%B6=0&a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E5%85%B3%E6%92%AD=0&activeTab=9&currentEnv=prod&viewPanel=1822698067&changeEvent=true\",\"type\":\"监控\"}]","teamLink":"https://team.corp.kuaishou.com/task/T8745097","teamTitle":"国补专项-直播间","teamInfo":{"id":10755911,"taskId":"T8745097","title":"国补专项-直播间","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"直播间","sectionId":"S46005","statusPhase":"END","projectId":"P1063","taskTypeGroup":"demand","createdAt":"zeshenghao","createdName":"迮圣皓","teamStatusLog":null,"提需组织":"行业","是否需要AB测试":"需要AB测试","开发负责人":"叶梦萦","实际开发开始时间":"2025-04-27 14:09:09","提需人":"秦时月","执行人":"迮圣皓","任务状态":"已发布"},"owner":["叶梦萦"],"schedule":{"dev":{"startDate":"2025-04-25 00:00:00.0","endDate":"2025-04-29 23:59:59.0"},"integration":{"startDate":"2025-04-30 00:00:00.0","endDate":"2025-05-06 23:59:59.0"},"test":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-09 23:59:59.0"},"publish":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-13 23:59:59.0"},"gray":{"startDate":"2025-05-14 00:00:00.0","endDate":"2025-05-14 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/merge_requests?mrId=2300&repoId=26229","kimGroups":[{"value":5811415451510860,"label":"国补专项-直播间 - T8647257","avatar":"https://static.yximgs.com/bs2/kimAvatar/b009d62e7f4943c7964370dd2e784305?type=default","kimLink":"kim://thread?type=4&id=5811415451510860"}],"extraTime":[],"status":"放量中"},{"id":153,"key":"153","teamGroup":"xhc","category":"小黄车","shortName":"小黄车列表底部商品推荐（二期）","detail":"[{\"type\":\"PRD\",\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"title\":\"PRD-直播电商-小黄车列表底部商品推荐（二期）\"}]","teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"小黄车列表底部商品推荐（二期）","teamInfo":{"id":10528409,"taskId":"********","title":"小黄车列表底部商品推荐（二期）","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"社区电商","sectionId":"S111694","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"zhangke17","createdName":"张珂","teamStatusLog":null,"提需组织":"C端产品-用户","是否需要AB测试":"需要AB测试","开发负责人":null,"实际开发开始时间":null,"提需人":"张珂","执行人":"张珂","任务状态":"待详评"},"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":156,"key":"156","teamGroup":"xhc","category":"小黄车","shortName":"风控-直播间小黄车老版本接口申请下线","detail":null,"teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"【风控】直播间小黄车老版本接口申请下线","teamInfo":{"id":10641950,"taskId":"********","title":"【风控】直播间小黄车老版本接口申请下线","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"直播间","sectionId":"S46005","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"linyu06","createdName":"林瑜","teamStatusLog":null,"提需组织":"治理业务","是否需要AB测试":"不需要AB测试","开发负责人":null,"实际开发开始时间":null,"提需人":"石崇文","执行人":"林瑜","任务状态":"待详评"},"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":158,"key":"158","teamGroup":"xhc","category":"小黄车","shortName":"单单返链路优化","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"【PRD】单单返下单链路氛围表达\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"[技术方案] 单单返\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/orderRebateAggregationCouponSwitch\",\"type\":\"Kconf\",\"title\":\"orderRebateAggregationCouponSwitch\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/aggregationCouponDiffSwitch\",\"type\":\"Kconf\",\"title\":\"aggregationCouponDiffSwitch\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"「提测文档」单单返券618迭代二期\"},{\"type\":\"PRD\"}]","teamLink":"https://team.corp.kuaishou.com/task/T8689859","teamTitle":"【618】单单返链路优化","teamInfo":{"id":10690954,"taskId":"T8689859","title":"【618】单单返链路优化","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"用增营销产品","sectionId":"S209243","statusPhase":"END","projectId":"P1063","taskTypeGroup":"demand","createdAt":"yanhaotian03","createdName":"颜浩天","teamStatusLog":null,"提需组织":"C端产品-营销","是否需要AB测试":"需要AB测试","开发负责人":"严文博","实际开发开始时间":"2025-04-27 10:33:55","提需人":"颜浩天","执行人":"严文博","任务状态":"已发布"},"owner":["刘泉晟"],"schedule":{"dev":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-09 23:59:59.0"},"integration":{"startDate":"2025-05-11 00:00:00.0","endDate":"2025-05-12 23:59:59.0"},"test":{"startDate":"2025-05-13 00:00:00.0","endDate":"2025-05-14 23:59:59.0"},"publish":{"startDate":"2025-05-14 00:00:00.0","endDate":"2025-05-15 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/merge_requests?mrId=2304&repoId=26229&tabKey=baseInfo","kimGroups":[{"value":5854223041958930,"label":"【测试中】 单单返接入凑单","avatar":"https://static.yximgs.com/bs2/kimAvatar/f22e988479f94b4abeaf25064898b120?type=create","kimLink":"kim://thread?type=4&id=5854223041958930"},{"value":5868544223547669,"label":"25-618-单单返迭代内测/公测沟通群","avatar":"https://static.yximgs.com/bs2/kimAvatar/f267b6d221994627924eb93dd8d40b62?type=create","kimLink":"kim://thread?type=4&id=5868544223547669"},{"value":5789436327831867,"label":"单单返618迭代","avatar":"https://static.yximgs.com/bs2/kimAvatar/d60d5e7a5e154b6195b316301d6d5ad4?type=create","kimLink":"kim://thread?type=4&id=5789436327831867"},{"value":5865767868470598,"label":"【压测群】618单单返券活动投放","avatar":"https://static.yximgs.com/bs2/kimAvatar/c23b2dfcba674acb8752595cc309ba80?type=create","kimLink":"kim://thread?type=4&id=5865767868470598"}],"extraTime":["2025-05-11"],"status":"发布中"},{"id":159,"key":"159","teamGroup":"xhc","category":"小黄车","shortName":"主播卖点加违规词校验","detail":"[{\"type\":\"PRD\",\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"title\":\"【待开发】主播卖点设置加违规引流校验需求\"}]","teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"主播卖点设置加违规校验需求","teamInfo":{"id":10777132,"taskId":"********","title":"主播卖点设置加违规校验需求","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"跟播助手","sectionId":"S78016","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"mayuanyuan","createdName":"马媛媛","teamStatusLog":{"toStatusId":"17864","toStatusName":"开发中/技术方案中","flowedAt":"2025-05-14 10:49:16","flowdBy":"KDev","fromStatusName":"已排资源","fromStatusId":"17858"},"提需组织":"C端产品-用户","是否需要AB测试":"不需要AB测试","开发负责人":"赵子涵","实际开发开始时间":"2025-05-14 10:49:16","提需人":"马媛媛","执行人":"马媛媛","任务状态":"待测试/开发完成"},"owner":["闫浩"],"schedule":{"dev":{"startDate":"2025-05-09 00:00:00.0","endDate":"2025-05-09 23:59:59.0"},"integration":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-13 23:59:59.0"},"test":{"startDate":"2025-05-15 00:00:00.0","endDate":"2025-05-16 23:59:59.0"},"publish":{"startDate":"2025-05-19 00:00:00.0","endDate":"2025-05-20 23:59:59.0"},"gray":{"startDate":"2025-05-21 00:00:00.0","endDate":"2025-05-21 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5734968825221148,"label":"【待排期】主播卖点加违规词校验需求","avatar":"https://static.yximgs.com/bs2/kimAvatar/cda36913ad024b68a2be05a3db169b10?type=create","kimLink":"kim://thread?type=4&id=5734968825221148"}],"extraTime":[],"status":"联调中"},{"id":160,"key":"160","teamGroup":"xhc","category":"稳定性&架构","shortName":"浏览定位接入反爬","detail":"[{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLive/asyncAntispamGrayUri\",\"type\":\"Kconf\",\"title\":\"asyncAntispamGrayUri\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/24238/detail?activeTab=2&currentEnv=prod&viewPanel=3536697711&changeEvent=true\",\"type\":\"监控\"}]","teamLink":"https://team.corp.kuaishou.com/task/T8767142","teamTitle":"接口接入反爬","teamInfo":{"id":10782194,"taskId":"T8767142","title":"接口接入反爬","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"营销导购-直播间基础","sectionId":"S92046","statusPhase":"END","projectId":"P1063","taskTypeGroup":"demand","createdAt":"shichongwen","createdName":"石崇文","teamStatusLog":null,"提需组织":"其他","是否需要AB测试":"不需要AB测试","开发负责人":"闫浩","实际开发开始时间":"2025-05-08 15:29:34","提需人":"石崇文","执行人":"石崇文","任务状态":"已发布"},"owner":["闫浩"],"schedule":{"test":{"startDate":"2025-05-08 00:00:00.0","endDate":"2025-05-08 23:59:59.0"},"publish":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-12 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5455395339572284,"label":"小黄车B&C端接入反爬场景-测试群","avatar":"https://static.yximgs.com/bs2/kimAvatar/fb3bcfb2486c47e69f4ce405f0ef77fe?type=create","kimLink":"kim://thread?type=4&id=5455395339572284"}],"extraTime":[],"status":"已发布"},{"id":161,"key":"161","teamGroup":"xhc","category":"小黄车","shortName":"拉车引导自动/手动承接明确","detail":"[{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/couponTabInfoWithUsableItemSwitch\\\",\\\"title\\\":\\\"couponTabInfoWithUsableItemSwitch\",\"type\":\"Kconf\",\"title\":\"couponTabInfoWithUsableItemSwitch\\\",\\\"title\\\":\\\"couponTabInfoWithUsableItemSwitch\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"拉车引导自动/手动承接明确\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/8977/detail?a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E3%80%8C%E5%BC%80%E6%92%AD+/+%E5%B0%8F%E9%BB%84%E8%BD%A6%E7%83%AD%E7%82%B9%E3%80%8D%E4%BA%8B%E4%BB%B6=0&a_%E7%94%B5%E5%95%86%E5%A4%A7%E4%B8%BB%E6%92%AD%E5%85%B3%E6%92%AD=0&activeTab=9&currentEnv=prod&fromShare=1&viewPanel=9367168598&dashboardShowAlarm=false\",\"type\":\"监控\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"提测文档\",\"title\":\"拉车气泡二期-测试文档\"},{\"link\":\"https://abtest.corp.kuaishou.com/abnew#/experiment/49026/group?docType=12\",\"type\":\"测试报告\"}]","teamLink":"https://team.corp.kuaishou.com/task/T8758273","teamTitle":"拉车引导自动/手动承接明确","teamInfo":{"id":10771691,"taskId":"T8758273","title":"拉车引导自动/手动承接明确","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"社区电商","sectionId":"S111694","statusPhase":"END","projectId":"P1063","taskTypeGroup":"demand","createdAt":"zhangchi22","createdName":"张弛","teamStatusLog":null,"提需组织":"C端产品-用户","是否需要AB测试":"需要AB测试","开发负责人":"于传龙","实际开发开始时间":"2025-05-07 16:36:51","提需人":"张弛","执行人":"张弛","任务状态":"已发布"},"owner":["刘泉晟"],"schedule":{"dev":{"startDate":"2025-04-28 00:00:00.0","endDate":"2025-04-30 23:59:59.0"},"integration":{"startDate":"2025-05-06 00:00:00.0","endDate":"2025-05-06 23:59:59.0"},"test":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-09 23:59:59.0"},"publish":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-13 23:59:59.0"},"gray":{"startDate":"2025-05-14 00:00:00.0","endDate":"2025-05-15 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/merge_requests?mrId=2301&repoId=26229&tabKey=baseInfo","kimGroups":[{"value":5690259296233255,"label":"【全量】直播间自动拉车","avatar":"https://static.yximgs.com/bs2/kimAvatar/202f7bb2b25445ae97463b453a3d5f84?type=create","kimLink":"kim://thread?type=4&id=5690259296233255"}],"extraTime":[],"status":"放量中"},{"id":162,"key":"162","teamGroup":"xhc","category":"稳定性&架构","shortName":"AZ单元化升级","detail":"[{\"link\":\"\",\"type\":\"PRD\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["闫浩"],"schedule":{"dev":{"startDate":"2025-04-25 00:00:00.0","endDate":"2025-04-25 23:59:59.0"},"publish":{"startDate":"2025-05-11 00:00:00.0","endDate":"2025-05-12 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/merge_requests?mrId=2297&repoId=26229&tabKey=changedFiles","kimGroups":null,"extraTime":[],"status":"已发布"},{"id":164,"key":"164","teamGroup":"xhc","category":"讲解回放","shortName":"讲解回放页-下发讲解分辨率","detail":"[{\"type\":\"PRD\"}]","teamLink":"https://team.corp.kuaishou.com/task/T8811998","teamTitle":"连麦场景下讲解回放视频适配","teamInfo":{"id":10836418,"taskId":"T8811998","title":"连麦场景下讲解回放视频适配","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"直播间","sectionId":"S46005","statusPhase":"BEGIN","projectId":"P1063","taskTypeGroup":"demand","createdAt":"linyu06","createdName":"林瑜","teamStatusLog":null,"提需组织":"Bug修复","是否需要AB测试":"不需要AB测试","开发负责人":null,"实际开发开始时间":null,"提需人":"林瑜","执行人":"林瑜","任务状态":"需求 Idea"},"owner":["刘泉晟"],"schedule":{"dev":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-07 23:59:59.0"},"integration":{"startDate":"2025-05-12 00:00:00.0","endDate":"2025-05-12 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/compare?groupId=12333&repoId=26229&sourceBranch=feature_record_video_resolution","kimGroups":[{"value":5823113840208137,"label":"连麦场景下讲解回放视频 适配问题 [测试发现]","avatar":"https://static.yximgs.com/bs2/kimAvatar/f22e988479f94b4abeaf25064898b120?type=create","kimLink":"kim://thread?type=4&id=5823113840208137"}],"extraTime":[],"status":"联调中"},{"id":165,"key":"165","teamGroup":"xhc","category":"讲解回放","shortName":"购物车进讲解回放-没下发多码率","detail":"[{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/24991/detail?fromShare=1&viewPanel=6881206394&dashboardShowAlarm=false\",\"type\":\"监控\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["刘泉晟"],"schedule":{"dev":{"startDate":"2025-05-06 00:00:00.0","endDate":"2025-05-06 23:59:59.0"},"publish":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-07 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://kdev.corp.kuaishou.com/git/plateco-dev/kwaishop-c/kwaishop-flow-live-service/-/merge_requests?mrId=2293&repoId=26229&tabKey=changedFiles","kimGroups":[{"value":5822827982694715,"label":"电商&音视频技术交流群","avatar":"https://static.yximgs.com/bs2/kimAvatar/fb3bcfb2486c47e69f4ce405f0ef77fe?type=create","kimLink":"kim://thread?type=4&id=5822827982694715"}],"extraTime":[],"status":"已发布"},{"id":228,"key":"228","teamGroup":"xhc","category":"小黄车","shortName":"先用后付-0元试用/试吃权益拓展","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"【先用后付】-「0元试用」、「0元试吃」权益拓展\",\"value\":\"\"},{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"技术方案\",\"title\":\"0元试用&0元试吃项目技术方案\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":229,"key":"229","teamGroup":"xhc","category":"稳定性&架构","shortName":"直播回放老链路分批调用货架服务","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"提测文档\",\"title\":\"直播回放接口分批调用货架自测文档\",\"value\":\"\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/24238/detail?currentEnv=prod&activeTab=12\",\"type\":\"监控\"},{\"link\":\"https://kconf.corp.kuaishou.com/#/platecoDev/kwaishopFlowLiveService/batchQueryShelfItemInfoSwitch\",\"type\":\"Kconf\",\"title\":\"batchQueryShelfItemInfoSwitch\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["管盛灵"],"schedule":{"dev":{"startDate":"2025-04-16 00:00:00.0","endDate":"2025-04-16 23:59:59.0"},"test":{"startDate":"2025-04-25 00:00:00.0","endDate":"2025-04-25 23:59:59.0"},"publish":{"startDate":"2025-05-07 00:00:00.0","endDate":"2025-05-08 23:59:59.0"},"gray":{"startDate":"2025-05-08 00:00:00.0","endDate":"2025-05-09 23:59:59.0"}},"scheduleNote":null,"codeReviewLink":"https://ksurl.cn/VY1tTHoo","kimGroups":null,"extraTime":[],"status":"已放量"},{"id":230,"key":"230","teamGroup":"xhc","category":"小黄车","shortName":"直播电商-福利购支持双任务设置","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"PRD-直播电商-福利购支持双任务设置\",\"value\":\"\"}]","teamLink":"https://team.corp.kuaishou.com/task/********","teamTitle":"直播电商-福利购支持双任务设置","teamInfo":{"id":10556955,"taskId":"********","title":"直播电商-福利购支持双任务设置","taskTypeId":0,"taskTypeName":"产品需求","taskClassId":41292,"taskClassName":"产品需求","sectionName":"社区电商","sectionId":"S111694","statusPhase":"MIDDLE","projectId":"P1063","taskTypeGroup":"demand","createdAt":"zhangke17","createdName":"张珂","teamStatusLog":null,"提需组织":"C端产品-用户","是否需要AB测试":"不需要AB测试","开发负责人":null,"实际开发开始时间":null,"提需人":"张珂","执行人":"张珂","任务状态":"已排资源"},"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":231,"key":"231","teamGroup":"xhc","category":"稳定性&架构","shortName":"风控/反爬埋点","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/k/home/<USER>/fcADI_h9WktgAwCpcsNvnjXtx\",\"type\":\"Doc\",\"title\":\"反爬接入说明\",\"value\":\"\"},{\"link\":\"https://tianwen.corp.kuaishou.com/dashboard/24238/detail?activeTab=2&currentEnv=prod&viewPanel=3536697711&changeEvent=true\",\"type\":\"监控\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["陈磊"],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5853796516014420,"label":"0508-讲解回放显示无网络连接","avatar":"https://static.yximgs.com/bs2/kimAvatar/d085009aa45e49ca85dba4e74f15d590?type=create","kimLink":"kim://thread?type=4&id=5853796516014420"}],"extraTime":[],"status":"未开始"},{"id":232,"key":"232","teamGroup":"xhc","category":"小黄车","shortName":"reco信息透传","detail":"[{\"link\":\"https://docs.corp.kuaishou.com/d/home/<USER>",\"type\":\"PRD\",\"title\":\"小黄车排序请求透传信息需求\",\"value\":\"\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5536708643989291,"label":"直播间内商品推荐沟通群（不含讲解回放）","avatar":"https://static.yximgs.com/bs2/kimAvatar/ae070d744c6a4be3ac70e6857379b3fc?type=update","kimLink":"kim://thread?type=4&id=5536708643989291"}],"extraTime":[],"status":"未开始"},{"id":235,"key":"235","teamGroup":"xhc","category":"稳定性&架构","shortName":"讲解回放-极限容灾","detail":"[{\"type\":\"PRD\",\"value\":\"\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":["刘泉晟"],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":null,"extraTime":[],"status":"未开始"},{"id":236,"key":"236","teamGroup":"xhc","category":"小黄车","shortName":"店铺关闭展示问题","detail":"[{\"type\":\"PRD\",\"value\":\"\"}]","teamLink":"","teamTitle":null,"teamInfo":null,"owner":[],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5811417758242123,"label":"小黄车展示关闭店铺信息case","avatar":"https://static.yximgs.com/bs2/kimAvatar/b4126ed093e64f51bebf94925c39f3ee?type=create","kimLink":"kim://thread?type=4&id=5811417758242123"}],"extraTime":[],"status":"未开始"},{"id":237,"key":"237","teamGroup":"xhc","category":"讲解回放","shortName":"流量回放diff-评价入口","detail":"[{\"type\":\"PRD\",\"value\":\"\"}]","teamLink":"https://team.corp.kuaishou.com/task/B2494697","teamTitle":"【线上灰度】讲解回放record/item/list接口commentEntranceInfo 字段回放存在diff","teamInfo":{"id":10783876,"taskId":"B2494697","title":"【线上灰度】讲解回放record/item/list接口commentEntranceInfo 字段回放存在diff","taskTypeId":3455,"taskTypeName":"缺陷","taskClassId":3754,"taskClassName":"缺陷","sectionName":"流量回放召回问题","sectionId":"S133393","statusPhase":"END","projectId":"P1063","taskTypeGroup":"bug","createdAt":"raofugui","createdName":"饶福贵","teamStatusLog":null,"执行人":"刘泉晟","任务状态":"延后解决"},"owner":["刘泉晟"],"schedule":null,"scheduleNote":null,"codeReviewLink":null,"kimGroups":[{"value":5854451095540028,"label":"讲解回放record/item/list接口commentEntranceInfo 字段回放存在diff","avatar":"https://static.yximgs.com/bs2/kimAvatar/703e11dfc1b942978b43f04aeb26a1b9?type=create","kimLink":"kim://thread?type=4&id=5854451095540028"}],"extraTime":[],"status":"未开始"}];
}

// scheduleUpdate
export const scheduleUpdate = async (params) => {
  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    // 将对象转换为 JSON 字符串
    body: JSON.stringify(
      {
        api: "api/team/schedule/update",
        param: JSON.stringify(params)
      })
  });
  const result = await response.json();
  console.log("scheduleUpdate:", result);
  return JSON.parse(result.data);
}


// scheduleAdd
export const scheduleAdd = async (params) => {
  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    // 将对象转换为 JSON 字符串
    body: JSON.stringify(
      {
        api: "api/team/schedule/add",
        param: JSON.stringify(params)
      })
  });
  const result = await response.json();
  console.log("scheduleAdd:", result);
  return result.data;
}

// scheduleDelete
export const scheduleDelete = async (params) => {
  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    // 将对象转换为 JSON 字符串
    body: JSON.stringify(
      {
        api: "api/team/schedule/delete",
        param: JSON.stringify(params)
      })
  });
  const result = await response.json();
  console.log("scheduleDelete:", result);
  return result.data;
}


// scheduleDelete
export const pageConfig = async (params) => {
  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    // 将对象转换为 JSON 字符串
    body: JSON.stringify(
      {
        api: "api/team/schedule/pageConfig",
        param: JSON.stringify(params)
      })
  });
  const result = await response.json();
  console.log("pageConfig:", result);
  return JSON.parse(result.data);
}



// searchKimGroup，查询群信息
export const searchKimGroup = async (params) => {
  const response = await fetch('/gateway/live/control/common/api/invoke', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    // 将对象转换为 JSON 字符串
    body: JSON.stringify(
        {
          api: "api/team/schedule/searchKimGroup",
          param: JSON.stringify(params)
        })
  });
  const result = await response.json();
  console.log("searchKimGroup:", result);
  return JSON.parse(result.data);
}

// 通用查询组件
export const invoke_fetch = async (params) => {

  const response = await fetch('/data.json', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    // 将对象转换为 JSON 字符串
    body: JSON.stringify(params)
  });
  const data = await response.json();
  return data;
}
