// 脚本相关类型定义

export interface Variable {
  id: string;
  name: string; // 变量名，如 userId
  displayName: string; // 显示名
  type: 'input' | 'number' | 'boolean' | 'select' | 'radio' | 'textarea';
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>; // 用于 select 和 radio 类型
}

export interface ScriptData {
  id?: string;
  name: string;
  content: string; // Groovy脚本内容
  variables: Variable[];
  createdAt?: string;
  updatedAt?: string;
}

export interface ExecuteResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime?: number;
}

export interface ExecuteParams {
  scriptContent: string;
  variables: Record<string, any>;
}
