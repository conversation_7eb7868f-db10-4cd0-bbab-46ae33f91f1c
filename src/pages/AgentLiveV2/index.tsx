import React, {use<PERSON><PERSON>back, useEffect, useRef, useState} from 'react';
import Flex from '@/components/Flex';
import {Input, Tabs, Tooltip, Typography} from '@m-ui/react';
import StickyBox from 'react-sticky-box';
import {FieldTimeOutlined, QuestionCircleOutlined} from '@m-ui/icons';
import type {KwaiPlayer} from '@ks-video/kwai-player-web/react';
import {KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import cls from 'classnames/bind';
import style from './index.module.less';
import {AutoComplete, message, Space} from "antd";
import * as API from '@/common/API/AgentLiveV2API';
import type {GoodItem} from "@/pages/AgentLiveV2/Tabs/AgentLiveInfo";
import AgentLiveInfo from "@/pages/AgentLiveV2/Tabs/AgentLiveInfo";
import type {MatchedGood} from "@/components/Cards/MatchedGoodsCard";
import type {HistoryItem} from "@/pages/AgentLiveV2/Tabs/HistoryRecord";
import {LIVESTREAM_ID_KEY, STORAGE_KEY, TEST_LIVESTREAM_IDS} from "@/pages/AgentLiveV2/constant";
import {LiveStreamInfoDrawer} from '@/components/LiveStreamInfoDrawer';
import type {LiveStreamInfo} from '@/components/LiveStreamInfoTable';
import {Frame} from "@/interfaces/Frame";
import {LabelResultItem} from "@/interfaces/LabelResultItem";

export const cx = cls.bind(style);

/**
 * 将字符串列表转换为 AutoComplete options，并在每项前添加图标
 */
function mapHistoryToOptions(list: string[]): { value: string; label: React.ReactNode }[] {
  return list.map(v => ({
    value: v,
    label: (
        <Space>
          <FieldTimeOutlined />
          <span>{v}</span>
        </Space>
    ),
  }));
}

export default function AgentLiveV2() {

  const playerRef = useRef<KwaiPlayer | null>(null);

  const storedLiveStreamId = localStorage.getItem(LIVESTREAM_ID_KEY) ?? '';

  const [searchValue, setSearchValue] = useState(storedLiveStreamId);
  const [liveStreamId, setLiveStreamId] = useState<number>(() => {
    return storedLiveStreamId ? Number(storedLiveStreamId) : 0;
  });
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [activeKey, setActiveKey] = useState<'current'|'history'>('current');
  const [videoUrls, setVideoUrls] = useState<string[]>([]);
  const [srcIndex, setSrcIndex] = useState(0);
  const videoSrc = videoUrls[srcIndex] || '';

  // 商品列表和视频URL
  const [products, setProducts] = useState<GoodItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<GoodItem[]>(products);

  // 白盒化帧结果
  const[frameList,setFrameList] = useState<Frame[]>([]);
  // Agent策略匹配结果
  const [matchedGoodsAgent,setMatchedGoodsAgent] = useState<MatchedGood>();
  // 算法策略匹配结果
  const [matchedGoodsAlgo,setMatchedGoodsAlgo] = useState<MatchedGood>();
  // 回放策略匹配结果
  const [matchedGoodsReplay,setMatchedGoodsReplay] = useState<MatchedGood>();

  // 主播正在讲解的商品
  const [currentItem,setCurrentItem] = useState<MatchedGood| null>(null);
  // 历史记录
  const [itemTimeRecordsAlgo, setItemTimeRecordsAlgo] = useState<HistoryItem[]>([]);
  const [itemTimeRecordsAgent, setItemTimeRecordsAgent] = useState<HistoryItem[]>([]);
  //
  const [llmAnswer,setLlmAnswer] = useState<any>('');
  const [llmModelName,setLlmModelName] = useState<string>('');

  // 新增：Agent请求和响应信息
  const [agentRequest, setAgentRequest] = useState<any>(null);
  const [agentResponse, setAgentResponse] = useState<any>(null);

  const [startTime,setStartTime] = useState<string>('');
  const [endTime,setEndTime] = useState<string>('');

  const [windowTimeStamp,setWindowTimeStamp] =useState<number>(0);
  const [windowRelatedTime,setWindowRelatedTime] =useState<number>(0);

  const timestampRef = useRef<number | null>(null);
  const [specifiedTimestamp,setSpecifiedTimestamp] = useState(0);

  const [isLiving,setIsLiving] = useState(false);
  // 新增：标记用户是否在“回看”模式
  const [isReplaying, setIsReplaying]     = useState(false);

  // 搜索历史
  const [options, setOptions] = useState<{ value: string; label: React.ReactNode }[]>([]);
  const [open, setOpen] = useState(false);
  const autoCompleteRef = useRef<any>(null);

  // 新增：主播列表抽屉状态
  const [anchorDrawerVisible, setAnchorDrawerVisible] = useState(false);

  // 新增：URL参数中的taskId和mode
  const [urlTaskId, setUrlTaskId] = useState<string | undefined>();
  const [urlMode, setUrlMode] = useState<string | undefined>();
  const [urlMmuItemId, setUrlMmuItemId] = useState<number | undefined>();
  const [urlTmItemId, setUrlTmItemId] = useState<number | undefined>();

  const [replayId, setReplayId] =  useState<number | undefined>();
  const [replayModelVersion, setReplayModelVersion] = useState<string | undefined>();

  // 标注结果
  const [labelResult, setLabelResult] = useState<LabelResultItem[] | null>(null);

  useEffect(() => {
    setFilteredProducts(products);
  }, [products]);


  // —— 当 liveStreamId 变化时，保存到 localStorage ——
  useEffect(() => {
    if (!liveStreamId) return;
    localStorage.setItem(LIVESTREAM_ID_KEY, String(liveStreamId));
  }, [liveStreamId]);

  /**
   * 接口调用
   * @param id 直播流 ID
   * @param timestamp 可选的回放时间（单位：秒），仅回放模式下传入
   */
  const fetchInfo = useCallback(async (id: number, timestamp?: number) => {
        try {
          const params: { liveStreamId: number; timestamp: number | null } = {
            liveStreamId: id,
            timestamp: timestamp ?? 0,
          };
          const info = await API.LiveInfoV2(params);

          if(timestamp == undefined) {
            setVideoUrls(info.videoUrl ?? []);
            setSrcIndex(0);
          }

          setWindowRelatedTime(info.windowRelatedTime ?? 0);
          setWindowTimeStamp(info.windowTimestamp ?? 0);

          setMatchedGoodsAgent(info.matchedGoodsAgent?.[0] ?? null);
          setMatchedGoodsAlgo(info.matchedGoodsAlgo?.[0] ?? null);
          setFrameList(info.frameTraceInfoList ?? []);
        } catch (err) {
          message.error('接口调用失败');
          console.error(err);
        }
      },
      [] // 保持空依赖数组，因为内部使用的都是setState函数
  );

  const fetchInfoByUrl = useCallback(async (id: number, timestamp: number, mmuItemId:number,tmItemId:number,switchNewData:boolean) => {
        try {
          const params: { liveStreamId: number; timestamp: number, mmuItemId:number,tmItemId:number,switchNewData:boolean} = {
            liveStreamId: id,
            timestamp: timestamp ?? 0,
            mmuItemId:mmuItemId,
            tmItemId:tmItemId,
            switchNewData:switchNewData,
          };
          const info = await API.LiveInfoV2(params);

          // 更新视频
          setVideoUrls(info.videoUrl ?? []);
          setSrcIndex(0);
          
          setWindowRelatedTime(info.windowRelatedTime ?? 0);
          setWindowTimeStamp(info.windowTimestamp ?? 0);
          setCurrentItem(info.matchedGoodsMmu?.[0] ?? null);
          setMatchedGoodsAgent(info.matchedGoodsAgent?.[0] ?? null);
          setMatchedGoodsAlgo(info.matchedGoodsAlgo?.[0] ?? null);
          setFrameList(info.frameTraceInfoList ?? []);
          setAgentRequest(info.agentRequest ?? '');
          setAgentResponse(info.agentResponse ?? '');

        } catch (err) {
          message.error('接口调用失败');
          console.error(err);
        }
      },
      []
  );


  const fetchInfoByUrlForReplay = useCallback(async (replayId: number) => {
        try {
          const params: { replayId: number} = {
            replayId: replayId,
          };
          const info = await API.LiveInfoV2ForReplay(params);

          setVideoUrls(info.videoUrl ?? []);
          setSrcIndex(0);

          setWindowRelatedTime(info.windowRelatedTime ?? 0);
          setWindowTimeStamp(info.windowTimestamp ?? 0);
          setMatchedGoodsReplay(info.matchedGoodsReplay?.[0] ?? null)
          setFrameList(info.frameTraceInfoList ?? []);
          setAgentRequest(info.agentRequest ?? '');
          setAgentResponse(info.agentResponse ?? '');
          setReplayModelVersion(info.replayFineTuneModelVersion ?? '');
          setLabelResult(info.labelResult ?? null);
          timestampRef.current = info.windowRelatedTime/1000;

        } catch (err) {
          message.error('接口调用失败');
          console.error(err);
        }
      },
      []
  );

  // 新增：获取主播列表的函数
  const fetchLiveStreamInfo = useCallback(async (): Promise<LiveStreamInfo[]> => {
    try {
      const result = await API.LiveStreamInfo({});
      return result || [];
    } catch (error) {
      console.error('获取主播列表失败:', error);
      throw error;
    }
  }, []);

  // 2. helper：把新值写入 localStorage 并去重、限长
  const saveToHistory = useCallback((val: string) => {
    if (!val) return;
    const raw = localStorage.getItem(STORAGE_KEY);
    let list: string[] = raw ? JSON.parse(raw) : [];
    list = [val, ...list.filter(x => x !== val)];
    if (list.length > 10) list = list.slice(0, 10);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(list));
    setOptions(mapHistoryToOptions(list));
  }, []);

  // 处理URL参数：liveStreamId 和 timestamp
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const idParam = urlParams.get('liveStreamId');
    const timestampParam = urlParams.get('timestamp');
    const mmuItemId = Number(urlParams.get('mmuItemId'));
    const tmItemId = Number(urlParams.get('tmItemId'));
    const taskId = urlParams.get('taskId');
    const mode = urlParams.get('mode');
    const replayId = Number(urlParams.get('replayId'));
    const switchNewDataRaw = urlParams.get('switchNewData');
    const switchNewData: boolean = switchNewDataRaw === 'true';// 只有当它精确等于 'true' 时才为 true，其它（null、'false'、''）都为 false
    // 保存URL参数到状态
    setUrlTaskId(taskId || undefined);
    setUrlMode(mode || undefined);
    setUrlMmuItemId(mmuItemId || undefined);
    setUrlTmItemId(tmItemId || undefined);
    setReplayId(replayId || undefined);

    if (idParam) {
      const id = Number(idParam);
      if (!isNaN(id) && id > 0) {
        setSearchValue(idParam);
        setLiveStreamId(id);

        let timestamp;
        if (timestampParam) {
          const ts = parseFloat(timestampParam);
          if (!isNaN(ts) && ts >= 0) {
            timestamp = ts;
            timestampRef.current = ts;
          }else{
            timestamp = 0;
          }
        }
        if (!isNaN(replayId) && replayId > 0) {
            fetchInfoByUrlForReplay(replayId); //回放任务

        }else{
          fetchInfoByUrl(id, timestamp, mmuItemId,tmItemId,switchNewData);
        }
        // 保存到历史记录
        saveToHistory(idParam);
      }
    }
  }, []);

  const onTimeUpdate = useCallback((_: any) => {
    const inst = (playerRef.current as any)
    if (!inst) return
    let t: number;
    if (typeof inst.currentTime === 'number') {
      t = inst.currentTime;
    } else if (typeof inst.getCurrentTime === 'function') {
      t = inst.getCurrentTime();
    } else {
      t = 0;
    }
    setCurrentTime(t)
  }, [playerRef])

  const onLoadedMetadata = useCallback(() => {
    const inst = playerRef.current
    if (!inst) return
    const d = (inst as any)?.duration
    if (typeof d === 'number') setDuration(d)
    console.log('视频元数据加载 -- duration: ', d)
    // 如果有 timestamp 参数，跳转到对应时间并自动播放
    if (timestampRef.current !== null) {
      try {
        (inst as any).currentTime = timestampRef.current;
        if(isLiving) {
          setIsReplaying(true)
        }
      } catch (e) {
        console.warn('设置跳转时间失败', e);
      }
    }
    //(inst as any)?.play();
  }, [])

  const handleTabClick = (key: string) => {
    if (key === 'history') {
      // fetchHistory(); // 重新调用接口
    }
  };

  const handleTabChange = (key: string) => {
    setActiveKey(key as 'current' | 'history');
  };

  /**
   * 搜索框的历史记录处理
   */
  // 1. 初始化时从 localStorage 读历史
  useEffect(() => {
    // 检查是否有URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const idParam = urlParams.get('liveStreamId');

    // 如果没有URL参数，才从localStorage读取
    if (!idParam && storedLiveStreamId) {
      const id = Number(storedLiveStreamId);
      setLiveStreamId(id);
      fetchInfo(id);
    }

    // 读取搜索历史
    const raw = localStorage.getItem(STORAGE_KEY);
    if (raw) {
      try {
        const list: string[] = JSON.parse(raw);
        setOptions(mapHistoryToOptions(list));
      } catch {
        //
      }
    }
  }, []);

  // 3. 处理搜索
  const onSearch = useCallback((val: string) => {
    setSearchValue(val);
    const id = Number(val);
    setLiveStreamId(id);
    fetchInfo(id);
    timestampRef.current = null;
    saveToHistory(val);
    setOpen(false);
  }, [fetchInfo, setLiveStreamId, saveToHistory]);

  // 新增：处理直播ID点击的回调函数
  const handleLiveStreamIdClick = useCallback((liveStreamId: number) => {
    setSearchValue(String(liveStreamId));
    setLiveStreamId(Number(liveStreamId));
    fetchInfo(Number(liveStreamId));
    setAnchorDrawerVisible(false); // 关闭抽屉
  }, [fetchInfo]);

  return (
      <Flex vertical gap={16} className={cx('video')}>
        <Flex className={cx('columns')} gap={16} style={{ display: 'flex', flex: 1, width: '100%' ,paddingRight:"40px"}}>

          <Flex className={cx('left')} vertical gap={16} style={{ flex: '0 0 420px' }}>
            <StickyBox offsetTop={20} className={cx('sticky-box')}>

              {/* 标题 + Tooltip */}
              <Flex gap={8} align="center" style={{ padding: '0 16px', height: 30, lineHeight: '30px', fontSize: '18px',marginBottom:16 }}>
                <Typography.Text strong className={cx('title')}>
                  智能直播讲解
                </Typography.Text>
                <Tooltip title="输入 liveStreamId 搜索">
                  <QuestionCircleOutlined style={{ color: '#999' }} />
                </Tooltip>
                <AutoComplete
                    ref={autoCompleteRef}
                    style={{ width: '100%', maxWidth: 220 ,marginLeft:10}}
                    options={options}
                    open={open}
                    value={searchValue}
                    onFocus={() => setOpen(options.length > 0)}      // 聚焦时如果有历史就展开
                    onBlur={() => setTimeout(() => setOpen(false), 200)} // 200ms 延迟，保留点击选项时间
                    onSelect={value => onSearch(value)}               // 选中历史记录
                    onSearch={val => {
                      setSearchValue(val);
                      // 动态过滤下拉
                      const history: string[] = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
                      setOptions(mapHistoryToOptions(
                          history.filter(item => item.includes(val))
                      ));
                    }}
                >
                  <Input.Search
                      placeholder='请输入直播间ID'
                      onChange={e => setSearchValue(e.target.value)}
                      enterButton
                      onSearch={onSearch}
                  />
                </AutoComplete>
              </Flex>

              {/* 推荐直播间*/}
              <Flex gap={8} align="center" style={{ padding: '0 16px', height: 30, lineHeight: '30px', fontSize: '18px',marginBottom:16 }}>
                <Typography.Text type="secondary" style={{ fontSize: 14  }}>
                  试试这些：
                </Typography.Text>
                {TEST_LIVESTREAM_IDS.map(item => (
                    <Typography.Link
                        key={item}
                        type="secondary"
                        style={{
                          fontSize: 14,
                          marginRight: '8px', // 每个链接右侧添加空格
                        }}
                        onClick={() => {
                          setSearchValue(String(item));
                          setLiveStreamId(Number(item));
                          fetchInfo(Number(item));
                        }}
                    >
                      {item}
                    </Typography.Link>
                ))}
                <Typography.Link
                    type="secondary"
                    style={{
                      fontSize: 14,
                      marginLeft: 8,
                      color: '#1890FF', // 蓝色（可替换为任意蓝色值）
                      fontWeight: 'bold' // 加粗
                    }}
                    onClick={() => setAnchorDrawerVisible(true)}
                >
                  查看更多
                </Typography.Link>
              </Flex >

              {/* 播放器 */}
              <Flex vertical gap={8} className={cx('player')} style={{ padding: '0 16px' }}>
                <KwaiPlayerReact
                    key={`${liveStreamId}-${srcIndex}`}
                    style={{ width: '100%', height: '100%' }}
                    id="player"
                    ref={playerRef}
                    src={videoSrc}
                    // autoPlay={true}
                    showProgress={false}
                    controls={ true}
                    preload="auto"
                    onError={() => { if (srcIndex + 1 < videoUrls.length) setSrcIndex(srcIndex + 1); }}
                    onTimeUpdate={onTimeUpdate}
                    onLoadedMetadata={onLoadedMetadata}
                />
              </Flex>

              <Flex className={cx('info')} vertical gap={0}>

              </Flex>
            </StickyBox>
          </Flex>


          <Flex className={cx('right')} style={{ justifyContent: 'center', flex: '0 0 1250px' }} >
            <Tabs activeKey={activeKey} onChange={handleTabChange} onTabClick={handleTabClick}>
              <Tabs.TabPane tab="当前识别" key="current">
                <AgentLiveInfo
                    liveStreamId={liveStreamId??0}
                    onSelectChange={keys => console.log('select', keys)}
                    playerRef={playerRef}
                    duration={playerRef.current?.duration ?? 0}
                    playerCurrentTime={currentTime}
                    fetchInfo={fetchInfo}
                    timestamp={specifiedTimestamp}
                    currentItem={currentItem}
                    matchedGoodsAgent={matchedGoodsAgent ?? null}
                    matchedGoodsAlgo={matchedGoodsAlgo ?? null}
                    matchedGoodsReplay={matchedGoodsReplay ?? null}
                    startTime={startTime}
                    endTime={endTime}
                    isLiving={isLiving}
                    llmAnswer={llmAnswer}
                    llmModelName={llmModelName}

                    itemTimeRecords={itemTimeRecordsAlgo ?? []} //TODO 进度条先默认展示算法策略记录，之后再改
                    // isReplaying={isReplaying}
                    isReplaying={true}
                    onReplayingChange={setIsReplaying}
                    frameList={frameList ?? []}
                    windowRelatedTime={windowRelatedTime}
                    windowTimeStamp={windowTimeStamp}
                    evaluationLiveStreamId={liveStreamId}
                    evaluationTimestamp={windowRelatedTime}
                    taskId={urlTaskId}
                    mode={urlMode}
                    mmuItemId={urlMmuItemId}
                    tmItemId={urlTmItemId}
                    agentRequest={agentRequest}
                    agentResponse={agentResponse}
                    replayId = {replayId}
                    replayModelVersion = {replayModelVersion}
                    labelResult={labelResult}
                />
              </Tabs.TabPane>
              {/*<Tabs.TabPane tab="历史识别记录" key="history">*/}
              {/*  <HistoryRecord*/}
              {/*      itemTimeRecordsAlgo={itemTimeRecordsAlgo ?? []}*/}
              {/*      itemTimeRecordsAgent={itemTimeRecordsAgent ?? []}*/}
              {/*      playerRef={playerRef}*/}
              {/*  />*/}
              {/*</Tabs.TabPane>*/}
            </Tabs>

          </Flex>
        </Flex>

        {/* 主播列表抽屉 */}
        <LiveStreamInfoDrawer
            visible={anchorDrawerVisible}
            onClose={() => setAnchorDrawerVisible(false)}
            fetchLiveStreamInfo={fetchLiveStreamInfo}
            onLiveStreamIdClick={handleLiveStreamIdClick}
        />
      </Flex>
  );
}
