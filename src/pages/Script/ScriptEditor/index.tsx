import React, { useState, useEffect, useRef } from 'react';
import { Button, message, Typography, Space, Card, Alert, Spin } from 'antd';
import { SaveOutlined, PlayCircleOutlined, EyeOutlined, BugOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import Editor from '@monaco-editor/react';
import VariableEditor from './VariableEditor';
import type { Variable, ScriptData, ExecuteResult } from '@/types/script';

const { Title } = Typography;

const ScriptEditor: React.FC = () => {
  const navigate = useNavigate();
  const editorRef = useRef<any>(null);

  const [scriptContent, setScriptContent] = useState<string>(`// Groovy脚本示例
// 使用 $变量名 来引用变量，如 $userId, $userName

def processUser() {
    println "处理用户: \${$userId}"
    println "用户名: \${$userName}"

    // 你的业务逻辑
    def result = [
        userId: $userId,
        userName: $userName,
        processTime: new Date(),
        status: 'success'
    ]

    return result
}

// 执行主逻辑
return processUser()`);

  const [variables, setVariables] = useState<Variable[]>([]);
  const [saving, setSaving] = useState(false);
  const [debugging, setDebugging] = useState(false);
  const [debugResult, setDebugResult] = useState<ExecuteResult | null>(null);

  // 从脚本内容中提取变量
  const extractVariablesFromScript = (content: string) => {
    const variableRegex = /\$([a-zA-Z_][a-zA-Z0-9_]*)/g;
    const matches = content.match(variableRegex);

    if (!matches) {
      setVariables([]);
      return;
    }

    const uniqueVariableNames = [...new Set(matches.map(match => match.substring(1)))];
    const newVariables: Variable[] = [];

    uniqueVariableNames.forEach(name => {
      // 检查是否已存在该变量
      const existingVar = variables.find(v => v.name === name);
      if (existingVar) {
        newVariables.push(existingVar);
      } else {
        // 创建新变量
        newVariables.push({
          id: `var_${Date.now()}_${name}`,
          name,
          displayName: name,
          type: 'string',
          defaultValue: ''
        });
      }
    });

    setVariables(newVariables);
  };

  // 编辑器挂载时的回调
  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // 配置深色主题
    monaco.editor?.defineTheme?.('groovy-dark-theme', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'variable', foreground: '#4FC3F7', fontStyle: 'bold' },
        { token: 'string', foreground: '#A5D6A7' },
        { token: 'comment', foreground: '#757575', fontStyle: 'italic' },
        { token: 'keyword', foreground: '#81C784', fontStyle: 'bold' }
      ],
      colors: {
        'editor.background': '#1E1E1E',
        'editor.lineHighlightBackground': '#2D2D30'
      }
    });

    monaco.editor?.setTheme?.('groovy-dark-theme');

    // 添加变量高亮
    updateVariableHighlight(monaco);
  };

  // 更新变量高亮
  const updateVariableHighlight = (monaco: any) => {
    if (!monaco) return;

    // 创建变量名的正则表达式
    const variableNames = variables?.map(v => v.name)?.filter?.(name => name.trim());
    if (variableNames.length === 0) return;

    const variablePattern = `\\$(?:${variableNames.join('|')})\\b`;

    // 注册语言规则
    monaco.languages?.setMonarchTokensProvider?.('groovy', {
      tokenizer: {
        root: [
          [new RegExp(variablePattern), 'variable'],
          [/\/\/.*$/, 'comment'],
          [/".*?"/, 'string'],
          [/'.*?'/, 'string'],
          [/\b(def|class|if|else|for|while|return|import|package)\b/, 'keyword']
        ]
      }
    });
  };

  // 保存脚本
  const handleSave = async () => {
    try {
      setSaving(true);

      const scriptData: ScriptData = {
        name: '未命名脚本',
        content: scriptContent,
        variables: variables,
        updatedAt: new Date().toISOString()
      };

      // 模拟保存API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success('脚本保存成功！');
      console.log('保存的脚本数据:', scriptData);

    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 调试脚本
  const handleDebug = async () => {
    if (!scriptContent.trim()) {
      message.warning('请先编写脚本内容');
      return;
    }

    try {
      setDebugging(true);
      setDebugResult(null);

      // 模拟调试执行
      const startTime = Date.now();
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1500));
      const executionTime = Date.now() - startTime;

      // 创建模拟的变量值
      const mockVariableValues: Record<string, any> = {};
      variables.forEach(variable => {
        switch (variable.type) {
          case 'number':
            mockVariableValues[variable.name] = Math.floor(Math.random() * 1000);
            break;
          case 'boolean':
            mockVariableValues[variable.name] = Math.random() > 0.5;
            break;
          default:
            mockVariableValues[variable.name] = variable.defaultValue || `mock_${variable.name}`;
        }
      });

      const mockResult = {
        success: Math.random() > 0.2, // 80% 成功率
        result: Math.random() > 0.2 ? {
          message: '调试执行成功',
          variables: mockVariableValues,
          output: `处理完成，共处理 ${Math.floor(Math.random() * 50)} 条数据`,
          timestamp: new Date().toISOString()
        } : undefined,
        error: Math.random() > 0.2 ? undefined : `语法错误：第 ${Math.floor(Math.random() * 10) + 1} 行附近`,
        executionTime
      };

      setDebugResult(mockResult);
    } catch (error) {
      setDebugResult({
        success: false,
        error: '调试执行失败'
      });
    } finally {
      setDebugging(false);
    }
  };

  // 预览脚本
  const handlePreview = () => {
    if (!scriptContent.trim()) {
      message.warning('请先编写脚本内容');
      return;
    }

    // 跳转到执行页面，传递脚本内容和变量配置
    navigate('/script-execute', {
      state: {
        scriptContent,
        variables
      }
    });
  };

  // 处理脚本内容变化
  const handleScriptChange = (value: string | undefined) => {
    const newContent = value || '';
    setScriptContent(newContent);
    // 自动提取变量
    extractVariablesFromScript(newContent);
  };

  // 监听变量变化，更新编辑器高亮
  useEffect(() => {
    if (editorRef.current) {
      const monaco = (window as any)?.monaco;
      if (monaco) {
        updateVariableHighlight(monaco);
      }
    }
  }, [variables]);

  return (
    <div style={{ padding: '24px', height: '100vh' ,width:'100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>Groovy 脚本编辑器</Title>
          <Space>
            <Button
              type="default"
              icon={<SaveOutlined />}
              loading={saving}
              onClick={handleSave}
            >
              {saving ? '保存中...' : '保存'}
            </Button>
            <Button
              type="default"
              icon={<BugOutlined />}
              loading={debugging}
              onClick={handleDebug}
            >
              {debugging ? '调试中...' : '调试'}
            </Button>
            <Button
              type="primary"
              icon={<EyeOutlined />}
              onClick={handlePreview}
            >
              预览执行
            </Button>
          </Space>
        </div>
      </div>

      {/* 主体内容 */}
      <div style={{ flex: 1, display: 'flex', gap: '16px', minHeight: 0 }}>
        {/* 左侧：代码编辑器和调试结果 */}
        <div style={{ flex: 2, display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* 代码编辑器 */}
          <Card
            title="脚本编辑"
            style={{ flex: 2, display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, padding: 0 }}
          >
            <Editor
              height="100%"
              defaultLanguage="groovy"
              value={scriptContent}
              onChange={handleScriptChange}
              onMount={handleEditorDidMount}
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                wordWrap: 'on',
                folding: true,
                lineDecorationsWidth: 10,
                lineNumbersMinChars: 3,
                renderLineHighlight: 'line',
                theme: 'vs-dark'
              }}
            />
          </Card>

          {/* 调试结果 */}
          <Card
            title="调试结果"
            style={{ flex: 1, minHeight: '200px' }}
          >
            {debugging && (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px', color: '#666' }}>
                  正在调试执行...
                </div>
              </div>
            )}

            {debugResult && !debugging && (
              <div>
                <Alert
                  type={debugResult.success ? 'success' : 'error'}
                  message={debugResult.success ? '调试成功' : '调试失败'}
                  description={
                    debugResult.success
                      ? `执行时间: ${debugResult.executionTime}ms`
                      : debugResult.error
                  }
                  style={{ marginBottom: '16px' }}
                />

                {debugResult.success && debugResult.result && (
                  <div style={{
                    background: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    padding: '12px',
                    borderRadius: '4px',
                    fontFamily: 'Monaco, Consolas, monospace',
                    fontSize: '12px',
                    maxHeight: '150px',
                    overflow: 'auto'
                  }}>
                    <pre style={{ margin: 0 }}>
                      {JSON.stringify(debugResult.result, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}

            {!debugResult && !debugging && (
              <div style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px',
                border: '1px dashed #d9d9d9',
                borderRadius: '4px'
              }}>
                点击"调试"按钮开始调试执行
              </div>
            )}
          </Card>
        </div>

        {/* 右侧：变量编辑器 */}
        <div style={{ flex: 1, minWidth: '300px' }}>
          <VariableEditor
            variables={variables}
            onChange={setVariables}
          />
        </div>
      </div>
    </div>
  );
};

export default ScriptEditor;
