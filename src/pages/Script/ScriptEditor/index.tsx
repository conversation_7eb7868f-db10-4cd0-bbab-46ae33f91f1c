import React, { useState, useEffect, useRef } from 'react';
import { Button, message, Typography, Space, Card, Alert, Spin } from 'antd';
import { SaveOutlined, PlayCircleOutlined, EyeOutlined, BugOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import Editor from '@monaco-editor/react';
import VariableEditor from './VariableEditor';
import type { Variable, ScriptData, ExecuteResult } from '@/types/script';

const { Title } = Typography;

const ScriptEditor: React.FC = () => {
  const navigate = useNavigate();
  const editorRef = useRef<any>(null);

  const [scriptContent, setScriptContent] = useState<string>(`// Groovy脚本示例
// 使用 $变量名 来引用变量，如 $userId, $userName

def processUser() {
    println "处理用户: \${$userId}"
    println "用户名: \${$userName}"

    // 你的业务逻辑
    def result = [
        userId: $userId,
        userName: $userName,
        processTime: new Date(),
        status: 'success'
    ]

    return result
}

// 执行主逻辑
return processUser()`);

  const [variables, setVariables] = useState<Variable[]>([]);
  const [saving, setSaving] = useState(false);
  const [debugging, setDebugging] = useState(false);
  const [debugResult, setDebugResult] = useState<ExecuteResult | null>(null);

  // 从脚本内容中提取变量
  const extractVariablesFromScript = (content: string) => {
    const variableRegex = /\$([a-zA-Z_][a-zA-Z0-9_]*)/g;
    const matches = content.match(variableRegex);

    // 获取当前脚本中的所有变量名
    const currentVariableNames = matches ? [...new Set(matches.map(match => match.substring(1)))] : [];

    // 如果没有变量，清空变量列表
    if (currentVariableNames.length === 0) {
      setVariables([]);
      return;
    }

    const newVariables: Variable[] = [];

    // 添加当前脚本中存在的变量
    currentVariableNames.forEach(name => {
      // 检查是否已存在该变量，保留已配置的信息
      const existingVar = variables.find(v => v.name === name);
      if (existingVar) {
        newVariables.push(existingVar);
      } else {
        // 创建新变量，使用更智能的默认显示名
        const displayName = name
          .replace(/([A-Z])/g, ' $1') // 驼峰转空格
          .replace(/^./, str => str.toUpperCase()) // 首字母大写
          .trim();

        newVariables.push({
          id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name,
          displayName,
          type: 'string',
          defaultValue: ''
        });
      }
    });

    // 移除不再存在于脚本中的变量
    setVariables(newVariables);
  };

  // 注册Groovy语言支持
  const registerGroovyLanguage = (monaco: any) => {
    // 注册Groovy语言
    monaco.languages?.register?.({ id: 'groovy' });

    // 设置Groovy语言的语法规则
    monaco.languages?.setMonarchTokensProvider?.('groovy', {
      tokenizer: {
        root: [
          // 变量 ($开头)
          [/\$[a-zA-Z_][a-zA-Z0-9_]*/, 'variable'],

          // 字符串
          [/"([^"\\]|\\.)*$/, 'string.invalid'],
          [/"/, 'string', '@string_double'],
          [/'([^'\\]|\\.)*$/, 'string.invalid'],
          [/'/, 'string', '@string_single'],
          [/"""/, 'string', '@string_multiline'],

          // 注释
          [/\/\*/, 'comment', '@comment'],
          [/\/\/.*$/, 'comment'],

          // 数字
          [/\d*\.\d+([eE][\-+]?\d+)?[fFdD]?/, 'number.float'],
          [/0[xX][0-9a-fA-F]+[Ll]?/, 'number.hex'],
          [/0[0-7]+[Ll]?/, 'number.octal'],
          [/\d+[lL]?/, 'number'],

          // 关键字
          [/\b(abstract|as|assert|boolean|break|byte|case|catch|char|class|const|continue|def|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|in|instanceof|int|interface|long|native|new|package|private|protected|public|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|transient|try|void|volatile|while)\b/, 'keyword'],

          // 操作符
          [/[=><!~?:&|+\-*\/\^%]+/, 'operator'],

          // 分隔符
          [/[;,.]/, 'delimiter'],
          [/[{}()\[\]]/, 'bracket'],

          // 标识符
          [/[a-zA-Z_$][\w$]*/, 'identifier'],
        ],

        string_double: [
          [/[^\\"]+/, 'string'],
          [/\\./, 'string.escape'],
          [/"/, 'string', '@pop']
        ],

        string_single: [
          [/[^\\']+/, 'string'],
          [/\\./, 'string.escape'],
          [/'/, 'string', '@pop']
        ],

        string_multiline: [
          [/[^"]+/, 'string'],
          [/"""/, 'string', '@pop'],
          [/"/, 'string']
        ],

        comment: [
          [/[^\/*]+/, 'comment'],
          [/\*\//, 'comment', '@pop'],
          [/[\/*]/, 'comment']
        ]
      }
    });
  };

  // 编辑器挂载时的回调
  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // 注册Groovy语言支持
    registerGroovyLanguage(monaco);

    // 配置丰富的Groovy深色主题
    monaco.editor?.defineTheme?.('groovy-rich-theme', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        // 变量高亮 ($开头)
        { token: 'variable', foreground: '#FF6B6B', fontStyle: 'bold' },

        // 字符串
        { token: 'string', foreground: '#4ECDC4' },
        { token: 'string.escape', foreground: '#96CEB4' },
        { token: 'string.invalid', foreground: '#FF7675' },

        // 注释
        { token: 'comment', foreground: '#6C7B7F', fontStyle: 'italic' },

        // 关键字
        { token: 'keyword', foreground: '#45B7D1', fontStyle: 'bold' },

        // 数字
        { token: 'number', foreground: '#FFEAA7' },
        { token: 'number.float', foreground: '#FFEAA7' },
        { token: 'number.hex', foreground: '#FFEAA7' },
        { token: 'number.octal', foreground: '#FFEAA7' },

        // 操作符
        { token: 'operator', foreground: '#96CEB4' },

        // 分隔符和括号
        { token: 'delimiter', foreground: '#DDA0DD' },
        { token: 'bracket', foreground: '#DDA0DD' },

        // 标识符
        { token: 'identifier', foreground: '#f8f8f2' },
      ],
      colors: {
        'editor.background': '#1a1a1a',
        'editor.foreground': '#f8f8f2',
        'editor.lineHighlightBackground': '#2d2d2d',
        'editor.selectionBackground': '#44475a',
        'editor.selectionHighlightBackground': '#424450',
        'editorCursor.foreground': '#f8f8f0',
        'editorWhitespace.foreground': '#3b3a32',
        'editorIndentGuide.background': '#3b3a32',
        'editorIndentGuide.activeBackground': '#9d550fb0',
        'editor.selectionHighlightBorder': '#222218'
      }
    });

    monaco.editor?.setTheme?.('groovy-rich-theme');

    // 初始化时提取变量
    extractVariablesFromScript(scriptContent);
  };



  // 保存脚本
  const handleSave = async () => {
    try {
      setSaving(true);

      const scriptData: ScriptData = {
        name: '未命名脚本',
        content: scriptContent,
        variables: variables,
        updatedAt: new Date().toISOString()
      };

      // 模拟保存API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success('脚本保存成功！');
      console.log('保存的脚本数据:', scriptData);

    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 调试脚本
  const handleDebug = async () => {
    if (!scriptContent.trim()) {
      message.warning('请先编写脚本内容');
      return;
    }

    try {
      setDebugging(true);
      setDebugResult(null);

      // 模拟调试执行
      const startTime = Date.now();
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1500));
      const executionTime = Date.now() - startTime;

      // 创建模拟的变量值
      const mockVariableValues: Record<string, any> = {};
      variables.forEach(variable => {
        switch (variable.type) {
          case 'number':
            mockVariableValues[variable.name] = Math.floor(Math.random() * 1000);
            break;
          case 'boolean':
            mockVariableValues[variable.name] = Math.random() > 0.5;
            break;
          default:
            mockVariableValues[variable.name] = variable.defaultValue || `mock_${variable.name}`;
        }
      });

      const mockResult = {
        success: Math.random() > 0.2, // 80% 成功率
        result: Math.random() > 0.2 ? {
          message: '调试执行成功',
          variables: mockVariableValues,
          output: `处理完成，共处理 ${Math.floor(Math.random() * 50)} 条数据`,
          timestamp: new Date().toISOString()
        } : undefined,
        error: Math.random() > 0.2 ? undefined : `语法错误：第 ${Math.floor(Math.random() * 10) + 1} 行附近`,
        executionTime
      };

      setDebugResult(mockResult);
    } catch (error) {
      setDebugResult({
        success: false,
        error: '调试执行失败'
      });
    } finally {
      setDebugging(false);
    }
  };

  // 预览脚本
  const handlePreview = () => {
    if (!scriptContent.trim()) {
      message.warning('请先编写脚本内容');
      return;
    }

    // 跳转到执行页面，传递脚本内容和变量配置
    navigate('/script-execute', {
      state: {
        scriptContent,
        variables
      }
    });
  };

  // 处理脚本内容变化
  const handleScriptChange = (value: string | undefined) => {
    const newContent = value || '';
    setScriptContent(newContent);
    // 自动提取变量
    extractVariablesFromScript(newContent);
  };



  return (
    <div style={{ padding: '24px', height: '100vh' ,width:'100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>Groovy 脚本编辑器</Title>
          <Space>
            <Button
              type="default"
              icon={<SaveOutlined />}
              loading={saving}
              onClick={handleSave}
            >
              {saving ? '保存中...' : '保存'}
            </Button>
            <Button
              type="default"
              icon={<BugOutlined />}
              loading={debugging}
              onClick={handleDebug}
            >
              {debugging ? '调试中...' : '调试'}
            </Button>
            <Button
              type="primary"
              icon={<EyeOutlined />}
              onClick={handlePreview}
            >
              预览执行
            </Button>
          </Space>
        </div>
      </div>

      {/* 主体内容 */}
      <div style={{ flex: 1, display: 'flex', gap: '16px', minHeight: 0 }}>
        {/* 左侧：代码编辑器和调试结果 */}
        <div style={{ flex: 2, display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* 代码编辑器 */}
          <Card
            title="脚本编辑"
            style={{ flex: 2, display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, padding: 0 }}
          >
            <Editor
              height="100%"
              defaultLanguage="groovy"
              value={scriptContent}
              onChange={handleScriptChange}
              onMount={handleEditorDidMount}
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                wordWrap: 'on',
                folding: true,
                lineDecorationsWidth: 10,
                lineNumbersMinChars: 3,
                renderLineHighlight: 'line',
                theme: 'vs-dark'
              }}
            />
          </Card>

          {/* 调试结果 */}
          <Card
            title="调试结果"
            style={{ flex: 1, minHeight: '200px' }}
          >
            {debugging && (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin size="large" />
                <div style={{ marginTop: '16px', color: '#666' }}>
                  正在调试执行...
                </div>
              </div>
            )}

            {debugResult && !debugging && (
              <div>
                <Alert
                  type={debugResult.success ? 'success' : 'error'}
                  message={debugResult.success ? '调试成功' : '调试失败'}
                  description={
                    debugResult.success
                      ? `执行时间: ${debugResult.executionTime}ms`
                      : debugResult.error
                  }
                  style={{ marginBottom: '16px' }}
                />

                {debugResult.success && debugResult.result && (
                  <div style={{
                    background: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    padding: '12px',
                    borderRadius: '4px',
                    fontFamily: 'Monaco, Consolas, monospace',
                    fontSize: '12px',
                    maxHeight: '150px',
                    overflow: 'auto'
                  }}>
                    <pre style={{ margin: 0 }}>
                      {JSON.stringify(debugResult.result, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}

            {!debugResult && !debugging && (
              <div style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px',
                border: '1px dashed #d9d9d9',
                borderRadius: '4px'
              }}>
                点击"调试"按钮开始调试执行
              </div>
            )}
          </Card>
        </div>

        {/* 右侧：变量编辑器 */}
        <div style={{ flex: 1, minWidth: '300px' }}>
          <VariableEditor
            variables={variables}
            onChange={setVariables}
          />
        </div>
      </div>
    </div>
  );
};

export default ScriptEditor;
