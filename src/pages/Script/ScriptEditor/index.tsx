import React, { useState, useEffect, useRef } from 'react';
import { Button, message, Typography, Space, Card, Modal } from 'antd';
import { SaveOutlined, PlayCircleOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import Editor from '@monaco-editor/react';
import VariableEditor from './VariableEditor';
import { Variable, ScriptData } from '@/types/script';

const { Title } = Typography;

const ScriptEditor: React.FC = () => {
  const navigate = useNavigate();
  const editorRef = useRef<any>(null);

  const [scriptContent, setScriptContent] = useState<string>(`// Groovy脚本示例
// 使用 $变量名 来引用变量，如 $userId, $userName

def processUser() {
    println "处理用户: \${$userId}"
    println "用户名: \${$userName}"

    // 你的业务逻辑
    def result = [
        userId: $userId,
        userName: $userName,
        processTime: new Date(),
        status: 'success'
    ]

    return result
}

// 执行主逻辑
return processUser()`);

  const [variables, setVariables] = useState<Variable[]>([
    {
      id: 'var_1',
      name: 'userId',
      displayName: '用户ID',
      type: 'string',
      defaultValue: '12345'
    },
    {
      id: 'var_2',
      name: 'userName',
      displayName: '用户名',
      type: 'string',
      defaultValue: '张三'
    }
  ]);

  const [saving, setSaving] = useState(false);

  // 编辑器挂载时的回调
  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // 配置编辑器主题和选项
    monaco.editor.defineTheme('groovy-theme', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'variable', foreground: '0066CC', fontStyle: 'bold' },
        { token: 'string', foreground: '008000' },
        { token: 'comment', foreground: '808080', fontStyle: 'italic' },
        { token: 'keyword', foreground: '0000FF', fontStyle: 'bold' }
      ],
      colors: {
        'editor.background': '#FFFFFF',
        'editor.lineHighlightBackground': '#F5F5F5'
      }
    });

    monaco.editor.setTheme('groovy-theme');

    // 添加变量高亮
    updateVariableHighlight(monaco);
  };

  // 更新变量高亮
  const updateVariableHighlight = (monaco: any) => {
    if (!monaco) return;

    // 创建变量名的正则表达式
    const variableNames = variables.map(v => v.name).filter(name => name.trim());
    if (variableNames.length === 0) return;

    const variablePattern = `\\$(?:${variableNames.join('|')})\\b`;

    // 注册语言规则
    monaco.languages.setMonarchTokensProvider('groovy', {
      tokenizer: {
        root: [
          [new RegExp(variablePattern), 'variable'],
          [/\/\/.*$/, 'comment'],
          [/".*?"/, 'string'],
          [/'.*?'/, 'string'],
          [/\b(def|class|if|else|for|while|return|import|package)\b/, 'keyword']
        ]
      }
    });
  };

  // 保存脚本
  const handleSave = async () => {
    try {
      setSaving(true);

      const scriptData: ScriptData = {
        name: '未命名脚本',
        content: scriptContent,
        variables: variables,
        updatedAt: new Date().toISOString()
      };

      // 模拟保存API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success('脚本保存成功！');
      console.log('保存的脚本数据:', scriptData);

    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 预览脚本
  const handlePreview = () => {
    if (!scriptContent.trim()) {
      message.warning('请先编写脚本内容');
      return;
    }

    // 跳转到执行页面，传递脚本内容和变量配置
    navigate('/script-execute', {
      state: {
        scriptContent,
        variables
      }
    });
  };

  // 监听变量变化，更新编辑器高亮
  useEffect(() => {
    if (editorRef.current) {
      const monaco = (window as any).monaco;
      if (monaco) {
        updateVariableHighlight(monaco);
      }
    }
  }, [variables]);

  return (
    <div style={{ padding: '24px', height: '100vh' ,width:'100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>Groovy 脚本编辑器</Title>
          <Space>
            <Button
              type="default"
              icon={<SaveOutlined />}
              loading={saving}
              onClick={handleSave}
            >
              {saving ? '保存中...' : '保存'}
            </Button>
            <Button
              type="primary"
              icon={<EyeOutlined />}
              onClick={handlePreview}
            >
              预览执行
            </Button>
          </Space>
        </div>
      </div>

      {/* 主体内容 */}
      <div style={{ flex: 1, display: 'flex', gap: '16px', minHeight: 0 }}>
        {/* 左侧：代码编辑器 */}
        <div style={{ flex: 2, display: 'flex', flexDirection: 'column' }}>
          <Card
            title="脚本编辑"
            style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, padding: 0 }}
          >
            <Editor
              height="100%"
              defaultLanguage="groovy"
              value={scriptContent}
              onChange={(value) => setScriptContent(value || '')}
              onMount={handleEditorDidMount}
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                wordWrap: 'on',
                folding: true,
                lineDecorationsWidth: 10,
                lineNumbersMinChars: 3,
                renderLineHighlight: 'line'
              }}
            />
          </Card>
        </div>

        {/* 右侧：变量编辑器 */}
        <div style={{ flex: 1, minWidth: '300px' }}>
          <VariableEditor
            variables={variables}
            onChange={setVariables}
          />
        </div>
      </div>
    </div>
  );
};

export default ScriptEditor;
