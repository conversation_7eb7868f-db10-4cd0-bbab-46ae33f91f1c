import React from 'react';
import { Form, Input, Select, Button, Space, Card, Typography } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { Variable } from '@/types/script';

const { Option } = Select;
const { Title } = Typography;

interface VariableEditorProps {
  variables: Variable[];
  onChange: (variables: Variable[]) => void;
}

const VariableEditor: React.FC<VariableEditorProps> = ({ variables, onChange }) => {
  const addVariable = () => {
    const newVariable: Variable = {
      id: `var_${Date.now()}`,
      name: '',
      displayName: '',
      type: 'string',
      defaultValue: ''
    };
    onChange([...variables, newVariable]);
  };

  const updateVariable = (index: number, field: keyof Variable, value: any) => {
    const newVariables = [...variables];
    newVariables[index] = { ...newVariables[index], [field]: value };
    onChange(newVariables);
  };

  const removeVariable = (index: number) => {
    const newVariables = variables.filter((_, i) => i !== index);
    onChange(newVariables);
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5} style={{ margin: 0 }}>变量配置</Title>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            size="small"
            onClick={addVariable}
          >
            添加变量
          </Button>
        </div>
      }
      size="small"
      style={{ height: '100%', overflow: 'auto' }}
    >
      <div style={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
        {variables.map((variable, index) => (
          <Card 
            key={variable.id} 
            size="small" 
            style={{ marginBottom: 8 }}
            extra={
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
                onClick={() => removeVariable(index)}
              />
            }
          >
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>变量名:</label>
                <Input
                  placeholder="如: userId"
                  value={variable.name}
                  onChange={(e) => updateVariable(index, 'name', e.target.value)}
                  size="small"
                  prefix="$"
                />
              </div>
              
              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>显示名:</label>
                <Input
                  placeholder="如: 用户ID"
                  value={variable.displayName}
                  onChange={(e) => updateVariable(index, 'displayName', e.target.value)}
                  size="small"
                />
              </div>
              
              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>类型:</label>
                <Select
                  value={variable.type}
                  onChange={(value) => updateVariable(index, 'type', value)}
                  size="small"
                  style={{ width: '100%' }}
                >
                  <Option value="string">字符串</Option>
                  <Option value="number">数字</Option>
                  <Option value="boolean">布尔值</Option>
                  <Option value="object">对象</Option>
                </Select>
              </div>
              
              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>默认值:</label>
                <Input
                  placeholder="默认值"
                  value={variable.defaultValue}
                  onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                  size="small"
                />
              </div>
            </Space>
          </Card>
        ))}
        
        {variables.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            color: '#999', 
            padding: '20px',
            border: '1px dashed #d9d9d9',
            borderRadius: '4px'
          }}>
            暂无变量，点击上方"添加变量"按钮开始配置
          </div>
        )}
      </div>
    </Card>
  );
};

export default VariableEditor;
