import React from 'react';
import { Input, Select, Card, Typography, Space, Tag, Button, Radio } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import type { Variable } from '@/types/script';

const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface VariableEditorProps {
  variables: Variable[];
  onChange: (variables: Variable[]) => void;
}

const VariableEditor: React.FC<VariableEditorProps> = ({ variables, onChange }) => {
  const updateVariable = (index: number, field: keyof Variable, value: any) => {
    const newVariables = [...variables];
    newVariables[index] = { ...newVariables[index], [field]: value };

    // 如果改变类型，重置相关字段
    if (field === 'type') {
      if (value === 'select' || value === 'radio') {
        newVariables[index].options = [{ label: '选项1', value: 'option1' }];
      } else {
        delete newVariables[index].options;
      }
      // 重置默认值
      newVariables[index].defaultValue = '';
    }

    onChange(newVariables);
  };

  const addOption = (variableIndex: number) => {
    const newVariables = [...variables];
    const variable = newVariables[variableIndex];
    if (!variable.options) {
      variable.options = [];
    }
    variable.options.push({
      label: `选项${variable.options.length + 1}`,
      value: `option${variable.options.length + 1}`
    });
    onChange(newVariables);
  };

  const updateOption = (variableIndex: number, optionIndex: number, field: 'label' | 'value', value: string) => {
    const newVariables = [...variables];
    const variable = newVariables[variableIndex];
    if (variable.options && variable.options[optionIndex]) {
      variable.options[optionIndex][field] = value;
      onChange(newVariables);
    }
  };

  const removeOption = (variableIndex: number, optionIndex: number) => {
    const newVariables = [...variables];
    const variable = newVariables[variableIndex];
    if (variable.options) {
      variable.options.splice(optionIndex, 1);
      onChange(newVariables);
    }
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5} style={{ margin: 0 }}>变量配置</Title>
          <Tag color="blue">{variables.length} 个变量</Tag>
        </div>
      }
      size="small"
      style={{ height: '100%', overflow: 'auto' }}
    >
      <div style={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
        {variables.map((variable, index) => (
          <Card
            key={variable.id}
            size="small"
            style={{ marginBottom: 8 }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <div>
                <Text strong style={{ color: '#1890ff' }}>${variable.name}</Text>
              </div>

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>显示名:</label>
                <Input
                  placeholder="如: 用户ID"
                  value={variable.displayName}
                  onChange={(e) => updateVariable(index, 'displayName', e.target.value)}
                  size="small"
                />
              </div>

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>类型:</label>
                <Select
                  value={variable.type}
                  onChange={(value) => updateVariable(index, 'type', value)}
                  size="small"
                  style={{ width: '100%' }}
                >
                  <Option value="input">文本输入</Option>
                  <Option value="number">数字</Option>
                  <Option value="boolean">布尔值</Option>
                  <Option value="select">下拉选择</Option>
                  <Option value="radio">单选按钮</Option>
                  <Option value="textarea">多行文本</Option>
                </Select>
              </div>

              {/* 选项配置 - 仅对 select 和 radio 类型显示 */}
              {(variable.type === 'select' || variable.type === 'radio') && (
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                    <label style={{ fontSize: '12px', color: '#666' }}>选项配置:</label>
                    <Button
                      type="dashed"
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={() => addOption(index)}
                    >
                      添加选项
                    </Button>
                  </div>
                  {variable.options?.map((option, optionIndex) => (
                    <div key={optionIndex} style={{ display: 'flex', gap: '4px', marginBottom: '4px' }}>
                      <Input
                        placeholder="显示文本"
                        value={option.label}
                        onChange={(e) => updateOption(index, optionIndex, 'label', e.target.value)}
                        size="small"
                        style={{ flex: 1 }}
                      />
                      <Input
                        placeholder="值"
                        value={option.value}
                        onChange={(e) => updateOption(index, optionIndex, 'value', e.target.value)}
                        size="small"
                        style={{ flex: 1 }}
                      />
                      <Button
                        type="text"
                        danger
                        size="small"
                        icon={<DeleteOutlined />}
                        onClick={() => removeOption(index, optionIndex)}
                      />
                    </div>
                  ))}
                </div>
              )}

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>默认值:</label>
                {variable.type === 'boolean' ? (
                  <Select
                    value={variable.defaultValue}
                    onChange={(value) => updateVariable(index, 'defaultValue', value)}
                    size="small"
                    style={{ width: '100%' }}
                  >
                    <Option value={true}>是</Option>
                    <Option value={false}>否</Option>
                  </Select>
                ) : variable.type === 'select' || variable.type === 'radio' ? (
                  <Select
                    value={variable.defaultValue}
                    onChange={(value) => updateVariable(index, 'defaultValue', value)}
                    size="small"
                    style={{ width: '100%' }}
                    placeholder="选择默认值"
                  >
                    {variable.options?.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                ) : variable.type === 'textarea' ? (
                  <TextArea
                    placeholder="默认值"
                    value={variable.defaultValue}
                    onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                    size="small"
                    rows={2}
                  />
                ) : (
                  <Input
                    placeholder="默认值"
                    value={variable.defaultValue}
                    onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                    size="small"
                    type={variable.type === 'number' ? 'number' : 'text'}
                  />
                )}
              </div>
            </Space>
          </Card>
        ))}

        {variables.length === 0 && (
          <div style={{
            textAlign: 'center',
            color: '#999',
            padding: '20px',
            border: '1px dashed #d9d9d9',
            borderRadius: '4px'
          }}>
            在脚本中使用 $变量名 格式，系统会自动识别并生成变量配置
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              例如: $userId, $userName
            </Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default VariableEditor;
