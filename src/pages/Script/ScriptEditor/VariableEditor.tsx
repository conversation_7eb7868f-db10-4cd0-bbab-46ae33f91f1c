import React from 'react';
import { Input, Select, Card, Typography, Space, Tag } from 'antd';
import type { Variable } from '@/types/script';

const { Option } = Select;
const { Title, Text } = Typography;

interface VariableEditorProps {
  variables: Variable[];
  onChange: (variables: Variable[]) => void;
}

const VariableEditor: React.FC<VariableEditorProps> = ({ variables, onChange }) => {
  const updateVariable = (index: number, field: keyof Variable, value: any) => {
    const newVariables = [...variables];
    newVariables[index] = { ...newVariables[index], [field]: value };
    onChange(newVariables);
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={5} style={{ margin: 0 }}>变量配置</Title>
          <Tag color="blue">{variables.length} 个变量</Tag>
        </div>
      }
      size="small"
      style={{ height: '100%', overflow: 'auto' }}
    >
      <div style={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
        {variables.map((variable, index) => (
          <Card
            key={variable.id}
            size="small"
            style={{ marginBottom: 8 }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <div>
                <Text strong style={{ color: '#1890ff' }}>${variable.name}</Text>
              </div>

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>显示名:</label>
                <Input
                  placeholder="如: 用户ID"
                  value={variable.displayName}
                  onChange={(e) => updateVariable(index, 'displayName', e.target.value)}
                  size="small"
                />
              </div>

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>类型:</label>
                <Select
                  value={variable.type}
                  onChange={(value) => updateVariable(index, 'type', value)}
                  size="small"
                  style={{ width: '100%' }}
                >
                  <Option value="string">字符串</Option>
                  <Option value="number">数字</Option>
                  <Option value="boolean">布尔值</Option>
                  <Option value="object">对象</Option>
                </Select>
              </div>

              <div>
                <label style={{ fontSize: '12px', color: '#666' }}>默认值:</label>
                <Input
                  placeholder="默认值"
                  value={variable.defaultValue}
                  onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                  size="small"
                />
              </div>
            </Space>
          </Card>
        ))}

        {variables.length === 0 && (
          <div style={{
            textAlign: 'center',
            color: '#999',
            padding: '20px',
            border: '1px dashed #d9d9d9',
            borderRadius: '4px'
          }}>
            在脚本中使用 $变量名 格式，系统会自动识别并生成变量配置
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              例如: $userId, $userName
            </Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default VariableEditor;
