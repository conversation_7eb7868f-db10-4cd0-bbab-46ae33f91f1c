import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Typography, Space, Alert, Spin, InputNumber, Switch, Select, Radio } from 'antd';
import { PlayCircleOutlined, ArrowLeftOutlined, CopyOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import type { Variable, ExecuteResult } from '@/types/script';
import <PERSON>sonViewer from '@/components/JsonViewer';

const { Title, Text } = Typography;
const { TextArea } = Input;

const ScriptExecute: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [scriptContent, setScriptContent] = useState<string>('');
  const [variables, setVariables] = useState<Variable[]>([]);
  const [executing, setExecuting] = useState(false);
  const [result, setResult] = useState<ExecuteResult | null>(null);

  useEffect(() => {
    // 从路由参数中获取脚本内容和变量配置
    const state = location.state as { scriptContent?: string; variables?: Variable[] };
    if (state) {
      setScriptContent(state.scriptContent || '');
      setVariables(state.variables || []);
      
      // 设置表单默认值
      const initialValues: Record<string, any> = {};
      state.variables?.forEach(variable => {
        initialValues[variable.name] = variable.defaultValue;
      });
      form.setFieldsValue(initialValues);
    }
  }, [location.state, form]);

  const executeScript = async () => {
    try {
      setExecuting(true);
      setResult(null);
      
      const formValues = await form.validateFields();
      
      // 模拟脚本执行 - 实际项目中这里应该调用后端API
      const startTime = Date.now();
      
      // 模拟异步执行
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      const executionTime = Date.now() - startTime;
      
      // 模拟执行结果
      const mockResult = {
        success: Math.random() > 0.3, // 70% 成功率
        result: Math.random() > 0.3 ? {
          message: '脚本执行成功',
          data: {
            processedItems: Math.floor(Math.random() * 100),
            timestamp: new Date().toISOString(),
            variables: formValues
          }
        } : undefined,
        error: Math.random() > 0.3 ? undefined : '脚本执行失败：变量 $userId 未定义',
        executionTime
      };
      
      setResult(mockResult);
    } catch (error) {
      setResult({
        success: false,
        error: '表单验证失败，请检查输入参数'
      });
    } finally {
      setExecuting(false);
    }
  };

  const renderVariableInput = (variable: Variable) => {
    const commonProps = {
      placeholder: `请输入${variable.displayName}`,
      size: 'middle' as const
    };

    switch (variable.type) {
      case 'number':
        return <InputNumber {...commonProps} style={{ width: '100%' }} />;
      case 'boolean':
        return <Switch checkedChildren="是" unCheckedChildren="否" />;
      case 'select':
        return (
          <Select {...commonProps} style={{ width: '100%' }}>
            {variable.options?.map((option) => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        );
      case 'radio':
        return (
          <Radio.Group>
            {variable.options?.map((option) => (
              <Radio key={option.value} value={option.value}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        );
      case 'textarea':
        return <Input.TextArea {...commonProps} rows={3} />;
      case 'input':
      default:
        return <Input {...commonProps} />;
    }
  };

  return (
    <div style={{ padding: '24px', width: '100%', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate(-1)}
          style={{ marginBottom: '16px' }}
        >
          返回编辑器
        </Button>
        <Title level={2}>脚本执行</Title>
      </div>

      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        {/* 参数输入 */}
        <Card title="执行参数" style={{ marginBottom: '24px' }}>
          {variables.length > 0 ? (
            <Form
              form={form}
              layout="vertical"
              onFinish={executeScript}
            >
              {variables.map(variable => (
                <Form.Item
                  key={variable.id}
                  label={
                    <Space>
                      <Text strong>{variable.displayName}</Text>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        [{variable.type}]
                      </Text>
                    </Space>
                  }
                  name={variable.name}
                  rules={[
                    { required: true, message: `请输入${variable.displayName}` }
                  ]}
                >
                  {renderVariableInput(variable)}
                </Form.Item>
              ))}

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<PlayCircleOutlined />}
                  loading={executing}
                  size="large"
                  block
                >
                  {executing ? '执行中...' : '执行脚本'}
                </Button>
              </Form.Item>
            </Form>
          ) : (
            <div style={{
              textAlign: 'center',
              color: '#999',
              padding: '40px',
              border: '1px dashed #d9d9d9',
              borderRadius: '4px'
            }}>
              该脚本没有配置变量参数
              <br />
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={executeScript}
                loading={executing}
                style={{ marginTop: '16px' }}
              >
                {executing ? '执行中...' : '执行脚本'}
              </Button>
            </div>
          )}
        </Card>

        {/* 执行结果 */}
        <Card title="执行结果">
          {executing && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Spin size="large" />
              <div style={{ marginTop: '16px', color: '#666' }}>
                脚本执行中，请稍候...
              </div>
            </div>
          )}

          {result && !executing && (
            <div>
              {result.success && result.result && (
                <div style={{ marginBottom: '16px' }}>
                  <Text strong style={{ fontSize: '16px', marginBottom: '12px', display: 'block' }}>
                    执行结果:
                  </Text>
                  <JsonViewer data={result.result} title="JSON 结果" />
                </div>
              )}

              <Alert
                type={result.success ? 'success' : 'error'}
                message={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{result.success ? '执行成功' : '执行失败'}</span>
                    {result.success && result.executionTime && (
                      <span style={{ fontSize: '12px', opacity: 0.8 }}>
                        执行时间: {result.executionTime}ms
                      </span>
                    )}
                  </div>
                }
                description={!result.success ? result.error : undefined}
              />
            </div>
          )}

          {!result && !executing && (
            <div style={{
              textAlign: 'center',
              color: '#999',
              padding: '40px',
              border: '1px dashed #d9d9d9',
              borderRadius: '4px'
            }}>
              点击"执行脚本"按钮开始执行
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ScriptExecute;
