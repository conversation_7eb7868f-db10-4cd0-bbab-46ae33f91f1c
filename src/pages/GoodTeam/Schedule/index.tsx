import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {forEach, groupBy} from 'lodash';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
    Space,
    Table,
    Tooltip,
    message
} from 'antd';
import {
    FrownOutlined,
    LeftOutlined,
    MinusCircleOutlined,
    PlusOutlined,
    RightOutlined,
    SmileOutlined,
    SwapOutlined
} from '@ant-design/icons';
import moment from 'moment'; // 引入 moment
import zhCN from 'antd/lib/locale/zh_CN'; // 引入中文语言包
import 'moment/locale/zh-cn'; // 引入 moment 的中文语言包
import './Table.css';
import './index.css';
import EditableCell from "./EditableCell";
import dayjs from "dayjs";
import 'dayjs/locale/zh-cn';

import * as API from '@/common/API/TeamScheduleAPI';
import Legend from "./Legend";
import 'braft-editor/dist/index.css';
import ContentUtils from "braft-editor";
import TextArea from "antd/es/input/TextArea";
import ReactDOMServer from 'react-dom/server';
import {Column} from "@ant-design/charts";
import {useLocation, useNavigate} from 'react-router-dom';
import { Flex } from 'antd';


// 设置 moment 的周起始日为周一
moment.updateLocale('zh-cn', {
    week: {
        dow: 1, // 周一是一周的第一天
    },
});

const smileIcon = <SmileOutlined/>;
const cryIcon = <FrownOutlined/>;


const isBetween = require('dayjs/plugin/isBetween')
const weekOfYear = require('dayjs/plugin/weekOfYear');
dayjs.extend(isBetween)
dayjs.locale('zh-cn');
dayjs.extend(weekOfYear);

const detailTypes = [
    {"type": "PRD", "keyword": "docs.corp.kuaishou.com","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/prd15.png"},
    {"type": "技术方案", "keyword": "","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/jishufangan3.png"},
    {"type": "提测文档", "keyword": "","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/test3.png"},
    {"type": "Kconf", "keyword": "kconf.corp.kuaishou.com","matchRegex":/\/([^/?]+)(?:\?|$)/,"icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/kconf.png"},
    {"type": "Kswitch", "keyword": "kswitch.corp.kuaishou.com","matchRegex":/[?&]key=([^&]+)/,"icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/kswitch.png"},
    {"type": "监控", "keyword": "tianwen.corp.kuaishou.com/dashboard","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/monitor2.png"},
    {"type": "测试报告", "keyword": "","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/test-report3.png"},
    {"type": "实验参数", "keyword": "abtest.corp.kuaishou.com","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/abtest.png"},
    {"type": "实验反馈", "keyword": "","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/abtest.png"},
    {"type": "Excel", "keyword": "","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/excel3.png"},
    {"type": "Doc", "keyword": "" ,"icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/doc3.png"},
    {"type": "其他", "keyword": "","icon":"https://w2.eckwai.com/udata/pkg/eshop/c-ai-dev/team/other3.png"}
];

// 将JSON字符串转换为JSX链接元素
const convertJsonStringToLinks = (jsonString) => {
    if (!jsonString || typeof jsonString !== 'string') {
        return null;
    }

    try {
        // 第一步：将字符串转换为JSON对象数组
        const jsonArray = JSON.parse(jsonString);

        if (!Array.isArray(jsonArray)) {
            return null;
        }
        // 第二步：将JSON对象数组转换为JSX链接元素
        return jsonArray.map((item, index) => {
            const { type, link, title } = item;
            let icon: string | null = null;
            detailTypes.forEach(detailType => {
                if (detailType.type === type) {
                    icon = detailType.icon;
                }
            });

            if(!link || link =="" ){
                return ;
            }

            return (
                <div key={index}>
                    <a href={link} target="_blank" rel="noreferrer" style={{ display: 'inline-flex', alignItems: 'center', flexWrap: 'nowrap', maxWidth: '100%' }}>
                        <img src={icon??""} alt={type} style={{ width: '16px', height: '16px', marginRight: '2px', flexShrink: 0 }} />
                        <Tooltip title = {title?? type+"链接"} >
                        <span style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{title?? type+"链接"}</span>
                        </Tooltip>
                    </a>
                </div>
            );
        });
    } catch (error) {
        console.error('JSON解析错误:', error);
        return null;
    }
};

moment.locale('zh-cn'); // 设置 moment 的语言为中文

const {RangePicker} = DatePicker;
const {Option} = Select;

const firstDayOfWeek = moment()?.subtract?.(1, 'week')?.startOf?.('week');
const lastDayOfWeek = moment()?.endOf?.('week');

const formItemLayout = {
    labelCol: {
        span: 8,
        // xs: { span: 24 },
        // sm: { span: 4 },
    },
    wrapperCol: {
        span: 15,
        // xs: { span: 24 },
        // sm: { span: 20 },
    },
};
const formItemLayoutWithOutLabel = {
    wrapperCol: {
        offset: 8,
        span: 15,
    },
};

// 计算日期范围的函数，使用 moment 实现
const getDateRange = (option: string | null) => {
    const now = moment();
    switch (option) {
        case 'currentWeek':
            return [now?.clone()?.startOf?.('week'), now?.clone()?.endOf?.('week')];
        case 'before1WeekToCurrent':
            return [now?.clone()?.subtract?.(1, 'week')?.startOf?.('week'), now?.clone()?.endOf?.('week')];
        case 'currentToAfter1Week':
            return [now?.clone()?.startOf?.('week'), now?.clone()?.add?.(1, 'week')?.endOf?.('week')];
        case 'currentMonth':
            // 获取当前月份区间
            const startOfMonth = now?.clone()?.startOf?.('month');
            const endOfMonth = now?.clone()?.endOf?.('month');
            return [startOfMonth, endOfMonth];
        case 'currentYear':
            // 获取当前年份区间
            const startOfYear = now?.clone()?.startOf?.('year');
            const endOfYear = now?.clone()?.endOf?.('year');
            return [startOfYear, endOfYear];
        default:
            //默认是current
            return [now?.clone()?.startOf?.('week'), now?.clone()?.endOf?.('week')];
    }
};
const [initStartDate, initEndDate] = getDateRange(localStorage.getItem('dateSelectValue'));




const ScheduleTable = () => {
        const [startDate, setStartDate] = useState(initStartDate); // 以周一为起始日
        const [endDate, setEndDate] = useState(initEndDate); // 以周日为结束日
        const [dataSource, setDataSource] = useState([]);
        const teamMemberObjectList = useRef([]);
        const categoryList = useRef([]);
        const [stageStatus, setStageStatus] = useState([]);
        const [statusColorMap, setStatusColorMap] = useState({});
        const [stageDescMap, setStageDescMap] = useState({});
        const [holidays, setHolidays] = useState([]);
        const [isInsightModalVisible, setIsInsightModalVisible] = useState(false);
        const [dateSelectValue, setDateSelectValue] = useState(() => {
            return localStorage.getItem('dateSelectValue') || 'currentWeek';
        });

        const [kimGroupOptions, setKimGroupOptions] = useState([]);
        const [filters, setFilters] = useState({});
        const [sorter, setSorter] = useState({});

        const legendStatusColorMap = {
            '开发': '#f4d9d9',
            '联调': '#f2cde4',
            '测试': '#fcf8c0',
            '发布': '#e4f7d6',
            '放量': '#6db259',
        };

        // @ts-ignore
        const teamMemberList = useMemo(() => teamMemberObjectList.current?.map(item => item.name) ?? [], [teamMemberObjectList.current?.length]);

        // @ts-ignore
        const shortNameListOptions = useMemo(() => dataSource?.map(item => item.shortName) ?? [], [dataSource?.length]);


        const getQueryParam = (paramName: string) => {
            // 获取当前页面的 URL
            const url = window.location.href;
            // 创建 URLSearchParams 对象
            const urlParams = new URLSearchParams(new URL(url).search);
            // 获取指定参数的值
            return urlParams.get(paramName);
        }

        const teamGroup = getQueryParam('teamGroup') ?? 'xhc';

        // 获取一周的日期范围（周一到周日）

        const getWeekDates = (start: moment.Moment | undefined, end: moment.Moment | undefined) => {
            // @ts-ignore
            const startDate = new moment(start.format('YYYY-MM-DD'));
            // @ts-ignore
            const endDate = new moment(end.format('YYYY-MM-DD'));
            const dates = [];
            const currentDate = startDate.clone();
            while (!currentDate.isAfter(endDate, 'day')) {
                // @ts-ignore
                dates.push(new moment(currentDate.format('YYYY-MM-DD')));
                currentDate.add(1, 'days');
            }
            return dates;
        };

        const [editingKey, setEditingKey] = useState('');

        const isEditing = useCallback((record) => {
            return record.key === editingKey;
        }, [editingKey]);

        const handleDoubleClick = (record) => {
            if (editingKey !== record.key) {
                edit(record);
            }
        };
        const [form] = Form.useForm();
        const edit = (record) => {
            form.setFieldsValue({
                name: '',
                status: '',
                ...record,
            });
            setEditingKey("" + record.key);
        };

        let currentData: any = null;
// 添加明确的类型注解，符合 Table 组件的 summary 属性要求
        const teamSummary = (pageData: readonly any[]): React.ReactNode => {
            currentData = pageData;
            // 返回 null 或者空的 React 节点，以满足 ReactNode 类型要求
            return null;
        }


        function getWeekStageCountObj(record,weekNum : number = -1){
            const stages = ['dev', 'integration', 'test', 'publish', 'gray'];
            // 用于记录已经统计过的日期
            const countedDates = new Set();
            // 用于存储每周投入的天数
            const weeklyDays = {};
            // 遍历每个阶段
            stages.forEach(stage => {
                if(record.shortName == '直播电商-福利购支持双任务设置' && stage == 'dev'){
                  debugger
                }

                if(!record?.schedule?.[stage]?.['startDate'] || !record?.schedule?.[stage]?.['endDate']){
                    return ;
                }
                // @ts-ignore
                const sDate = new dayjs(record?.schedule?.[stage]?.['startDate']);
                // @ts-ignore
                const eDate = new dayjs(record?.schedule?.[stage]?.['endDate']);

                // 遍历日期范围
                // @ts-ignore
                let currentDate = new dayjs(sDate);
                let countStartDate = startDate ;
                let countEndDate = startDate ;

                if(isMomentObject(startDate)){
                    // @ts-ignore
                    countStartDate = dayjs(startDate.toDate());
                }
                if(isMomentObject(endDate)){
                    // @ts-ignore
                    countEndDate = dayjs(endDate?.toDate());
                }

                while (currentDate <= eDate && currentDate >= countStartDate && currentDate <= countEndDate ) {
                    const dateKey = currentDate?.toISOString()?.split?.('T')?.[0];
                    // debugger
                    // console.log(record.shortName,currentDate.format('YYYY-MM-DD'),startDate.format('YYYY-MM-DD'),endDate.format('YYYY-MM-DD'))
                    // 如果该日期还没有被统计过，并且是工作日或者加班日
                    if (!countedDates.has(dateKey) && (isWorkday(currentDate) || isOvertime(currentDate, record.extraTime.map(dateString => dayjs(dateString,'YYYY-MM-DD'))))) {
                        // 获取当前日期所在的周数
                        const weekNumber = getWeekNumber(currentDate);
                        //如果指定了周数，则只统计指定周数的投入天数
                        if(weekNumber != weekNum){
                            currentDate = currentDate.add(1, 'day');
                            continue;
                        }
                        // 如果该周数还没有记录，初始化投入天数为 0
                        if (!weeklyDays[stage]) {
                            weeklyDays[stage] = 0;
                        }
                        // 增加该周的投入天数
                        weeklyDays[stage]++;
                        // 标记该日期已被统计
                        countedDates.add(dateKey);
                    }
                    // 移动到下一天
                    currentDate = currentDate.add(1, 'day');                }
            });
            return  weeklyDays;
        }

        function getWeekCountObj(record,weekNum : number = -1){
            const stages = ['dev', 'integration', 'test', 'publish', 'gray'];
            // 用于记录已经统计过的日期
            const countedDates = new Set();
            // 用于存储每周投入的天数
            const weeklyDays = {};
            // 遍历每个阶段
            stages.forEach(stage => {

                if(!record?.schedule?.[stage]?.['startDate'] || !record?.schedule?.[stage]?.['endDate']){
                    return ;
                }
                // @ts-ignore
                const sDate = new dayjs(record?.schedule?.[stage]?.['startDate']);
                // @ts-ignore
                const eDate = new dayjs(record?.schedule?.[stage]?.['endDate']);

                // 遍历日期范围
                // @ts-ignore
                let currentDate = new dayjs(sDate);
                while (currentDate <= eDate && currentDate >= startDate && currentDate <= endDate ) {
                    const dateKey = currentDate?.toISOString()?.split?.('T')?.[0];

                    // 如果该日期还没有被统计过，并且是工作日或者加班日
                    if (!countedDates.has(dateKey) && (isWorkday(currentDate) || isOvertime(currentDate, record.extraTime.map(dateString => dayjs(dateString,'YYYY-MM-DD'))))) {
                        // 获取当前日期所在的周数
                        const weekNumber = getWeekNumber(currentDate);
                        //如果指定了周数，则只统计指定周数的投入天数
                        if(weekNum != -1 && weekNumber != weekNum){
                            continue;
                        }
                        // 如果该周数还没有记录，初始化投入天数为 0
                        if (!weeklyDays[weekNumber]) {
                            weeklyDays[weekNumber] = 0;
                        }
                        // 增加该周的投入天数
                        weeklyDays[weekNumber]++;
                        // 标记该日期已被统计
                        countedDates.add(dateKey);
                    }
                    // 移动到下一天
                    currentDate = currentDate.add(1, 'day');                }
            });
            return  weeklyDays;
        }


        const getInsightData = () => {
            let insightData = [] ;
            const map = new Map();
            const now = moment();
            const currentWeek = now.week(); // 获取当前是第几周
            currentData.forEach((record: any) => {
                const { owner, schedule } = record;
                if(!owner || owner.length == 0){
                    return;
                }
                const weekCountObj = getWeekStageCountObj(record,currentWeek);
                Object.keys(weekCountObj).map((stage) => {
                    const member =  owner?.[0]
                    const key = member+"-"+stage
                    let memberCntObj = map.get(key);
                    if(!memberCntObj) {
                        memberCntObj = {} ;
                    }
                    if(!memberCntObj["member"]){
                        memberCntObj["member"] = member;
                    }
                    if(!memberCntObj["stage"]){
                        memberCntObj["stage"] = stageDescMap[stage];
                    }

                    if(!memberCntObj["cnt"]){
                        memberCntObj["cnt"] = 0;
                    }
                    memberCntObj["cnt"] = memberCntObj["cnt"] + weekCountObj[stage];
                    map.set(key, memberCntObj);
                })
            });
            // @ts-ignore
            // 创建一个阶段顺序映射，基于stageDescMap的顺序
            const stageOrder = {};
            const stageKeys = Object.keys(stageDescMap);
            stageKeys.forEach((key, index) => {
                stageOrder[stageDescMap[key]] = index;
            });
            // 按照stageDescMap定义的顺序排序
            // @ts-ignore
            insightData = Array.from(map.values()).sort((a, b) => {
                // @ts-ignore
                const aIndex = teamMemberObjectList.current?.findIndex((user) => user.name === a.member);
                // @ts-ignore
                const bIndex = teamMemberObjectList.current?.findIndex((user) => user.name === b.member);
                if( aIndex != bIndex ){
                    return aIndex - bIndex;
                }

                return stageOrder[a.stage] - stageOrder[b.stage];
            });

            const annotations = [];
            forEach(groupBy(insightData, 'member'), (values, k) => {
                // @ts-ignore
                const value = values.reduce((a, b) => a + b.cnt, 0);
                // @ts-ignore
                annotations.push({
                    type: 'text',
                    data: [k, value],
                    style: {
                        textAlign: 'center',
                        fontSize: 14,
                        fill: 'rgba(0,0,0,0.85)',
                    },
                    xField: 'member',
                    yField: 'cnt',
                    // @ts-ignore
                    style: {
                        text: `${value}`,
                        textBaseline: 'bottom',
                        position: 'top',
                        textAlign: 'center',
                    },
                    tooltip: false,
                });
            });

            return {insightData:insightData,annotations:annotations};
        }


        const insightColorMap = {
            '开发': '#ea9999',
            '联调': '#8e7cc3',
            '测试': '#f1c232',
            '发布': '#70b452',
            '放量': '#659c4c',
        };
        const InsightStackedColumn = () => {
            const {insightData,annotations} = getInsightData();
            const config = {
                data: insightData,
                xField: 'member',
                yField: 'cnt',
                stack: true,
                colorField: 'stage',
                label: {
                    text: 'stage',
                    textBaseline: 'bottom',
                    position: 'inside',
                },
                style: {
                    fill: ({stage}) => {
                        let color =  insightColorMap[stage];
                        if (!color) {
                            color = '#1890ff';
                        }
                        return color ;
                    }
                },
                annotations,
            };
            return <Column {...config} />;
        };


        const renderScheduleStage = (text, record) => {
            const schedule = record.schedule;
            const scheduleNote = record.scheduleNote;

            if (!schedule && !scheduleNote) {
                return <div></div>
            }


            return <div style={{fontSize: 12}}>
                {
                    // @ts-ignore
                    schedule && Object.keys(schedule).map((stage) => {
                        if (schedule[stage]?.startDate) {
                            return <div key={stage}>
                                <div>{/* @ts-ignore */}{stageDescMap[stage]} : {schedule[stage]?.startDate?.substring?.(5, 10)}~{schedule[stage]?.endDate?.substring?.(5, 10)}</div>
                            </div>
                        }
                    })
                }
                <div>
                    {scheduleNote}
                </div>
            </div>
        }

        const renderOwnerColumn = (value, record, showAvatar) => {
            if (!value) {
                return;
            }

            let ownerDisplay = value?.map((owner) => {
                return <div>
                    <span style={{color: 'blue'}}>@{owner}</span>
                </div>;
            });

            if (showAvatar) {
                ownerDisplay = value?.map((owner) => {
                    return <div style={{display: 'flex', justifyContent: 'flex - start'}}><img
                        src={getUserAvatarByName(owner)}
                        alt="Avatar"
                        style={{
                            width: '18px',
                            height: '18px',
                            borderRadius: '50%',
                            marginRight: '5px'
                        }}
                    />
                        <span style={{color: 'blue'}}>{owner}</span>
                    </div>;
                });
            }

            return <div>
                {ownerDisplay}
            </div>

        }

        const renderDateColumnContent = (text, record, date) => {
            const {owner, schedule,extraTime} = record;
            if (!schedule) {
                return ''; // 如果 schedule 为 undefined 或者 null，返回空字符串
            }
            const stage = getScheduleStage(date, schedule,extraTime);
            return <div>
                {/* @ts-ignore */}
                {stageDescMap[stage]} <br/>
            </div>
        }

        const renderDateColumnStyle = (record, date) => {
            const cellStyle = {
                style: {
                    borderLeft: date.isSame(moment(), 'day') ? '2px solid blue' : '', // 设置左边框颜色
                    borderRight: date.isSame(moment(), 'day') ? '2px solid blue' : '', // 设置右边框颜色
                    paddingLeft: 0,
                    paddingRight: 0,
                }
            };

            const schedule = record.schedule;
            if (!schedule) {
                return cellStyle; // 如果 schedule 为 undefined 或者 null，返回空字符串
            }
            const stage = getScheduleStage(date, schedule,record.extraTime);
            // @ts-ignore
            cellStyle.style['backgroundColor'] = statusColorMap[stage] || 'white';

            return cellStyle;
        }

// 定义进度状态与背景颜色的映射关系


        function getStyle(record) {
            const backgroundColor = statusColorMap[record.status] || 'white';
            return {
                style: {
                    // 根据条件设置行的背景颜色，这里假设 age 大于 40 的行背景颜色为 #f0f9eb
                    backgroundColor: backgroundColor,
                }
            };
        }


        function getCategoryStyle(record) {
            const backgroundColor = statusColorMap[record.category] || 'white';
            return {
                style: {
                    // 根据条件设置行的背景颜色，
                    backgroundColor: backgroundColor,
                }
            };
        }

        const chineseWeekdays = ['日', '一', '二', '三', '四', '五', '六'];

        function getHolidayName(date) {
            for (let i = 0; i < holidays.length; i++) {
                const holidayObj = holidays[i];
                // @ts-ignore
                if (holidayObj.holidayArrangement?.includes?.(date)) {
                    // @ts-ignore
                    return holidayObj.holiday;
                }
            }
            return null;
        }

        function isAdjustedDay(date) {
            for (let i = 0; i < holidays.length; i++) {
                const holidayObj = holidays[i];
                // @ts-ignore
                if (holidayObj.adjustedDay?.includes?.(date)) {
                    return true;
                }
            }
            return false;
        }

        function getDateColumnTitle(date) {
            const dayNumber = date.day();
            const holidayName = getHolidayName(date.format('YYYY-MM-DD'));
            let bottomDisplay = chineseWeekdays[dayNumber];
            let color: string | null = null;
            if (holidayName) {
                bottomDisplay = holidayName;
                // @ts-ignore
                color = 'green';
            }
            if (isAdjustedDay(date.format('YYYY-MM-DD'))) {
                bottomDisplay = "班";
                // @ts-ignore
                color = 'red';
            }
            return <div>{date.format('MM/DD')}
                <br/>
                {/* @ts-ignore */}
                <div style={{fontSize: 'small', textAlign: 'center', color: color}}>({bottomDisplay})</div>
            </div>
        }

        function handleDelete(deleteRecord) {
            Modal.confirm({
                title: '确认删除',
                content: '你确定要删除这条记录吗？',
                okText: '确认',
                okType: 'danger',
                cancelText: '取消',
                onOk() {
                    API.scheduleDelete({
                        id: deleteRecord.id
                    }).then(() => {
                        const newDataSource = dataSource.filter((oldRecord) =>
                             // @ts-ignore
                            oldRecord.key != deleteRecord.key
                        );
                        setDataSource(newDataSource);
                    });
                },
                onCancel() {
                    console.log('取消删除');
                },
            });
        }

        const getUserAvatarByName = useCallback((name) => {
            // @ts-ignore
            const user = teamMemberObjectList.current?.find((user) => user.name === name);
            // @ts-ignore
            return user ? user.avatar : null;
        }, [teamMemberObjectList.current?.length])

        const sortCategory = useCallback((a, b) => {
            // @ts-ignore
            if (categoryList.current?.indexOf?.(a.category) == categoryList.current?.indexOf?.(b.category)) {
                return 0;
            }
            // @ts-ignore
            return categoryList.current?.indexOf?.(a.category) - categoryList.current?.indexOf?.(b.category);
        }, [categoryList.current?.length])

        const sortSchedule = (a, b, order) => {
            // sort by the first day of "dev" stage
            const aDevDate = a.schedule?.dev?.startDate;
            const bDevDate = b.schedule?.dev?.startDate;
            // undefined date should always at the end no matter what order is
            if (aDevDate == undefined) {
                return order == 'ascend' ? 1 : -1;
            }
            if (bDevDate == undefined) {
                return order == 'ascend' ? -1 : 1;
            }
            if (aDevDate == bDevDate) {
                return 0;
            }
            // sort date string
            return aDevDate.localeCompare(bDevDate);
        }

        const sortStatus = (a, b) => {
            // @ts-ignore
            const aStatus = stageStatus?.filter((s) => s.label == a.status)?.[0];
            // @ts-ignore
            const bStatus = stageStatus?.filter((s) => s.label == b.status)?.[0];
            // @ts-ignore
            return aStatus?.order - bStatus?.order;
        };


        function renderTeamInfo(text, record) {
            const teamInfo =  record?.teamInfo ;
            return teamInfo &&
                    <div>
                        <span style={{color: 'blue'}}>@{teamInfo?.['提需人']} </span>
                        <br/>
                        <span style={{color: 'blue'}}>【{teamInfo?.['提需组织']}】</span>
                    </div>;
        }
        function renderTeamLink(text, record,isShow211Setting: boolean = true) {

           const teamInfo =  record?.teamInfo ;

           const teamDateStartTimeStr = teamInfo?.teamStatusLog?.flowedAt;
            const fromStatusName = teamInfo?.teamStatusLog?.fromStatusName;
            const toStatusName = teamInfo?.teamStatusLog?.toStatusName;

            const publishEndDateStr =  record?.schedule?.publish?.endDate;

            let teamDevStartDate: any = null ;
            let teamDevStartDateDisplay: any = null ;
            let statusChange: any = null ;
            if( teamDateStartTimeStr) {
                teamDevStartDate =   dayjs(teamDateStartTimeStr ) ;
                statusChange =  fromStatusName +"->"+toStatusName ;
                teamDevStartDateDisplay =   "Team开发时间:" + teamDevStartDate.format('YYYY-MM-DD ');
            }

           let publishEndDate: any = null ;
           let publishEndDisplay = null ;
            if( publishEndDateStr) {
                publishEndDate =   dayjs(publishEndDateStr ) ;
                publishEndDisplay = publishEndDate.format('YYYY-MM-DD ');
            }

            let curDurationStyle: {} = {} ;
            let finalDuration: any = null ;
            let curDuration: any = null ;
            let curDurationDisplay: any = null ;
            let finalDurationDisplay: any = null ;
            let finalDurationStyle: {} = {} ;
            let ownerStyle: {} = {};
            const owner = teamInfo?.['开发负责人'] ?? teamInfo?.['执行人'] ;
            if(teamDevStartDate) {
                if(publishEndDate == null || dayjs()?.endOf?.('day') < publishEndDate){
                    curDuration = dayjs()?.endOf?.('day')?.diff?.(teamDevStartDate, 'day');
                    if(fromStatusName!="待开发"){
                        curDurationDisplay = curDuration +"+2";
                        curDuration = curDuration +2;
                    }
                    if(curDuration >= 10){
                        curDurationStyle =  {color:'red',fontWeight: 'bold'};
                    }else if(curDuration >= 6) {
                        curDurationStyle =  {color:'black',fontWeight: 'bold'};
                    }else {
                        curDurationStyle =  {color:'green',fontWeight: 'bold'};
                    }
                }


                finalDuration =  publishEndDate ? dayjs(publishEndDate)?.diff?.(teamDevStartDate, 'day') : null ;
                if(fromStatusName!="待开发"){
                    finalDurationDisplay = finalDuration +"+2";
                    finalDuration = finalDuration +2;
                }
                if(finalDuration >= 10){
                    finalDurationStyle =  {color:'red',fontWeight: 'bold'};
                }else if(finalDuration >= 6) {
                    finalDurationStyle =  {color:'black',fontWeight: 'bold'};
                }else {
                    finalDurationStyle =  {color:'green',fontWeight: 'bold'};
                }
            }

            const  isShow211 = teamGroup == 'xhc' && isShow211Setting;

            if(owner && !teamMemberList.includes(owner) && isShow211) {
                ownerStyle = {color: 'brown'};
            }

            let display211: any = null ;
            if(isShow211){
                display211 =<div>
                    {teamDevStartDateDisplay && <Tooltip title={statusChange}> <a>{teamDevStartDateDisplay}</a> <br/></Tooltip>}
                    {curDurationDisplay &&  <Tooltip title={statusChange}><span style={curDurationStyle}>持续时长:{curDurationDisplay};</span></Tooltip>}
                    {finalDurationDisplay && <Tooltip title={statusChange}><span style={finalDurationStyle} >预计时长:{finalDurationDisplay}</span></Tooltip>}
                 </div>
            }


            return record.teamLink &&
                <div style={{minWidth: '200px', maxWidth: '200px', wordWrap: 'break-word', whiteSpace: 'pre-wrap'}}>
                    <a href={record.teamLink} target="_blank" rel="noreferrer">
                        <img
                            src="https://w2.eckwai.com/udata/pkg/eshop/team-icon.svg"
                            alt="Avatar"
                            style={{
                                width: '18px',
                                height: '18px',
                                borderRadius: '50%',
                            }}
                        />
                        {text ? <span>【{teamInfo?.taskTypeName}】{text} 【{record?.teamStatus}】</span> : 'Team链接'}
                    </a>
                    <br/>
                    {owner && <a> 产品:@{teamInfo?.createdName} - 主R:<span style={ownerStyle}>@{owner}</span> <br/></a>}
                    {display211}
                </div>
        }



        useEffect(() => {
            // 从 localStorage 加载筛选和排序条件
            const storedFilters = localStorage.getItem('scheduleFilters');
            const storedSorter = localStorage.getItem('scheduleSorter');
            console.error("storedFilters", storedFilters);
            //query 中有过滤
            const initFilter = {};
            const filterCategory = getQueryParam("filterCategory");
            const filterOwner = getQueryParam("filterOwner");
            if (filterOwner || filterCategory) {
                setFilters(
                    {
                        owner: filterOwner ? filterOwner.split(",") : undefined
                        , category: filterCategory ? filterCategory.split(",") : undefined
                    });
            } else if (storedFilters) {
                setFilters(JSON.parse(storedFilters));
            }
            if (storedSorter) {
                setSorter(JSON.parse(storedSorter));
            }
        }, []);

        function renderDetailColumn(text, record) {
            //CR信息补充
            let crLink: any = null;
            let kimGroupsInfo: any = null;
            if (record?.codeReviewLink) {
                crLink = (
                    <Flex align="center" gap={8}>
                        <img
                            src="https://w2.eckwai.com/udata/pkg/eshop/crLink.png"
                            style={{
                                width: 16,
                                height: 16,
                                borderRadius: '50%'
                            }}
                        />
                        <a target="_blank" href={record?.codeReviewLink} rel="noreferrer">
                            CR链接
                        </a>
                        <div
                            role="button"
                            tabIndex={0}
                            style={{
                                border: 0,
                                background: 'transparent',
                                padding: 0,
                                cursor: 'pointer',
                                display: 'inline-flex',
                                alignItems: 'center',
                                fontSize: '14px',
                                color: '#1890ff'
                            }}
                            onClick={(e) => {
                                e.stopPropagation();
                                try {
                                    // 定义必须包含的类型
                                    const mustHaveTypes = ['技术方案', 'PRD', '监控']; // 使用数组而不是Set

                                    // 解析detail字符串为数组
                                    const detailItems = JSON.parse(record.detail || '[]');

                                    // 检查是否包含所有必需类型
                                    const hasAllTypes = mustHaveTypes.every(type =>
                                        detailItems.some(item => item.type === type)
                                    );

                                    if (!hasAllTypes) {
                                        // 显示具体缺少哪些类型
                                        const missingTypes = mustHaveTypes.filter(type =>
                                            !detailItems.some(item => item.type === type)
                                        ).join('、');

                                        message.error(`请补充完善事项详情：缺少${missingTypes}`);
                                        return;
                                    }

                                    // 原有的复制链接逻辑
                                    const cleanURL = new URL(window.location.href);
                                    cleanURL.search = '?layoutType=1';
                                    const schedule = record.schedule || {};
                                    const timeRanges = Object.values(schedule)
                                        ?.filter((item: any) => item?.startDate && item?.endDate)
                                        ?.map?.((item: any) => ({
                                            start: item?.startDate?.split?.(' ')?.[0],
                                            end: item?.endDate?.split?.(' ')?.[0]
                                        }))
                                        ?.sort?.((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());

                                    const params = new URLSearchParams({
                                        layoutType: '1',
                                        id: record.id
                                    });

                                    if (timeRanges.length > 0) {
                                        params.set('startTime', timeRanges[0]?.start);
                                        params.set('endTime', timeRanges[timeRanges.length - 1]?.end);
                                    }

                                    navigator.clipboard.writeText(`${cleanURL.origin}${cleanURL.pathname}?${params.toString()}`)
                                        .then(() => message.success('链接已复制'))
                                        .catch(console.error);
                                } catch (error) {
                                    message.error('复制链接失败');
                                }
                            }}
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 1024 1024"
                                style={{
                                    width: '14px',
                                    height: '14px',
                                    marginRight: '4px',
                                    verticalAlign: '-0.15em',
                                    fill: 'currentColor',
                                    overflow: 'hidden'
                                }}
                            >
                                <path d="M862.592 161.152A256 256 0 0 0 500.544 161.152L410.048 251.712 411.968 253.632A47.936 47.936 0 0 0 398.72 330.88 48 48 0 0 0 476.032 317.632L477.952 319.552 568.448 229.12A160 160 0 0 1 794.752 455.36L704.192 545.856 706.112 547.776A48 48 0 1 0 770.176 611.776L772.16 613.76 862.592 523.2A256 256 0 0 0 862.592 161.152zM161.152 862.592A256 256 0 0 1 161.152 500.672L251.648 410.048 253.568 411.968A47.936 47.936 0 0 1 330.88 398.848 48 48 0 0 1 317.632 476.032L319.552 477.952 229.12 568.448A160 160 0 0 0 455.296 794.752L545.92 704.256 547.776 706.176A48 48 0 1 1 611.776 770.176L613.76 772.096 523.2 862.72A256 256 0 0 1 161.152 862.656z"/>
                                <path d="M647.68 376.128A48 48 0 0 0 579.776 376.128L376.128 579.84A48 48 0 0 0 443.968 647.68L647.68 444.096A48 48 0 0 0 647.68 376.192Z"/>
                            </svg>
                        </div>
                    </Flex>
                );
            }

            // Kim群信息
            if (record?.kimGroups) {
                kimGroupsInfo = record.kimGroups?.map?.(kimGroup => {
                        return <div>
                            <img
                                src="https://w2.eckwai.com/udata/pkg/eshop/kim.png"
                                style={{
                                    width: '18px',
                                    height: '18px',
                                    borderRadius: '50%',
                                    marginRight: '3px'
                                }}
                            />
                            <a target='_blank' href={kimGroup?.kimLink} rel="noreferrer">{kimGroup?.label}</a>
                        </div>
                    }
                );
            }

            // text是一个json对象的list的字符串，
            // 第一步将 text转化为 对象json数组， json有type，link，title
            // 第二步，将json数组 转化为 <a href=link>[type] title</a> 一个jsx对象

            // 处理text字符串，将其转换为JSX链接元素
            let detailLinks: any = null;
            if (text && typeof text === 'string') {
                detailLinks = convertJsonStringToLinks(text);
            }

            return <div style={{minWidth: '200px', maxWidth: '200px', wordWrap: 'break-word', whiteSpace: 'pre-wrap'}}>
                {detailLinks || <div dangerouslySetInnerHTML={{__html: text}}></div>}
                {crLink}
                {kimGroupsInfo}
            </div>
                ;
        }

        const columns = [
            {
                title: '事项分类',
                // width:
                //     50,
                dataIndex:
                    'category',
                key:
                    'category',
                fixed:
                    'left',
                align: 'center',
                onCell: getCategoryStyle,
                filters:
                    categoryList.current?.map?.((category) => ({
                        value: category ?? '空',
                        text: category ?? '空',
                    })),
                filteredValue: filters["category"],
                sorter: {
                    compare: sortCategory,
                    multiple: 3,
                },
                sortDirections: ['ascend', 'descend'],
                defaultSortOrder: 'ascend',
                onFilter:
                    (value, record) => {
                        if (value == '空' && record?.category == undefined) {
                            return 1;
                        }
                        return record.category?.indexOf?.(value) > -1;
                    }
            },
            {
                title: '事项简称',
                width:
                    50,
                dataIndex:
                    'shortName',
                key:
                    'shortName',
                fixed:
                    'left',
                onCell: getStyle,
                filters:
                    shortNameListOptions?.map((flower) => ({
                        value: flower ?? '空',
                        text: flower ?? '空',
                    })),
                filteredValue: filters["shortName"],
                onFilter:
                    (value, record) => {
                        if (value == '空' && record?.shortName == undefined) {
                            return 1;
                        }
                        return record?.shortName?.indexOf(value) > -1;
                    },
            },
            {
                title: '事项详情',
                dataIndex:
                    'detail',
                key:
                    'detail',
                width: 10,
                fixed:
                    'left',
                onCell: getStyle,
                render: renderDetailColumn,
            },
            {
                title: 'team信息',
                dataIndex:
                    'teamTitle',
                key:
                    'teamTitle',
                width:
                    50,
                fixed:
                    'left',
                onCell: getStyle,
                render: renderTeamLink
            },
            {
                title: '提需组织',
                dataIndex:
                    'teamInfo',
                key:
                    'teamInfo',
                width:
                    50,
                fixed:
                    'left',
                onCell: getStyle,
                hidden: true,
                render: renderTeamInfo
            },
            {
                title: '跟进人',
                dataIndex:
                    'owner',
                key:
                    'owner',
                width:
                    50,
                fixed:
                    'left',
                align: 'center',
                sorter: {
                    multiple: 2,
                    compare: (a, b, order) => {
                        // undefined owner always at the end no matter ascending or descending
                        if (!a.owner?.length) return order === 'ascend' ? 1 : -1;
                        if (!b.owner?.length) return order === 'ascend' ? -1 : 1;
                        // sort by the first owner based on their orders in teamMemberList
                        // @ts-ignore
                        const aIndex = teamMemberObjectList.current?.findIndex((user) => user.name === a.owner?.[0]);
                        // @ts-ignore
                        const bIndex = teamMemberObjectList.current?.findIndex((user) => user.name === b.owner?.[0]);
                        return aIndex - bIndex;
                    },
                },
                defaultSortOrder: 'ascend',
                onCell: getStyle,
                filteredValue: filters["owner"],
                filters:
                    teamMemberList.map((flower) => ({
                        value: flower ?? '空',
                        text: flower ?? '空',
                    })),
                render: (value, record) => renderOwnerColumn(value, record, true),
                onFilter:
                    (value, record) => {
                        if (value == '空' && record?.owner == undefined) {
                            return 1;
                        }
                        return record?.owner?.indexOf(value) > -1;
                    },
            },
            {
                title: '时间安排',
                dataIndex:
                    'schedule',
                key:
                    'schedule',
                width:
                    50,
                fixed:
                    'left',
                sorter: {
                    multiple: 1,
                    compare: sortSchedule,
                },
                render:
                    (text, record) => renderScheduleStage(text, record),
                onCell: getStyle
            },
            {
                title: '状态',
                className: "schedule-status",
                dataIndex:
                    'status',
                key:
                    'status',
                width:
                    20,
                fixed:
                    'left',
                editable:
                    true,
                sorter: {
                    multiple: 4,
                    compare: sortStatus,
                },
                filters:
                    // @ts-ignore
                    stageStatus.map(item => ({value: item.value, text: item.value})),
                filteredValue: filters["status"],
                onFilter:
                    (value, record) => record?.status?.indexOf(value) > -1,
                onCell:
                    (record) => ({
                        record,
                        inputType: 'select',
                        dataIndex: 'status',
                        title: '状态',
                        editing: true,
                        // editing: isEditing(record),
                        // onDoubleClick: () => handleDoubleClick(record),
                        onEditChange: (value, editRecord) => {
                            const updateRecord = {
                                ...editRecord
                            };

                            if (updateRecord?.schedule) {
                                Object.keys(updateRecord?.schedule)?.forEach((stage) => {
                                    //更新状态场景,直接取saveRecord中
                                    updateRecord.schedule[stage].startDate = updateRecord.schedule?.[stage]?.startDate?.substring?.(0, 10);
                                    updateRecord.schedule[stage].endDate = updateRecord.schedule?.[stage]?.endDate?.substring?.(0, 10);
                                });
                            }
                            updateRecord.status = value;
                            API.scheduleUpdate(updateRecord)
                                .then(res => {
                                        const newDataSource = dataSource.map((oldRecord) =>
                                            // @ts-ignore
                                            oldRecord.key === updateRecord.key ? updateRecord : oldRecord
                                        );
                                    // @ts-ignore
                                    setDataSource(newDataSource);
                                    }
                                )
                        },
                        options: stageStatus,
                    }),
            },
            ...getWeekDates(startDate, endDate)?.map?.((date) => ({
                title: getDateColumnTitle(date),
                dataIndex:
                // @ts-ignore
                    `date-${date.format('YYYY-MM-DD')}`,
                // @ts-ignore
                key: `date-${date.format('YYYY-MM-DD')}`,
                width: 40,
                onCell: (record) => renderDateColumnStyle(record, date),
                render: (text, record) => renderDateColumnContent(text, record, date),
                className: "dateColumn"
            })),
            {
                title: '操作',
                dataIndex:
                    'operation',
                key: 'operation',
                width:
                    20,
                fixed:
                    'right',
                align: 'center',
                render:
                    (_, record) => (
                        <div>
                            <Button type="link" onClick={() => handleEdit(record)}>
                                编辑
                            </Button>
                            <Button type="link" danger onClick={() => handleDelete(record)}>
                                删除
                            </Button>
                        </div>
                    ),
            },
        ]
        // ), [isEditing, startDate, endDate, dataSource?.length, filters]);

        const location = useLocation(); // 如果没用 react-router，可以直接用 window.location.search

        const fetchTableData = async (startTime, endTime, strict = false) => {
            const qs = new URLSearchParams(location.search);
            const startTimeUrl = qs.get('startTime') ?? '';
            const endTimeUrl = qs.get('endTime') ?? '';
            const teamId = qs.get('teamId') ?? '';
            const id = qs.get('id') ?? '';

            // 判断是否需要根据URL参数进行过滤
            const shouldFilterByUrl = startTimeUrl && endTimeUrl && teamId;
            const shouldShowSingleRecord = id !== '';

            // 强制使用URL中的时间范围（如果存在）
            const actualStartTime = shouldShowSingleRecord || shouldFilterByUrl ? startTimeUrl : startTime;
            const actualEndTime = shouldShowSingleRecord || shouldFilterByUrl ? endTimeUrl : endTime;
            const actualTeamGroup = shouldFilterByUrl ? 'live' : teamGroup;

            try {
                const data = await API.scheduleList({
                    startTime: actualStartTime,
                    endTime: actualEndTime,
                    teamGroup: actualTeamGroup,
                    strict: strict,
                });

                let filteredData = data;

                // 优先处理单记录筛选（修复类型比较）
                if (shouldShowSingleRecord) {
                    filteredData = data.filter(item => item.id === Number(id)); // 转换为数字类型

                    if (filteredData.length === 0) {
                        console.warn('未找到指定ID的记录:', id);
                        alert('未找到指定的记录');
                    }
                }
                // 再处理原有的URL参数过滤逻辑
                else if (shouldFilterByUrl) {
                    filteredData = data.filter(item => item.teamLink?.includes(teamId));
                }

                setDataSource(filteredData);
            } catch (error) {
                console.error('获取数据失败:', error);
                setDataSource([]);
                alert('数据获取失败，请重试');
            }
        };

        useEffect(() => {
            const fetchData = async () => {
                try {
                    const pageConfig = await API.pageConfig({teamGroup: teamGroup});
                    teamMemberObjectList.current = pageConfig?.teamMemberList ?? [];
                    categoryList.current = pageConfig.categoryList;
                    setStageStatus(pageConfig.stageStatus);
                    setStatusColorMap(pageConfig.statusColorMap);
                    setStageDescMap(pageConfig.stageDescMap);
                    setHolidays(pageConfig.holidays);

                    // 获取URL参数
                    const qs = new URLSearchParams(location.search);
                    const startTimeUrl = qs.get('startTime') ?? '';
                    const endTimeUrl = qs.get('endTime') ?? '';
                    const shouldUseUrlTime = startTimeUrl && endTimeUrl;

                    // 如果URL中有时间参数，使用URL中的时间；否则使用当前状态的时间
                    let actualStartTime, actualEndTime;
                    if (shouldUseUrlTime) {
                        // 使用URL中的时间更新state
                        const urlStartDate = moment(startTimeUrl);
                        const urlEndDate = moment(endTimeUrl);
                        setStartDate(urlStartDate);
                        setEndDate(urlEndDate);
                        actualStartTime = startTimeUrl;
                        actualEndTime = endTimeUrl;
                    } else {
                        actualStartTime = startDate.format("YYYY-MM-DD");
                        actualEndTime = endDate.format("YYYY-MM-DD");
                    }

                    fetchTableData(actualStartTime, actualEndTime);
                } catch (error) {
                    console.error('Error fetching data:', error);
                }
            };
            fetchData();
        }, [location.search]);

// 处理日期范围选择
        const handleDateRangeChange = (dates) => {
            if (dates && dates?.[0]) {
                const selectedstartDate = dates?.[0];
                const selectedEndDate = dates?.[1];
                setStartDate(selectedstartDate);
                if (selectedEndDate) {
                    setEndDate(selectedEndDate);
                }
                fetchTableData(selectedstartDate.format("YYYY-MM-DD"),selectedEndDate.format("YYYY-MM-DD"),true);
            }
        };

// 点击“上一周”按钮
        const handlePrevWeek = () => {
            const newStartDate = moment(startDate)?.subtract?.(7, 'days');
            setStartDate(newStartDate);
        };

// 点击“下一周”按钮
        const handleNextWeek = () => {
            const newEndDate = moment(endDate)?.add?.(7, 'days');
            setEndDate(newEndDate);
        };

// 新增部分：添加按钮和弹窗相关状态和方法
        const [isModalVisible, setIsModalVisible] = useState(false);
        const [modalTitle, setModalTitle] = useState('添加事项');
        const [addForm] = Form.useForm();

        const showAddModal = () => {
            setEditingRecord(null);
            // setIsModalVisible(true);
            setModalTitle('添加事项')
            setIsEditModalVisible(true)
        };

        const handleOk = () => {
            addForm.validateFields().then((values) => {
                // @ts-ignore
                setDataSource([...dataSource, values]);
                setIsModalVisible(false);
                addForm.resetFields();
            }).catch((errorInfo) => {
                console.log('Failed:', errorInfo);
            });
        };

        const handleCancel = () => {
            setIsModalVisible(false);
            addForm.resetFields();
        };

// 编辑弹窗相关状态和方法
        const [isEditModalVisible, setIsEditModalVisible] = useState(false);
        const [editForm] = Form.useForm();
        const [editingRecord, setEditingRecord] = useState(null);

        const handleEdit = (record) => {
            setEditingRecord(record);
            setModalTitle('编辑事项')
            editForm.setFieldsValue(convertEditRecord4Display(record));
            setIsEditModalVisible(true);
        };

        const convertEditRecord4Display = (record) => {
            const editRecord = {
                ...record
            };

            editRecord.detail = ContentUtils.createEditorState(record.detail);
            try{
                editRecord.newDetail  = JSON.parse(record.detail);
            }catch (error){
                editRecord.newDetail = [];
            }


            const dateFormat = 'YYYY-MM-DD';

            // kim 的options 设置，否则编辑的时候无法取值
            if (record?.kimGroups && record.kimGroups.filter(item => item != null)) {
                setKimGroupOptions(record.kimGroups?.filter?.(item => item != null));
                editRecord.kimGroups = record.kimGroups?.map(item => item?.value);
            }


            if (!editRecord?.schedule) {
                return editRecord;
            }

            if (record.extraTime) {
                editRecord.extraTime = record.extraTime?.map?.(date => dayjs(date, dateFormat));
            }

            Object.keys(editRecord?.schedule).forEach((stage) => {
                if (editRecord.schedule?.[stage]) {
                    editRecord.schedule[stage].dateRange = []
                }
                if (editRecord.schedule?.[stage]?.startDate) {
                    editRecord.schedule[stage].dateRange[0] = dayjs(editRecord.schedule?.[stage]?.startDate, dateFormat)
                }
                if (editRecord.schedule?.[stage]?.endDate) {
                    editRecord.schedule[stage].dateRange[1] = dayjs(editRecord.schedule?.[stage]?.endDate, dateFormat)
                }
            });
            return editRecord;
        }

        const dateFormat = 'YYYY-MM-DD';
        const convertEditRecord4Save = (oldRecord, editRecord) => {

            const saveRecord = {
                ...editRecord
            };

            //加班场景
            if (editRecord?.extraTime) {
                saveRecord["extraTime"] = editRecord.extraTime?.map?.(date => date?.format(dateFormat));
            }
            // kim群
            if (editRecord.kimGroups) {
                let kimGroupRepo: any[] = kimGroupOptions;
                if (oldRecord.kimGroups) {
                    kimGroupRepo = [...oldRecord.kimGroups, ...kimGroupOptions];
                }
                saveRecord["kimGroups"] = editRecord.kimGroups?.map?.(kimGroupId => kimGroupRepo?.filter(item => item?.value == kimGroupId)?.[0]);
            }
            // 日程场景
            Object.keys(editRecord?.schedule)?.forEach((stage) => {
                //更新状态场景,直接取saveRecord中
                if (!editRecord.schedule?.[stage]?.dateRange) {
                    saveRecord.schedule[stage].startDate = saveRecord.schedule?.[stage]?.startDate?.format?.(dateFormat);
                    saveRecord.schedule[stage].endDate = saveRecord.schedule?.[stage]?.endDate?.format?.(dateFormat);
                    return;
                }

                if (editRecord.schedule?.[stage]?.dateRange?.[0]) {
                    saveRecord.schedule[stage].startDate = editRecord.schedule?.[stage]?.dateRange?.[0]?.format?.(dateFormat);
                }
                if (editRecord.schedule?.[stage]?.dateRange?.[1]) {
                    saveRecord.schedule[stage].endDate = editRecord.schedule?.[stage]?.dateRange?.[1]?.format?.(dateFormat);
                }
            });

            if (editRecord?.newDetail) {
                saveRecord.detail = JSON.stringify(editRecord.newDetail);
            }
            return saveRecord;
        }

        const handleEditOk = () => {
            editForm.validateFields().then((values) => {

                if (!editingRecord) {
                    const addRecord = convertEditRecord4Save({}, values);
                    API.scheduleAdd(addRecord)
                        .then(res => {
                                // refresh data source
                                const startTime = startDate.format("YYYY-MM-DD");
                                const endTime = endDate.format("YYYY-MM-DD");
                                fetchTableData(startTime,endTime);

                                // close modal
                                setIsEditModalVisible(false);
                                editForm.resetFields();
                            }
                        )
                    return;
                }

                // @ts-ignore
                const currentRecord = dataSource?.filter(record => record.key === editingRecord.key)?.[0];
                const updateRecord = convertEditRecord4Save(currentRecord, values);
                API.scheduleUpdate(updateRecord)
                    .then(updatedRecordList => {
                            const newDataSource: any[] = dataSource.map((oldRecord) => {
                                    // @ts-ignore
                                    const updatedRecord = updatedRecordList?.filter(record => record.key === oldRecord.key)?.[0];
                                    return updatedRecord ?? oldRecord;
                                }
                            );
                            // @ts-ignore
                            setDataSource(newDataSource);
                            setIsEditModalVisible(false);
                            editForm.resetFields();
                        }
                    )

            }).catch((errorInfo) => {
                console.log('Failed:', errorInfo);
            });
        };

        const handleEditCancel = () => {
            setIsEditModalVisible(false);
            setEditingRecord(null); // 重置状态
            editForm.resetFields();
        };

        // 判断是否为 Moment 对象
        function isMomentObject(obj) {
            return obj instanceof moment || obj._isAMomentObject === true;
        }

// 判断是否为 Dayjs 对象
        function isDayjsObject(obj) {
            return obj && typeof obj.$d === 'object' && obj.$d instanceof Date;
        }

// 判断是否为周末
        function isWeekend(date) {
            let dayOfWeek;
            if (isMomentObject(date)) {
                dayOfWeek = date.day();
            } else if (isDayjsObject(date)) {
                dayOfWeek = date.day();
            }
            return dayOfWeek === 0 || dayOfWeek === 6;
        }

        // 判断是否为工作日的函数
        function isWorkday(date) {
            const dayOfWeek = date.day();
            // 判断是否为常规工作日（周一到周五）
            const isRegularWorkday = dayOfWeek >= 1 && dayOfWeek <= 5;

            // 检查是否为节假日
            const isHoliday = holidays.some(holiday => {
                // @ts-ignore
                return holiday.holidayArrangement?.some?.(holidayDateStr => {
                    const holidayDate = dayjs(holidayDateStr);
                    return holidayDate.isSame(date, 'day');
                });
            });

            // 检查是否为调休工作日
            const isAdjustedWorkday = holidays.some(holiday => {
                // @ts-ignore
                return holiday.adjustedDay?.some?.(adjustedDateStr => {
                    const adjustedDate = dayjs(adjustedDateStr);
                    return adjustedDate.isSame(date, 'day');
                });
            });

            // 综合判断
            if (isRegularWorkday && !isHoliday) {
                return true;
            }
            return isAdjustedWorkday;
        }

        function convertToMoment(current) {
            // 判断 current 是否为 dayjs 对象
            if (dayjs.isDayjs(current)) {
                return moment({
                    year: current.year(),
                    month: current.month(),
                    date: current.date(),
                    hour: current.hour(),
                    minute: current.minute(),
                    second: current.second(),
                    millisecond: current.millisecond()
                });
            }
            return current;
        }

        function isOvertime(current, extraTimeDates) {
            let isOvertime = false;
            if (!extraTimeDates) {
                return isOvertime;
            }
            extraTimeDates.forEach((date) => {
                if (date.isSame(current, 'day')) {
                    isOvertime = true;
                }
            });
            return isOvertime;
        }

        // @ts-ignore
        function getScheduleStage(current, schedule,extraTime) {
            if (!schedule) {
                return;
            }

            current = isDayjsObject(current) ? current : dayjs(current.valueOf());

            const stages = Object.keys(schedule)?.reverse();
            // @ts-ignore
            const matchedStages = stages?.map((stage) => {
                if (!schedule[stage].startDate && !schedule[stage]?.dateRange) {
                    return;
                }
                let startDate, endDate, extraTimeDates;
                startDate = schedule[stage]?.startDate ? moment(schedule[stage]?.startDate, 'YYYY-MM-DD') : schedule[stage]?.dateRange?.[0];
                endDate = schedule[stage]?.endDate ? moment(schedule[stage]?.endDate, 'YYYY-MM-DD') : schedule[stage]?.dateRange?.[1];
                // endDate = moment(schedule[stage].endDate, 'YYYY-MM-DD');
                // extraTimeDates = schedule["extraTime"]?.dates?.map(date => isDayjsObject(date) ? date : dayjs(date, 'YYYY-MM-DD'));
                extraTimeDates = editForm.getFieldValue("extraTime") ?? extraTime?.map(dateString => dayjs(dateString,'YYYY-MM-DD'));

                const isInRange = current.isBetween(startDate, endDate, 'day', '[]')
                // 选择时段 && (非周末 或者 加班)
                if (isInRange && (isWorkday(current) || isOvertime(current, extraTimeDates))) {
                    return stage;
                }
            })?.filter?.((value) => value !== undefined);

            if (matchedStages.length > 0) {
                return matchedStages?.[0];
            }
        }

        const dateRangeCellRender = (current, info) => {
            if (info.type !== 'date') {
                return info.originNode;
            }
            const schedule = editForm.getFieldValue("schedule");
            // @ts-ignore
            const stage = getScheduleStage(current, schedule);
            const style = {
                // @ts-ignore
                border: `1px solid ` + statusColorMap[stage] ?? 'blue',
                borderRadius: '50%',
                // @ts-ignore
                backgroundColor: statusColorMap[stage] ?? 'blue'
            };
            return (
                <div className="ant-picker-cell-inner" style={stage ? style : {}}>
                    {current.date()}
                </div>
            );
        }


        const handleRecentChange = (value) => {
            setDateSelectValue(value);
            localStorage.setItem('dateSelectValue', value);
            const [newStartDate, newEndDate] = getDateRange(value);
            setStartDate(newStartDate);
            setEndDate(newEndDate);
            fetchTableData(newStartDate,newEndDate);
        };


        const columnsToCopy = ['category', 'detail', 'status', 'schedule', 'owner','teamInfo'];
        const [filteredData, setFilteredData] = useState(dataSource);


        const handleTableChange = (pagination, newFilters, newSorter) => {
            // 处理表格筛选和排序变化
            const filtered = dataSource.filter((record) => {
                for (const key in filters) {
                    if (filters[key] && !filters[key].includes(record[key])) {
                        return false;
                    }
                }
                return true;
            });
            setFilteredData(filtered);

            setFilters(newFilters);
            setSorter(newSorter);
            // 存储筛选和排序条件到 localStorage
            localStorage.setItem('scheduleFilters', JSON.stringify(newFilters));
            localStorage.setItem('scheduleSorter', JSON.stringify(newSorter));
        };

        const handleCopy = async () => {
            // 提取需要复制的列的表头
            const headers = columnsToCopy.map((col) => {
                return columns?.find((c) => c.dataIndex === col)?.title || col;
            });

            // 创建表头的 <tr> 标签
            let tableRows = `<tr>${headers?.map((header) => `<th style = "font-weight:bold;background:#d0f0fd ">${header}</th>`)?.join?.('')}</tr>`;

            // 提取筛选后的数据的指定列内容并创建 <tr> 标签
            currentData.forEach((record) => {
                const rowData = columnsToCopy.map((col) => {
                    if ('detail' == col) {
                        const regex = /https:\/\/docs\.corp\.kuaishou\.com\/[^\s"]+/g;
                        const matches = record[col]?.match?.(regex);
                        return (matches?.join("<br/>") ?? record['shortName'] )+"<br/>"+ ReactDOMServer.renderToString(renderTeamLink(record["teamTitle"], record,false));
                    }
                    // if ('teamTitle' == col) {
                    //     return ReactDOMServer.renderToString(renderTeamInfo(record[col], record))
                    // }
                    if ('schedule' == col) {
                        return ReactDOMServer.renderToString(renderScheduleStage('', record))
                    }
                    if ('owner' == col) {
                        // @ts-ignore
                        return ReactDOMServer.renderToString(renderOwnerColumn(record[col], record, false));
                    }

                    if('teamInfo' == col){
                        return ReactDOMServer.renderToString(renderTeamInfo(record[col], record));
                    }

                    return record[col]
                });

                let trStyle = '';
                if (record.status) {
                    trStyle = `style = "background-color:${statusColorMap[record.status]}" `
                }


                tableRows += `<tr>${rowData?.map((cell) => `<td ${trStyle}>${cell}</td>`)?.join?.('')}</tr>`;
            });

            const htmlContent = `<table>${tableRows}</table>`;

            console.error(htmlContent);

            try {
                // 创建 ClipboardItem 对象，指定 MIME 类型为 text/html
                const clipboardItem = new ClipboardItem({
                    'text/html': new Blob([htmlContent], {type: 'text/html'}),
                    'text/plain': new Blob([htmlContent.replace(/<[^>]*>/g, '')], {type: 'text/plain'})
                });

                // 使用 Clipboard API 的 write 方法复制数据到剪贴板
                await navigator.clipboard.write([clipboardItem]);
                console.log('数据已复制到剪贴板');
            } catch (error) {
                console.error('复制数据时出错:', error);
            }
        };


        function getWeekNumber(date) {
            // 确保传入的日期是有效的 dayjs 对象
            const currentDate = dayjs(date);
            if (!currentDate.isValid()) {
                return null;
            }

            // 设置一周的起始为周一
            // @ts-ignore
            currentDate.locale('en', { week: { dow: 1 } });
            // 获取从周一开始计算的周数
            // @ts-ignore
            return currentDate.week();
        }


        const getWeekNumberName = (startDate,endDate) =>{
          const start =   getWeekNumber(startDate);
          const end =   getWeekNumber(endDate);
            const weekArray = [];
            for (let i = start; i <= end; i++) {
                // @ts-ignore
                weekArray.push(i);
            }
            return weekArray;
        }

        const teamScheduleInsight = async () => {

            setIsInsightModalVisible(true);

        }

        const navigate = useNavigate();  // 初始化 navigate
        const teamBoard = () => {
            navigate('/team/insight?layoutType=1');  // 页面跳转到 team/insight，传递参数
        };
        const handleResourceCopy = async () => {

            // 提取需要复制的列的表头

            const handleResourceCopy = ['id', 'detail', 'target', 'teamInfo', 'owner', 'teamLink'];

            const weekNumberNameList = getWeekNumberName(startDate, endDate);
            const weekNumberNameHeader = weekNumberNameList.map((name) => name + '周');
            const handleResourceHeader = [...handleResourceCopy, ...weekNumberNameHeader];
            // 创建表头的 <tr> 标签
            let tableRows = `<tr>${handleResourceHeader?.map((header) => `<th style = "font-weight:bold;background:#d0f0fd ">${header}</th>`)?.join?.('')}</tr>`;

            const tableCaption =  +currentData.length +`行x`+handleResourceHeader.length+`列`;
                // 提取筛选后的数据的指定列内容并创建 <tr> 标签
                [...currentData]?.sort((a, b) => a.id - b.id)?.forEach?.((record) => {
                let rowData = handleResourceCopy.map((col) => {

                    if ('id' == col) {
                        return record['id'];
                    }
                    if ('detail' == col) {
                        const regex = /https:\/\/docs\.corp\.kuaishou\.com\/[^\s"]+/g;
                        const matches = record[col]?.match?.(regex);
                        return matches?.[0] ?? record['shortName'];
                    }

                    if ('target' == col) {
                        return "常规迭代"
                    }
                    if ('teamInfo' == col) {
                        return record?.teamInfo?.['提需组织'] ?? "";
                    }

                    if ('owner' == col) {
                        // @ts-ignore
                        return ReactDOMServer.renderToString(renderOwnerColumn(record[col], record, false));
                    }

                    if ('teamLink' == col) {
                        return record.teamLink
                    }

                    return col;
                });

                const weekCountObj = getWeekCountObj(record);
                let trStyle = '';
                if ( Object.keys(weekCountObj).length === 0) {
                    trStyle = `style = "background-color:yellow" `
                }

                const weekNumberList = weekNumberNameList.map((key) => weekCountObj[key] ?? '');
                rowData = [...rowData, ...weekNumberList];
                tableRows += `<tr>${rowData?.map((cell) => `<td ${trStyle}>${cell}</td>`)?.join?.('')}</tr>`;
            });

            const htmlContent = tableCaption + `<table> ${tableRows}</table>`;

            console.error(htmlContent);

            try {
                // 创建 ClipboardItem 对象，指定 MIME 类型为 text/html
                const clipboardItem = new ClipboardItem({
                    'text/html': new Blob([htmlContent], {type: 'text/html'}),
                    'text/plain': new Blob([htmlContent.replace(/<[^>]*>/g, '')], {type: 'text/plain'})
                });

                // 使用 Clipboard API 的 write 方法复制数据到剪贴板
                await navigator.clipboard.write([clipboardItem]);
                console.log('数据已复制到剪贴板');
            } catch (error) {
                console.error('复制数据时出错:', error);
            }
        };


        const handleKimSearch = async (keywords) => {
            const kimGroups = await API.searchKimGroup({text: keywords});
            const options = kimGroups.map(kimGroup => ({
                    value: kimGroup.groupId,
                    label: kimGroup.groupName,
                    avatar: kimGroup.avatar,
                    kimLink: "kim://thread?type=4&id=" + kimGroup.groupId
                }))
            ;
            setKimGroupOptions(options);
        };


        const tagTypes = ["营销楼层", "商品卡-卖点", "商品卡-标签", "商品卡-价格", "底导购物车", "店铺", "商家客服", "万人团", "闪电购", "销量"]

        // 处理输入框内容变化，检查是否包含关键字
        const handleInputChange = (e, field, form) => {
            if(1==1){
                // 关闭改功能
                return ;
            }
            const value = e.target.value;
            if (!value) return;

            // 遍历detailTypes，检查输入值是否包含关键字
            for (const detailType of detailTypes) {
                let matchedType = detailType.type;
                let matched = false ;
                let extractedTitle = "";
                if (detailType.keyword && detailType.keyword!='' && value.includes(detailType.keyword.toLowerCase())) {
                    // 如果包含关键字，将DetailTypeSelect的值设置为对应的type
                    matchedType = detailType.type ;
                    matched = true;
                }

                if (matched && detailType.matchRegex && value.match(detailType.matchRegex)) {
                   // value 命中matchRegex的值设置为 title
                    extractedTitle = value?.match(detailType.matchRegex)?.[1];
                }

                if(matched){
                    form.setFieldsValue({
                        newDetail: form?.getFieldValue('newDetail')?.map?.((item, i) => {
                            if (i === field.name) {
                                if(extractedTitle!=''){
                                    return { ...item, type: matchedType ,title:extractedTitle};
                                }else{
                                    return { ...item, type: matchedType};
                                }

                            }
                            return item;
                        })
                    });
                }
            }
        };

        const DetailTypeSelect = <Select style={{ width: 100 }}>
            {
                detailTypes.map(item => <Option key={item.type} value={item.type}>{item.type}</Option>)
            }
        </Select>;


        const [showDetailItem, setShowDetailItem] = useState({});

        const [showDetailValueList, setShowDetailValueList] = useState([]);


        const toggleNewDetailInput = (index) => {

            // 遍历showDetailValueList,如果有index相同的项，则取反
            // 如果没有，则添加一个新的项,showValue = true
            const newShowDetailValueList: any[] = showDetailValueList.map((item) => {
                // @ts-ignore
                if (item.index === index) {
                    return {
                        // @ts-ignore
                        ...item,
                        // @ts-ignore
                        showValue: !item.showValue,
                    };
                }
                return item;
            });

            if (!newShowDetailValueList.some((item) => item.index === index)) {
                newShowDetailValueList.push({index, showValue: true});
            }
            // @ts-ignore
            setShowDetailValueList(newShowDetailValueList);
        };

        // @ts-ignore
        // @ts-ignore
        // @ts-ignore
        return (
            <ConfigProvider locale={zhCN}>
                <div style={{width: '100%'}}>
                    <div style={{marginBottom: 16, textAlign: 'center'}}>
                        <Button icon={<LeftOutlined/>} onClick={handlePrevWeek}>
                            上一周
                        </Button>
                        <Select
                            value={dateSelectValue}
                            onChange={handleRecentChange}
                            style={{marginLeft: 16, marginRight: 6}}
                        >
                            <Option value="currentWeek">近一周</Option>
                            <Option value="before1WeekToCurrent">近两周</Option>
                            <Option value="currentToAfter1Week">后两周</Option>
                            <Option value="currentMonth">本月度</Option>
                            <Option value="currentYear">本年度</Option>
                        </Select>
                        <RangePicker
                            onChange={handleDateRangeChange}
                            style={{margin: '0 16px'}}
                            value={[dayjs(startDate.valueOf()), dayjs(endDate.valueOf())]}
                        />
                        <Button icon={<RightOutlined/>} onClick={handleNextWeek}>
                            下一周
                        </Button>
                        <Button icon={<PlusOutlined/>} onClick={showAddModal} style={{marginLeft: 16}}>
                            添加
                        </Button>
                        <Button onClick={handleCopy } style={{marginLeft: 16}}>复制</Button>
                        <Button onClick={handleResourceCopy} style={{marginLeft: 16}}>资源复制</Button>
                        <Button onClick={teamScheduleInsight} style={{marginLeft: 16}}>投入查看</Button>
                        <Button onClick={teamBoard} style={{marginLeft: 16}}>仪表板</Button>

                        <Legend statusColorMap={legendStatusColorMap}
                            // style={{display: 'inline-block'}}
                        />
                    </div>
                    <Table
                        components={{
                            body: {
                                cell: EditableCell,
                            },
                        }}
                        // @ts-ignore
                        columns={columns}
                        dataSource={dataSource}
                        rowClassName="editable-row"
                        scroll={{x: 'max-content'}}
                        bordered
                        pagination={false}
                        size="small"
                        onChange={handleTableChange}
                        filters={filters}
                        sorter={sorter}
                        summary={teamSummary}
                    />
                    <Modal
                        title={modalTitle}
                        visible={isEditModalVisible}
                        onOk={handleEditOk}
                        onCancel={handleEditCancel}
                        className="schedule-edit-modal"
                    >
                        <Form
                            form={editForm}
                            name="editForm"
                            layout="horizontal"
                            labelCol={{
                                span: 8,
                            }}
                            wrapperCol={{
                                span: 14,
                            }}
                            initialValues={{
                                category: categoryList.current?.[0],
                                shortName: "任务简称-" + moment()?.valueOf?.(),
                                teamGroup: teamGroup,
                                // detail: ContentUtils.createEditorState("editData.richText")
                            }}
                        >

                            <Form.Item
                                label="ID"
                                name="id"
                                hidden={true}
                            >
                                <Input/>
                            </Form.Item>
                            <Form.Item
                                label="key"
                                name="key"
                                hidden={true}
                            >
                                <Input/>
                            </Form.Item>
                            <Form.Item
                                label="teamGroup"
                                name="teamGroup"
                                hidden={true}
                            >
                                <Input/>
                            </Form.Item>
                            <Form.Item
                                label="事项分类"
                                name="category"
                                rules={[{required: true, message: '分类'}]}
                            >
                                <Select
                                    options={categoryList.current?.map?.((category) => ({
                                        value: category,
                                        label: category,
                                    }))}
                                />
                            </Form.Item>
                            <Form.Item
                                label="事项简称"
                                name="shortName"
                                rules={[{required: true, message: '请输入事项简称'}]}
                            >
                                <Input/>
                            </Form.Item>
                            {/*<Form.Item*/}
                            {/*    label="事项详情"*/}
                            {/*    name="detail"*/}
                            {/*>*/}
                            {/*    /!* 使用 BraftEditor 替换 Input *!/*/}
                            {/*    <BraftEditor*/}
                            {/*        placeholder="请输入事项详情"*/}
                            {/*        // @ts-ignore*/}
                            {/*        controls={['']}*/}
                            {/*        onChange={(editorState) => {*/}
                            {/*            // 将编辑器内容同步到表单值*/}
                            {/*            form.setFieldsValue({detail: editorState.toHTML()});*/}
                            {/*        }}*/}
                            {/*    />*/}
                            {/*</Form.Item>*/}
                            <Form.Item label="事项详情">
                                <Form.List
                                    name="newDetail"
                                    initialValue={[{ type: "PRD", value: "" }]}
                                >
                                    {(fields, { add, remove }, { errors }) => (
                                        <>
                                            {fields.map((field, index) => (
                                                <Space style={{ display: 'flex', marginBottom: 2 }} align="baseline">
                                                    {/* @ts-ignore */}
                                                    {  !showDetailValueList.find(item => item.index === index)?.showValue && <Form.Item
                                                        {...field}
                                                        validateTrigger={['onChange', 'onBlur']}
                                                        noStyle
                                                        name={[field.name, 'link']}
                                                    >
                                                        <Input
                                                            placeholder="链接 http://xxx.corp"
                                                            className="detail-link-input"
                                                            onChange={(e) => handleInputChange(e, field, editForm)}
                                                            addonBefore={
                                                            <Form.Item
                                                                name={[field.name, "type"]}
                                                                noStyle
                                                                initialValue="PRD"
                                                            >
                                                                {DetailTypeSelect}
                                                            </Form.Item>
                                                        }/>
                                                    </Form.Item>
                                                    }
                                                    {/* @ts-ignore */}
                                                    {  showDetailValueList.find(item => item.index === index)?.showValue &&
                                                        <Form.Item
                                                            {...field}
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            noStyle
                                                            name={[field.name, 'title']}
                                                        >
                                                            <Input
                                                                placeholder="事项详情"
                                                                onChange={(e) => handleInputChange(e, field, editForm)}
                                                                addonBefore={
                                                                <Form.Item
                                                                    name={[field.name, "type"]}
                                                                    noStyle
                                                                    initialValue="PRD"
                                                                >
                                                                    {DetailTypeSelect}
                                                                </Form.Item>
                                                            }/>
                                                        </Form.Item>
                                                    }

                                                    {fields.length > 1 ? (
                                                        <MinusCircleOutlined
                                                            className="dynamic-delete-button"
                                                            onClick={() => remove(field.name)}
                                                        />
                                                    ) : null}
                                                    <Tooltip title="链接和标题切换">
                                                        <SwapOutlined
                                                            className="dynamic-switch-button"
                                                            onClick={() => toggleNewDetailInput(index)}
                                                        />
                                                    </Tooltip>
                                                </Space>
                                            ))}
                                            <Form.Item {...formItemLayoutWithOutLabel}>
                                                <Button
                                                    type="dashed"
                                                    onClick={() => add()}
                                                    style={{ width: '60%' }}
                                                    icon={<PlusOutlined />}
                                                    size="small"
                                                >
                                                    更多
                                                </Button>
                                                <Form.ErrorList errors={errors} />
                                            </Form.Item>
                                        </>
                                    )}
                                </Form.List>
                            </Form.Item>
                            <Form.Item
                                label="team链接"
                                name="teamLink"
                                rules={[{message: '请输入team链接'}]}
                            >
                                <Input/>
                            </Form.Item>
                            <Form.Item
                                label="跟进人"
                                name="owner"
                            >
                                <Select
                                    mode="multiple"
                                    options={teamMemberList.map((flower) => ({
                                        value: flower,
                                        label: flower,
                                    }))}
                                />
                            </Form.Item>
                            <Form.Item label="标签" name="tags">
                                <Select
                                    showSearch
                                    mode="multiple"
                                    allowClear
                                    // style={{ width: 200 }}
                                    placeholder="选择标签"
                                    optionFilterProp="label"
                                    filterSort={(optionA, optionB) =>
                                        (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
                                    }
                                    options={ tagTypes.map(key => ({ value: key, label: key })) }
                                />
                            </Form.Item>
                            <Form.Item
                                label="KIM群"
                                name="kimGroups"
                            >
                                <Select
                                    mode="multiple"
                                    showSearch // 显示搜索框
                                    placeholder="输入kim关键字"
                                    optionFilterProp="children" // 指定搜索时匹配的属性，这里使用 option 的子节点文本
                                    filterOption={false}
                                    onSearch={handleKimSearch}
                                    options={kimGroupOptions}
                                    optionRender={(option) => (
                                        <Space>
                                            <img
                                                // @ts-ignore
                                                src={option.data?.avatar}
                                                style={{
                                                    width: '20px',
                                                    height: '20px',
                                                    borderRadius: '50%',
                                                }}
                                            />
                                            {/* @ts-ignore */}
                                            {option.data?.label}
                                        </Space>
                                    )}
                                />
                            </Form.Item>
                            <Form.Item
                                label="CR链接"
                                name="codeReviewLink"
                                rules={[{message: '请输入CR链接'}]}
                            >
                                <Input/>
                            </Form.Item>
                            <Form.Item
                                label="时间安排 - 备注"
                                name="scheduleNote"
                            >
                                <TextArea/>
                            </Form.Item>
                            {/*<Form.Item*/}
                            {/*    label="时间安排 - 预评"*/}
                            {/*    name={["schedule", "preEvaluation", "dateRange"]}*/}
                            {/*    // name="testrange"*/}
                            {/*>*/}
                            {/*    <RangePicker cellRender={dateRangeCellRender}/>*/}
                            {/*</Form.Item>*/}
                            {/*<Form.Item*/}
                            {/*    label="时间安排 - 详评"*/}
                            {/*    name={["schedule", "detailEvaluation", "dateRange"]}*/}
                            {/*    // name="testrange"*/}
                            {/*>*/}
                            {/*    <RangePicker cellRender={dateRangeCellRender}/>*/}
                            {/*</Form.Item>*/}
                            <Form.Item
                                label="时间安排 - 开发"
                                name={["schedule", "dev", "dateRange"]}
                                // name="testrange"
                            >
                                <RangePicker cellRender={dateRangeCellRender}/>
                            </Form.Item>
                            <Form.Item
                                label="时间安排 - 联调"
                                name={["schedule", "integration", "dateRange"]}
                            >
                                <RangePicker cellRender={dateRangeCellRender}/>
                            </Form.Item>
                            <Form.Item
                                label="时间安排 - 测试"
                                name={["schedule", "test", "dateRange"]}
                            >
                                <RangePicker cellRender={dateRangeCellRender}/>
                            </Form.Item>
                            <Form.Item
                                label="时间安排 - 发布"
                                name={["schedule", "publish", "dateRange"]}
                            >
                                <RangePicker cellRender={dateRangeCellRender}/>
                            </Form.Item>
                            <Form.Item
                                label="时间安排 - 放量"
                                name={["schedule", "gray", "dateRange"]}
                            >
                                <RangePicker cellRender={dateRangeCellRender}/>
                            </Form.Item>
                            <Form.Item
                                label="时间安排 - 加班"
                                name={["extraTime"]}
                            >
                                <DatePicker
                                    prefix={cryIcon}
                                    multiple
                                    maxTagCount="responsive"
                                    cellRender={dateRangeCellRender}/>
                            </Form.Item>
                            <Form.Item
                                label="状态"
                                name="status"
                                rules={[{message: '请选择当前进度'}]}
                            >
                                <Select
                                    options={stageStatus}
                                />
                            </Form.Item>
                        </Form>
                    </Modal>
                    <Modal
                        title='本周人力投入'
                        visible={isInsightModalVisible}
                        onOk={() => {setIsInsightModalVisible(false)}}
                        onCancel={() => {setIsInsightModalVisible(false)}}
                        className="schedule-insight-modal"
                    >
                        <InsightStackedColumn/>
                    </Modal>
                </div>
            </ConfigProvider>
        );
    }
;

export default ScheduleTable;
function isMomentObject(endDate: moment.Moment) {
    throw new Error('Function not implemented.');
}

