// src/components/HistoryRecord.tsx
import React from 'react';
import {<PERSON><PERSON>, Modal, Table} from 'antd';
import type {ColumnsType} from 'antd/lib/table';
import {ITEM_LINK_PREFIX} from "@/pages/StreamLabel/constant";

export interface HistoryItem {
    id: number;
    sellerId: number;
    liveStreamId: number;
    startTime: string;
    endTime: string;
    labelItemIdList: string;
    labelStatus: number;
    labelUserName: string;
    extend: string | null;
}

interface HistoryRecordProps {
    data: HistoryItem[];
    onDelete?: (liveStreamId: number, liveLabelRecordId: number) => void;
}

const HistoryRecord: React.FC<HistoryRecordProps> = ({ data, onDelete }) => {

    /**
     * 将 "HH:mm:ss" 格式的时间字符串转为秒数
     */
    const timeStringToSeconds = (time: string): number => {
        const parts = time.split(':').map(Number);
        if (parts.length === 3) {
            return parts[0] * 3600 + parts[1] * 60 + parts[2];
        }
        if (parts.length === 2) {
            return parts[0] * 60 + parts[1];
        }
        return Number(time) || 0;
    };

    /**
     * 对比两个时间字符串，返回它们的秒数差
     */
    const compareTime = (a: string, b: string): number =>
        timeStringToSeconds(a) - timeStringToSeconds(b);

    const columns: ColumnsType<HistoryItem> = [
        {
            title: '直播间ID',
            dataIndex: 'liveStreamId',
            key: 'liveStreamId',
            width: 200,
        },
        {
            title: '商品ID列表',
            dataIndex: 'labelItemIdList',
            key: 'labelItemIdList',
            width: 400,
            render: (text: string) => {
                // 1. 解析 string "[123,456]" → ['123','456']
                let ids: string[] = [];
                try {
                    // 如果 text 是合法 JSON 数组
                    const parsed = JSON.parse(text);
                    if (Array.isArray(parsed)) {
                        ids = parsed.map((i) => String(i).trim());
                    }
                } catch {
                    // 兜底：手动去掉首尾 [ ] 再分割
                    ids = text
                        .replace(/^\[|\]$/g, '')
                        .split(',')
                        .map((i) => i.trim())
                        .filter((i) => i);
                }

                if (ids.length === 0) return <span>无</span>;

                // 2. 渲染每个 id 为链接
                return (
                    <div style={{ whiteSpace: 'normal', overflowWrap: 'break-word', }}>
                        {ids.map((id, idx) => (
                            <React.Fragment key={id}>
                                <a
                                    href={`${ITEM_LINK_PREFIX}${encodeURIComponent(id)}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {id}
                                </a>
                                {idx < ids.length - 1 && ', '}
                            </React.Fragment>
                        ))}
                    </div>
                );
            },
        },
        {
            title: '开始时间',
            dataIndex: 'startTime',
            key: 'startTime',
            width: 180,
            sorter: (a, b) => compareTime(a.startTime, b.startTime),
        },
        {
            title: '结束时间',
            dataIndex: 'endTime',
            key: 'endTime',
            width: 180,
            sorter: (a, b) => compareTime(a.endTime, b.endTime),
        },
        {
            title: '标注人',
            dataIndex: 'labelUserName',
            key: 'labelUserName',
            width: 150,

        },
        {
            title: '说明',
            dataIndex: 'extend',
            key: 'extend',
            width: 100,
            render: (text: string | null) => text ?? '--',
        },
        {
            title: '操作',
            key: 'action',
            width: 100,
            render: (_text, record) => (
                <Button
                    type="link"
                    danger
                    style={{ paddingLeft: 0 }}
                    onClick={() => {
                        Modal.confirm({
                            title: '确认删除这条记录？',
                            content: '删除后将无法恢复，是否继续？',
                            okText: '删除',
                            okType: 'danger',
                            cancelText: '取消',
                            onOk: () => {
                                // 这里才调用父组件传入的 onDelete
                                if (onDelete) {
                                    try {
                                       onDelete(record.liveStreamId, record.id);
                                    } catch (err) {
                                        console.error(err);
                                        Modal.error({ title: '删除失败', content: '请重试或检查网络' });
                                    }
                                }
                            },
                        });
                    }}
                >
                    删除
                </Button>
            ),
        },
    ];

    return (
        <Table<HistoryItem>
            columns={columns}
            dataSource={data}
            rowKey="liveStreamId"
            pagination={{ pageSize: 10 }}
            style={{ width: '100%' }}
        />
    );
};

export default HistoryRecord;
