import React, {useCallback, useEffect, useRef, useState} from 'react';
import Flex from '@/components/Flex';
import {Input, Tabs, Tooltip, Typography} from '@m-ui/react';
import StickyBox from 'react-sticky-box';
import {QuestionCircleOutlined,} from '@m-ui/icons';
import {KwaiPlayer, KwaiPlayerReact} from '@ks-video/kwai-player-web/react';
import cls from 'classnames/bind';
import style from './index.module.less';
import {SOP_LINK, TEST_VIDEO_IDS} from './constant';
import CurrentRecord, {ProductItem} from "@/pages/StreamLabel/Tabs/CurrentRecord";
import HistoryRecord, {HistoryItem} from "@/pages/StreamLabel/Tabs/HistoryRecord";
import {Button, message} from "antd";
import * as API from '@/common/API/StreamLabelAPI';

const cx = cls.bind(style);

export default function StreamLabel() {

  const playerRef = useRef<KwaiPlayer | null>(null);
  const videoRefForChild = (playerRef as unknown) as React.RefObject<HTMLVideoElement>;

  const stored = localStorage.getItem('liveStreamId') ?? '';
  const [searchValue, setSearchValue] = useState(stored);
  const [liveStreamId, setLiveStreamId] = useState<number>(() => {
    return stored ? Number(stored) : 0;
  });
  const [activeKey, setActiveKey] = useState<'current'|'history'>('current');
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [history, setHistory] = useState<HistoryItem[]>([]);

  // 商品列表和视频URL
  const [products, setProducts] = useState<ProductItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductItem[]>(products);

  const [videoUrls, setVideoUrls] = useState<string[]>([]);
  const [srcIndex, setSrcIndex] = useState(0);
  const videoSrc = videoUrls[srcIndex] || '';

  useEffect(() => {
    setFilteredProducts(products);
  }, [products]);

  const fetchInfo = useCallback(async (id: number) => {
    try {
      const info = await API.LabelInfo({ liveStreamId: id });
      setVideoUrls(info.videoUrl ?? []);
      setSrcIndex(0);
      setProducts(info.itemInfos ?? []);
      setFilteredProducts(info.itemInfos ?? []);
    } catch {
      message.error('获取视频信息失败');
    }
  }, []);

  // —— 在挂载时，如果 localStorage 里有值，就自动拉一次 ——
  useEffect(() => {
    if (searchValue) {
      const id = Number(searchValue);
      setLiveStreamId(id);
      fetchInfo(id);
    }
  }, []);

  // 拉取历史记录
  const fetchHistory = useCallback(async () => {
    if (!liveStreamId) return;
    try {
      const historyData = await API.LabelHistory({ liveStreamId: Number(liveStreamId) });
      setHistory(historyData ?? []);
    } catch (err) {
      console.error(err);
      message.error('获取历史评测记录失败');
    }
  }, [liveStreamId]);

  useEffect(() => {
    setHistory([]);    // 切 ID 先清空
    fetchHistory();
    if (liveStreamId) {
      localStorage.setItem('liveStreamId', String(liveStreamId));
    }
  }, [liveStreamId]);

  const onTimeUpdate = useCallback((_: any) => {
    const inst = (playerRef.current as any)
    if (!inst) return
    let t: number;
    if (typeof inst.currentTime === 'number') {
      t = inst.currentTime;
    } else if (typeof inst.getCurrentTime === 'function') {
      t = inst.getCurrentTime();
    } else {
      t = 0;
    }
    setCurrentTime(t)
  }, [playerRef])

  const onLoadedMetadata = useCallback(() => {
    const inst = playerRef.current
    if (!inst) return
    const d = (inst as any).duration
    if (typeof d === 'number') setDuration(d)
  }, [])

  const handleTabClick = (key: string) => {
    if (key === 'history') {
      fetchHistory(); // 重新调用接口
    }
  };

  const handleTabChange = (key: string) => {
    setActiveKey(key as 'current' | 'history');
  };


  return (
    <Flex vertical gap={16} className={cx('video')}>
      <Flex
          className={cx('top')}
          gap={16}
          align="center"
          style={{ flexWrap: 'nowrap' , paddingTop:"20px"}}
      >
        {/* 标题 + Tooltip */}
        <Flex gap={8} align="center" style={{ flex: 'none', float:"left", paddingRight:"15px", paddingLeft:"20px", height:30, lineHeight:'30px', fontSize:'18px'}}>
          <Typography.Text strong className={cx('title')} >
            直播视频标注
          </Typography.Text>
          <Tooltip title="输入 liveStreamId 搜索">
            <QuestionCircleOutlined style={{ color: '#999' ,paddingLeft:'5px'}} />
          </Tooltip>
        </Flex>

        {/* Search 输入框 */}
        <Flex style={{ flex: 1, minWidth: 200, float:"left"}}>
          <Input.Search
              placeholder={`liveStreamId ${liveStreamId}`}
              value={searchValue}
              onChange={e => setSearchValue(e.target.value)}
              enterButton
              style={{ width: '100%' }}
              onSearch={value => {
                // 值同步到 liveStreamId，触发 localStorage & 数据拉取
                const id = Number(value);
                setSearchValue(value);
                setLiveStreamId(id);
                fetchInfo(id);

              }
          }
          />
        </Flex>

        {/* 推荐链接 和 SOP文档 */}
        <Flex gap={8} align="center" style={{ flex: 'none', whiteSpace: 'nowrap', height:30, lineHeight:'30px', fontSize:'18px'}}>
          <Typography.Text type="secondary" style={{ fontSize: 14,  paddingLeft:"10px" }}>
            试试这些：
          </Typography.Text>
          {TEST_VIDEO_IDS.map(item => (
              <Typography.Link
                  key={item}
                  type="secondary"
                  style={{ fontSize: 14 }}
                  onClick={() => {
                    setSearchValue(String(item));  // 更新搜索框的值
                    setLiveStreamId(Number(item)); // 更新 liveStreamId
                    fetchInfo(Number(item));       // 拉取数据
                  }}
              >
                {item}
              </Typography.Link>
          ))}

          <a
              href={SOP_LINK}
              target="_blank"
              rel="noopener noreferrer"
          >
            <Button
                type="link"
                style={{ paddingLeft: 0, fontSize: 16, color: '#1975fa', display: 'flex', alignItems: 'center',float:"right",marginRight:"50px" }}
            >
              <QuestionCircleOutlined style={{ marginRight: 1 }} /> 标注SOP文档
            </Button>
          </a>

        </Flex >

      </Flex>

      <Flex className={cx('columns')} gap={16} style={{ display: 'flex', flex: 1, width: '100%' ,paddingRight:"40px"}}>
        <Flex className={cx('left')} vertical gap={16}  style={{ flex: '0 0 420px' }}  >
          <StickyBox offsetTop={20} className={cx('sticky-box')}>
            <Flex vertical gap={8} align="center" className={cx('player')}>
              <KwaiPlayerReact
                key={liveStreamId}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                id="player"
                className="player"
                ref={playerRef}
                onError={() => { if (srcIndex + 1 < videoUrls.length) setSrcIndex(srcIndex + 1); }}
                src={videoSrc}
                autoPlay={false}
                preload="auto"
                onTimeUpdate={onTimeUpdate}
                onLoadedMetadata={onLoadedMetadata}
              />
            </Flex>
          </StickyBox>
        </Flex>

        <Flex className={cx('right')} flex={1} style={{ minWidth: 0, justifyContent: 'center' }} >
          <Tabs activeKey={activeKey} onChange={handleTabChange} onTabClick={handleTabClick}>
            <Tabs.TabPane tab="当前标注" key="current">
              <CurrentRecord
                  dataList={filteredProducts}
                  dataHistory={history}
                  liveStreamId={liveStreamId}
                  onSelectChange={keys => console.log('select', keys)}
                  playerRef={videoRefForChild}
                  duration={playerRef.current?.duration ?? 0}
                  playerCurrentTime={currentTime}
                  onHistoryRefresh={fetchHistory}
              />
            </Tabs.TabPane>
            <Tabs.TabPane tab="历史标注记录" key="history">
              <HistoryRecord data={history}
                             onDelete={async (liveStreamId, liveLabelRecordId) => {
                               try {
                                 await API.LabelDelete({ liveStreamId, liveLabelRecordId });
                                 message.success('删除成功');
                                 fetchHistory();
                               } catch (err) {
                                 console.error(err);
                                 message.error('删除失败，请重试');
                               }
              }}/>
            </Tabs.TabPane>
          </Tabs>
        </Flex>
      </Flex>
    </Flex>
  );
}
