import React, { useEffect, useMemo, useState } from 'react';
import { Button, ConfigProvider, Image, Input, message, Modal, Table, Tooltip, Tag } from 'antd';
import { InfoCircleOutlined, SearchOutlined } from '@ant-design/icons';

import * as API from '@/common/API/VideoLabelAPI';
import * as StreamAPI from '@/common/API/StreamLabelAPI';
import { ITEM_LINK_PREFIX } from '@/pages/VideoLabel/constant';
import zhCN from 'antd/es/locale/zh_CN';
import { useSearchParams } from 'react-router-dom';

export interface ProductItem {
  serialNumber: number;
  itemId: number;
  title: string;
  imageUrl: string;
  imageUrls: string[];
  itemPrice: string;
  itemCategoryPropInfo: Record<string, string | undefined>;
  anchorRecordStartTime: number | null;
  anchorRecordEndTime: number | null;
}

interface CurrentRecordProps {
  dataList: ProductItem[];
  dataHistory: any[]; // Changed from HistoryItem[] to any[]
  liveStreamId?: number;
  sliceId?: number;
  sliceEndTime?: number;
  onSelectChange: (selectedKeys: string[]) => void;
  onHistoryRefresh;
  selectedRowKeys?: string[]; // 已选择的商品ID
  mmuItemId?: number; // 需要高亮的商品ID
  mmuItemIdList?: number[]; // MMU商品ID列表
}

const CurrentRecord: React.FC<CurrentRecordProps> = ({
  sliceId: propSliceId,
  liveStreamId,
  dataList,
  dataHistory,
  onSelectChange,
  onHistoryRefresh,
  selectedRowKeys: propSelectedRowKeys = [],
  mmuItemId,
  mmuItemIdList = [],
}) => {
  const [marker, setMarker] = useState<[string, string]>(['', '']); // start, end

  const [sliceEndTimeValue, setSliceEndTimeValue] = useState(0);

  const [showSerial, setShowSerial] = useState(false);

  // 从 URL 中获取 sliceId 和 labelType
  const urlParams = new URLSearchParams(window.location.search);
  const urlSliceId = urlParams.get('sliceId');
  const urlLabelType = urlParams.get('labelType');
  const [currentSliceId, setCurrentSliceId] = useState<number | undefined>(
    urlSliceId ? parseInt(urlSliceId) : propSliceId,
  );
  const [currentLabelType, setCurrentLabelType] = useState<number>(
    urlLabelType ? parseInt(urlLabelType) : 0,
  );

  // 渲染"商品图片"列
  const renderImageUrlsCell = (urls: string[]) => {
    const maxVisible = 3;
    const visible = urls.slice(0, maxVisible);
    const hidden = urls.slice(maxVisible);

    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        {visible.map((url, idx) => (
          <Image
            key={idx}
            width={150}
            height={150}
            src={url}
            style={{ marginRight: hidden.length > 0 ? 4 : 0 }}
            loading="lazy"
          />
        ))}
        {hidden.length > 0 && (
          <Tooltip
            title={
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                {hidden.map((url, idx) => (
                  <Image key={idx} width={150} height={150} src={url} />
                ))}
              </div>
            }
          >
            <span style={{ cursor: 'pointer', color: '#888', marginLeft: 4 }}>
              +{hidden.length}
            </span>
          </Tooltip>
        )}
      </div>
    );
  };

  const columns = useMemo(() => {
    const base = [
      {
        title: '序号',
        dataIndex: 'serialNumber',
        key: 'serialNumber',
        width: 50,
      },
      {
        title: '商品ID',
        dataIndex: 'itemId',
        key: 'itemId',
        width: 150,
        render: (itemId: number) => (
          <a href={`${ITEM_LINK_PREFIX}${itemId}`} target="_blank" rel="noopener noreferrer">
            {itemId}
          </a>
        ),
      },
      {
        title: '商品标题',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        render: (text: string, record: ProductItem) => (
          <div
            style={{
              whiteSpace: 'normal',
              wordBreak: 'break-word',
            }}
          >
            {text}
            {mmuItemIdList.includes(record.itemId) && (
              <Tag color="orange" style={{ fontSize: '12px' }}>
                MMU商品
              </Tag>
            )}
          </div>
        ),
      },
      {
        title: '价格',
        dataIndex: 'itemPrice',
        key: 'itemPrice',
        width: 120,
        render: (price: string) => {
          if (!price) return <span></span>;
          return <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>¥{price}</span>;
        },
      },
      {
        title: '商品属性',
        dataIndex: 'itemCategoryPropInfo',
        key: 'itemCategoryPropInfo',
        width: 260,
        render: (info?: Record<string, string> | null) => {
          const entries = Object.entries(info ?? {});
          return (
            <div style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>
              {entries.map(([prop, val]) => (
                <div key={prop}>
                  {prop}: {val}
                </div>
              ))}
            </div>
          );
        },
      },
      {
        title: '商品图片',
        dataIndex: 'imageUrls',
        key: 'imageUrls',
        width: 500,
        render: renderImageUrlsCell,
      },
    ];

    if (showSerial) {
      return [...base];
    }

    return base;
  }, [showSerial, mmuItemId]);

  const rowSelection = {
    selectedRowKeys: propSelectedRowKeys,
    onChange: (keys: React.Key[]) => {
      onSelectChange(keys as string[]);
    },
  };

  // 标注提交后重置
  const handleReset = () => {
    setMarker(['', '']);
  };

  return (
    <div>
      {/*商品信息表格*/}
      <ConfigProvider locale={zhCN}>
        <Table<ProductItem>
          rowSelection={rowSelection}
          columns={columns}
          dataSource={dataList}
          rowKey="itemId"
          rowClassName={(record) => {
            // 如果当前行的 itemId 等于 mmuItemId，则添加高亮样式
            return record.itemId === mmuItemId ? 'mmu-item-highlight' : '';
          }}
          pagination={{
            pageSize: 100,
            showSizeChanger: false, // 不允许用户修改
            showQuickJumper: true, // 可选：支持跳页
            showTotal: (total, range) =>
              `共 ${total} 条，当前显示第 ${range?.[0]}-${range?.[1]} 条`,
          }}
          size="middle"
        />
      </ConfigProvider>
    </div>
  );
};

export default CurrentRecord;
