# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ad/ad-front-trace@1.0.10-alpha.1":
  version "1.0.10-alpha.1"
  resolved "https://npm.corp.kuaishou.com/@ad/ad-front-trace/-/@ad/ad-front-trace-1.0.10-alpha.1.tgz#72adb58041b6f0b4d03ddeabd68193fa9ce4157d"
  integrity sha512-IjNo2TizDGXCaHE0QnkdAsSdLHEjlxoe51tCafW9Eo0o1j0ijStUOLjfrA5KdJRMT/FiurZysw7Tf7csLptWLQ==
  dependencies:
    typescript "^4.2.4"
    uuid "^8.3.2"

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://npm.corp.kuaishou.com/@ampproject/remapping/-/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@ant-design/charts-util@0.0.1":
  version "0.0.1"
  resolved "https://npm.corp.kuaishou.com/@ant-design/charts-util/-/charts-util-0.0.1.tgz#49a4e93c1dfc25e29c9353131f96611ee53cbcd2"
  integrity sha512-zz9aCD8z90gzLm3XK17jyFdVtmpLrFApvexzIl5n9+TrxvIgrmOIqemlvx6QvzkmmXcOA6VIEJGzqQBSBAq55A==
  dependencies:
    lodash "^4.17.21"

"@ant-design/charts-util@0.0.1-alpha.7":
  version "0.0.1-alpha.7"
  resolved "https://npm.corp.kuaishou.com/@ant-design/charts-util/-/charts-util-0.0.1-alpha.7.tgz#39152b7106970faa226ba857fae64a0eb32f30b9"
  integrity sha512-Yh0o6EdO6SvdSnStFZMbnUzjyymkVzV+TQ9ymVW9hlVgO/fUkUII3JYSdV+UVcFnYwUF0YiDKuSTLCZNAzg2bQ==
  dependencies:
    lodash "^4.17.21"

"@ant-design/charts@^2.3.0":
  version "2.3.0"
  resolved "https://npm.corp.kuaishou.com/@ant-design/charts/-/charts-2.3.0.tgz#223921bcff342da5d2bd6ab4dc6ce92497e5353e"
  integrity sha512-uX/Re6C7t+JleAYKyWu2jv08gAxq2QcZVeGjfgNCV3VC/ODBmqRvu8H0m3Lrla2aHKjCyksirTZI7y2BOKfA1w==
  dependencies:
    "@ant-design/graphs" "^2.1.0"
    "@ant-design/plots" "^2.4.0"
    lodash "^4.17.21"

"@ant-design/colors@^6.0.0":
  version "6.0.0"
  resolved "https://npm.corp.kuaishou.com/@ant-design/colors/-/colors-6.0.0.tgz#9b9366257cffcc47db42b9d0203bb592c13c0298"
  integrity sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==
  dependencies:
    "@ctrl/tinycolor" "^3.4.0"

"@ant-design/colors@^7.0.0", "@ant-design/colors@^7.2.0":
  version "7.2.0"
  resolved "https://npm.corp.kuaishou.com/@ant-design/colors/-/colors-7.2.0.tgz#80d7325d20463f09c7839d28da630043dd5c263a"
  integrity sha512-bjTObSnZ9C/O8MB/B4OUtd/q9COomuJAR2SYfhxLyHvCKn4EKwCN3e+fWGMo7H5InAyV0wL17jdE9ALrdOW/6A==
  dependencies:
    "@ant-design/fast-color" "^2.0.6"

"@ant-design/cssinjs-utils@^1.1.3":
  version "1.1.3"
  resolved "https://npm.corp.kuaishou.com/@ant-design/cssinjs-utils/-/cssinjs-utils-1.1.3.tgz#5dd79126057920a6992d57b38dd84e2c0b707977"
  integrity sha512-nOoQMLW1l+xR1Co8NFVYiP8pZp3VjIIzqV6D6ShYF2ljtdwWJn5WSsH+7kvCktXL/yhEtWURKOfH5Xz/gzlwsg==
  dependencies:
    "@ant-design/cssinjs" "^1.21.0"
    "@babel/runtime" "^7.23.2"
    rc-util "^5.38.0"

"@ant-design/cssinjs@^1.21.0", "@ant-design/cssinjs@^1.23.0":
  version "1.23.0"
  resolved "https://npm.corp.kuaishou.com/@ant-design/cssinjs/-/cssinjs-1.23.0.tgz#492efba9b15d64f42a4cb5d568cab0607d0c2b16"
  integrity sha512-7GAg9bD/iC9ikWatU9ym+P9ugJhi/WbsTWzcKN6T4gU0aehsprtke1UAaaSxxkjjmkJb3llet/rbUSLPgwlY4w==
  dependencies:
    "@babel/runtime" "^7.11.1"
    "@emotion/hash" "^0.8.0"
    "@emotion/unitless" "^0.7.5"
    classnames "^2.3.1"
    csstype "^3.1.3"
    rc-util "^5.35.0"
    stylis "^4.3.4"

"@ant-design/fast-color@^2.0.6":
  version "2.0.6"
  resolved "https://npm.corp.kuaishou.com/@ant-design/fast-color/-/fast-color-2.0.6.tgz#ab4d4455c1542c9017d367c2fa8ca3e4215d0ba2"
  integrity sha512-y2217gk4NqL35giHl72o6Zzqji9O7vHh9YmhUVkPtAOpoTCH4uWxo/pr4VE8t0+ChEPs0qo4eJRC5Q1eXWo3vA==
  dependencies:
    "@babel/runtime" "^7.24.7"

"@ant-design/graphs@^2.1.0":
  version "2.1.0"
  resolved "https://npm.corp.kuaishou.com/@ant-design/graphs/-/graphs-2.1.0.tgz#311f3534fd728d58476d43b149e235424a46c8bf"
  integrity sha512-JavZyJVDRyO5wjReqz3CRYhml5MMpOe+fT4ucebdkfOfWYTlOG+W9vxtNSITJmCGHUVphQkQo9r1CPkZysDT0g==
  dependencies:
    "@ant-design/charts-util" "0.0.1-alpha.7"
    "@antv/g6" "^5.0.44"
    "@antv/g6-extension-react" "^0.2.0"
    "@antv/graphin" "^3.0.4"
    lodash "^4.17.21"
    styled-components "^6.1.15"

"@ant-design/icons-svg@^4.3.0", "@ant-design/icons-svg@^4.4.0":
  version "4.4.2"
  resolved "https://npm.corp.kuaishou.com/@ant-design/icons-svg/-/icons-svg-4.4.2.tgz#ed2be7fb4d82ac7e1d45a54a5b06d6cecf8be6f6"
  integrity sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA==

"@ant-design/icons@4.8.3":
  version "4.8.3"
  resolved "https://npm.corp.kuaishou.com/@ant-design/icons/-/icons-4.8.3.tgz#41555408ed5e9b0c3d53f3f24fe6a73abfcf4000"
  integrity sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/icons-svg" "^4.3.0"
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    lodash "^4.17.15"
    rc-util "^5.9.4"

"@ant-design/icons@^5.6.1":
  version "5.6.1"
  resolved "https://npm.corp.kuaishou.com/@ant-design/icons/-/icons-5.6.1.tgz#7290fcdc3d96ff3fca793ed399053cd29ad5dbd3"
  integrity sha512-0/xS39c91WjPAZOWsvi1//zjx6kAp4kxWwctR6kuU6p133w8RU0D2dSCvZC19uQyharg/sAvYxGYWl01BbZZfg==
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    classnames "^2.2.6"
    rc-util "^5.31.1"

"@ant-design/plots@^2.4.0":
  version "2.4.0"
  resolved "https://npm.corp.kuaishou.com/@ant-design/plots/-/plots-2.4.0.tgz#0aca0781b64a4cff1d7149bb95637de310b9c04e"
  integrity sha512-5JxX6gDp9VyQizkQsCBKjGqlHpgKhfV6XTRNqKnrJMYet0FBNO0srDsa/rmQoZZLxMRvE8eZhCXnM7DhRUWUdA==
  dependencies:
    "@ant-design/charts-util" "0.0.1"
    "@antv/event-emitter" "^0.1.3"
    "@antv/g" "^6.1.7"
    "@antv/g2" "^5.2.7"
    "@antv/g2-extension-plot" "^0.2.1"
    lodash "^4.17.21"

"@ant-design/react-slick@~0.28.1":
  version "0.28.4"
  resolved "https://npm.corp.kuaishou.com/@ant-design/react-slick/-/react-slick-0.28.4.tgz#8b296b87ad7c7ae877f2a527b81b7eebd9dd29a9"
  integrity sha512-j9eAHTn7GxbXUFNknJoHS2ceAsqrQi2j8XykjZE1IXCD8kJF+t28EvhBLniDpbOsBk/3kjalnhriTfZcjBHNqg==
  dependencies:
    "@babel/runtime" "^7.10.4"
    classnames "^2.2.5"
    json2mq "^0.2.0"
    lodash "^4.17.21"
    resize-observer-polyfill "^1.5.0"

"@ant-design/react-slick@~1.1.2":
  version "1.1.2"
  resolved "https://npm.corp.kuaishou.com/@ant-design/react-slick/-/react-slick-1.1.2.tgz#f84ce3e4d0dc941f02b16f1d1d6d7a371ffbb4f1"
  integrity sha512-EzlvzE6xQUBrZuuhSAFTdsr4P2bBBHGZwKFemEfq8gIGyIQCxalYfZW/T2ORbtQx5rU69o+WycP3exY/7T1hGA==
  dependencies:
    "@babel/runtime" "^7.10.4"
    classnames "^2.2.5"
    json2mq "^0.2.0"
    resize-observer-polyfill "^1.5.1"
    throttle-debounce "^5.0.0"

"@antv/algorithm@^0.1.26":
  version "0.1.26"
  resolved "https://npm.corp.kuaishou.com/@antv/algorithm/-/algorithm-0.1.26.tgz#e3f5e7f1d8db5b415c3f31e32b119cbcafc8f5de"
  integrity sha512-DVhcFSQ8YQnMNW34Mk8BSsfc61iC1sAnmcfYoXTAshYHuU50p/6b7x3QYaGctDNKWGvi1ub7mPcSY0bK+aN0qg==
  dependencies:
    "@antv/util" "^2.0.13"
    tslib "^2.0.0"

"@antv/component@^2.1.2":
  version "2.1.2"
  resolved "https://npm.corp.kuaishou.com/@antv/component/-/component-2.1.2.tgz#578a08abc1e70755dd2a2bad85ad7015dcfecf84"
  integrity sha512-5nC9i9lh5rBHE+pk4TNnerLe4mn5874YHHhvv6EdL618UkgpdKJL0hJu4l7uAYjZ3g46VBK+IYT7md0FYv8f4w==
  dependencies:
    "@antv/g" "^6.1.11"
    "@antv/scale" "^0.4.16"
    "@antv/util" "^3.3.10"
    svg-path-parser "^1.1.0"

"@antv/coord@^0.4.7":
  version "0.4.7"
  resolved "https://npm.corp.kuaishou.com/@antv/coord/-/coord-0.4.7.tgz#3ef6c6e3f9ca0f024b90888549946061f35df77a"
  integrity sha512-UTbrMLhwJUkKzqJx5KFnSRpU3BqrdLORJbwUbHK2zHSCT3q3bjcFA//ZYLVfIlwqFDXp/hzfMyRtp0c77A9ZVA==
  dependencies:
    "@antv/scale" "^0.4.12"
    "@antv/util" "^2.0.13"
    gl-matrix "^3.4.3"

"@antv/event-emitter@^0.1.3":
  version "0.1.3"
  resolved "https://npm.corp.kuaishou.com/@antv/event-emitter/-/event-emitter-0.1.3.tgz#3e06323b9dcd55a3241ddc7c5458cfabd2095164"
  integrity sha512-4ddpsiHN9Pd4UIlWuKVK1C4IiZIdbwQvy9i7DUSI3xNJ89FPUFt8lxDYj8GzzfdllV0NkJTRxnG+FvLk0llidg==

"@antv/expr@^1.0.2":
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/@antv/expr/-/expr-1.0.2.tgz#a75fa0a0f132fe47bedf6c8b93a847997ffe53ff"
  integrity sha512-vrfdmPHkTuiS5voVutKl2l06w1ihBh9A8SFdQPEE+2KMVpkymzGOF1eWpfkbGZ7tiFE15GodVdhhHomD/hdIwg==

"@antv/g-camera-api@2.0.35":
  version "2.0.35"
  resolved "https://npm.corp.kuaishou.com/@antv/g-camera-api/-/g-camera-api-2.0.35.tgz#0c8f5824f4525b2fed9941170aa9e668b9c5734f"
  integrity sha512-z4WKmB6yN2fFi9EnapjuHbFVF0ilhMrWo2eZCxYXcb0dV5MiflU/WZi/bjs4WqVMJPNtYKx+yZhTyROncEiglw==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-camera-api@2.0.38":
  version "2.0.38"
  resolved "https://npm.corp.kuaishou.com/@antv/g-camera-api/-/g-camera-api-2.0.38.tgz#8a2ddc59aae648fcf31a675287fbb7571bcde9d3"
  integrity sha512-BgFkUMcTO06Oz37Z+hVqxATwdWFE5DfBgMKlFaMwKKF/8n+7eNhlif1KBfcf2rEfGijS0FD0ZGKCr9uJ06+GIg==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-canvas@2.0.40":
  version "2.0.40"
  resolved "https://npm.corp.kuaishou.com/@antv/g-canvas/-/g-canvas-2.0.40.tgz#4d550e891c6bc2e51acc42eb877c21e722add68d"
  integrity sha512-Starh5g+ydOFKzfK/GpwnLwz+o6UZNHkyWBXdHx2ax/AJWHVXwjyCadt/6kkc2non0ts2ow/hpJaW7X3dgdDxQ==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-plugin-canvas-path-generator" "2.1.16"
    "@antv/g-plugin-canvas-picker" "2.1.19"
    "@antv/g-plugin-canvas-renderer" "2.2.19"
    "@antv/g-plugin-dom-interaction" "2.1.21"
    "@antv/g-plugin-html-renderer" "2.1.21"
    "@antv/g-plugin-image-loader" "2.1.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-canvas@^2.0.42":
  version "2.0.43"
  resolved "https://npm.corp.kuaishou.com/@antv/g-canvas/-/g-canvas-2.0.43.tgz#e0f93529d265a6b995e0ab3bcb59877c1e5d843c"
  integrity sha512-iAMX+b1eEhwddhm7qiQAtNsnwJQDQsiE5/ELgzni0UHF28zMVTlc5FdDMXKLPuaYs9S/dw/MXBtqctRj2IjA1g==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/g-plugin-canvas-path-generator" "2.1.19"
    "@antv/g-plugin-canvas-picker" "2.1.22"
    "@antv/g-plugin-canvas-renderer" "2.2.22"
    "@antv/g-plugin-dom-interaction" "2.1.24"
    "@antv/g-plugin-html-renderer" "2.1.24"
    "@antv/g-plugin-image-loader" "2.1.22"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-dom-mutation-observer-api@2.0.32":
  version "2.0.32"
  resolved "https://npm.corp.kuaishou.com/@antv/g-dom-mutation-observer-api/-/g-dom-mutation-observer-api-2.0.32.tgz#171361ff66970c620fd5320b51dd79e4add3631f"
  integrity sha512-50r7en1+doUtR9uXmFJk8YtENQ/+DFcj2g3a4XKu9xp58kmF2qBgtdst9n1deqGcL5s0ufX/Ck9rUhtHwka+Ow==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@babel/runtime" "^7.25.6"

"@antv/g-dom-mutation-observer-api@2.0.35":
  version "2.0.35"
  resolved "https://npm.corp.kuaishou.com/@antv/g-dom-mutation-observer-api/-/g-dom-mutation-observer-api-2.0.35.tgz#57633a3a54ac091d64427e42eea8b43fa334b408"
  integrity sha512-bAl3ViXDHvLEbGvGZwZBg4gpoNjUTwVQ3XTmRAkymkFGkUy+KV0ZwFdqEegP25TQGPl85er/hB6MCu6Yt58AJA==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@babel/runtime" "^7.25.6"

"@antv/g-lite@2.2.16":
  version "2.2.16"
  resolved "https://npm.corp.kuaishou.com/@antv/g-lite/-/g-lite-2.2.16.tgz#3aad1e45c7dca71d536ec7874d5dfb8a9ed4fdcb"
  integrity sha512-473r6S5srkxUiUxI3ZkrM74HMkgyO9+2HR1xtJ75yDOOuT8F6osdXDgy0Or5cWqOlsVjiN3L3DaPnQLHlUGO5A==
  dependencies:
    "@antv/g-math" "3.0.0"
    "@antv/util" "^3.3.5"
    "@antv/vendor" "^1.0.3"
    "@babel/runtime" "^7.25.6"
    eventemitter3 "^5.0.1"
    gl-matrix "^3.4.3"
    rbush "^3.0.1"
    tslib "^2.5.3"

"@antv/g-lite@2.2.19":
  version "2.2.19"
  resolved "https://npm.corp.kuaishou.com/@antv/g-lite/-/g-lite-2.2.19.tgz#ef8f4fa4f0e1e95f7f27b2e341196ddc8a8794f8"
  integrity sha512-QfxZsbLGTSGL18NgSOAVQURXC3xMXbmmS125EF7/vCzW2Lw2nF5I8k0KW4N09ty+/FtVpSESJX652g2phIvd5g==
  dependencies:
    "@antv/g-math" "3.0.1"
    "@antv/util" "^3.3.5"
    "@antv/vendor" "^1.0.3"
    "@babel/runtime" "^7.25.6"
    eventemitter3 "^5.0.1"
    gl-matrix "^3.4.3"
    rbush "^3.0.1"
    tslib "^2.5.3"

"@antv/g-math@3.0.0":
  version "3.0.0"
  resolved "https://npm.corp.kuaishou.com/@antv/g-math/-/g-math-3.0.0.tgz#834d993391546e39ae5a30452572fdc49a7c57ec"
  integrity sha512-AkmiNIEL1vgqTPeGY2wtsMdBBqKFwF7SKSgs+D1iOS/rqYMsXdhp/HvtuQ5tx/HdawE/ZzTiicIYopc520ADZw==
  dependencies:
    "@antv/util" "^3.3.5"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-math@3.0.1":
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/@antv/g-math/-/g-math-3.0.1.tgz#8c95b96c04a656345f8bfff9d100fa0b5708a2f0"
  integrity sha512-FvkDBNRpj+HsLINunrL2PW0OlG368MlpHuihbxleuajGim5kra8tgISwCLmAf8Yz2b1CgZ9PvpohqiLzHS7HLg==
  dependencies:
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-path-generator@2.1.16":
  version "2.1.16"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-canvas-path-generator/-/g-plugin-canvas-path-generator-2.1.16.tgz#f60dfa687027aba12aed90d64839b97b2c3c3be0"
  integrity sha512-E3/HUzWRv1/5QyKHLcXIgFJff0JBxDHz4NfHwYp6IOy5P/A1mbISsUjwafSl8JIVqx0J81CzgqpwU7pWHeXlaQ==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-math" "3.0.0"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-path-generator@2.1.19":
  version "2.1.19"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-canvas-path-generator/-/g-plugin-canvas-path-generator-2.1.19.tgz#dc77fec5bee2e267d2b9383d2b29d2bee4d02080"
  integrity sha512-+tc97NLvVYEFQnrLffmyxPpVXwUuTPbXBGy3aUTBYKd3YXhFBIKJYpQR39jsX2skgUvLh/67ZtA9QeUt6U41oQ==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/g-math" "3.0.1"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-picker@2.1.19":
  version "2.1.19"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-canvas-picker/-/g-plugin-canvas-picker-2.1.19.tgz#d4503b1819808703c765a51b0e3bccef5de4e4cd"
  integrity sha512-69G0m2v09FimmYSU+hO1wjft1FqM467Cf1jDpjBz6Y3caQ98Hrqpz/7Prko1hMOALCo92MDo65yTTnz/LhBiQA==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-math" "3.0.0"
    "@antv/g-plugin-canvas-path-generator" "2.1.16"
    "@antv/g-plugin-canvas-renderer" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-picker@2.1.22":
  version "2.1.22"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-canvas-picker/-/g-plugin-canvas-picker-2.1.22.tgz#e7a870a6d63576fd860e5caaa2053ee1f1020414"
  integrity sha512-Pm/N+YTFOlXtjPran3wfN7Iuv0i2YglXrByxvBY8IQ3IzmB68+yZr+yPNRqjgbIM0yh45vIxz3SB/7VwYDPXMA==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/g-math" "3.0.1"
    "@antv/g-plugin-canvas-path-generator" "2.1.19"
    "@antv/g-plugin-canvas-renderer" "2.2.22"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-renderer@2.2.19":
  version "2.2.19"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-canvas-renderer/-/g-plugin-canvas-renderer-2.2.19.tgz#90911b38ec15edbbc946f7201ce9090bd22a43cd"
  integrity sha512-3Ac0pjU0NAafu0rwTnthwWV/CV5kV9CpTf96v1CCXX0P3iPWtW72SatQNOt/v2aQ2NjYB34YuwYy9i0U1oS8rg==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-math" "3.0.0"
    "@antv/g-plugin-canvas-path-generator" "2.1.16"
    "@antv/g-plugin-image-loader" "2.1.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-renderer@2.2.22":
  version "2.2.22"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-canvas-renderer/-/g-plugin-canvas-renderer-2.2.22.tgz#69d0fe31db160245610a7af8c71085c783760fd9"
  integrity sha512-+KyorG9lmwx5mogYZ6DP6BaiE9bnGUHojUxg+pTq032dx3SkCkLBl+CB5pZz1C2LEiQ+NkYtP3PlEjgSeeR/9A==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/g-math" "3.0.1"
    "@antv/g-plugin-canvas-path-generator" "2.1.19"
    "@antv/g-plugin-image-loader" "2.1.22"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-dom-interaction@2.1.21":
  version "2.1.21"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-dom-interaction/-/g-plugin-dom-interaction-2.1.21.tgz#7a764b270a2da8fc367a763231071f38d5ac49cf"
  integrity sha512-Vm8yeNjZ2aNgNH3LwDRExRChpuVv0Wv2zOblUGy5rgyRIh2Fkm8R89pKLmd3GlLo4AF1ZqAGWHiY2WOeMHEEIA==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-dom-interaction@2.1.24":
  version "2.1.24"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-dom-interaction/-/g-plugin-dom-interaction-2.1.24.tgz#5e2001d0451293573958763a37b4cc1a6d1ffd27"
  integrity sha512-1IrsUp2k+4oi2brVNstgxoisdwcdwqSNdEYJBDtVP1Bv5KZabKSs9lxlkxVR0DTb8BJtWBi80gmKQFIJ8znofQ==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-dragndrop@2.0.32":
  version "2.0.32"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-dragndrop/-/g-plugin-dragndrop-2.0.32.tgz#31559d38c5401a5116a6a8b7c64ba8c939208186"
  integrity sha512-0Y9S/jx6Z7O3hEQhqrXGWNIcV1dBoRpokSP9gIMqTxOjCLzVUFYv8pFoI+Uyeow6PAWe+gdBQu+EJgVi223lJQ==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-dragndrop@^2.0.34":
  version "2.0.35"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-dragndrop/-/g-plugin-dragndrop-2.0.35.tgz#a44ab89fbb7214e88dc6c25daf328fee69d24efb"
  integrity sha512-1ZG+j91uEQAiFN0UqRkYCx3G8WWlKYoCXgTTx6m4YFJESJiab5M1C4OAi7zXclt1maOR154x3L/j3sRmBHFA+A==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-html-renderer@2.1.21":
  version "2.1.21"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-html-renderer/-/g-plugin-html-renderer-2.1.21.tgz#2077e5eae60c818962f275f3cf73044a0c8aaa88"
  integrity sha512-1PR9rYt4BgSx8LFnVPF+cPlcBYKfI7iWK/xPipEa3jZ4j/xftELQ5EEyZpfPnrTqu2PtKeMurx7oaM/HPsgaiQ==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-html-renderer@2.1.24":
  version "2.1.24"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-html-renderer/-/g-plugin-html-renderer-2.1.24.tgz#c5f2e7c43b52f72a34d9542f9b190663c9b65f26"
  integrity sha512-UPEitSu5F42kRgqy8Cr34aC6O4+0cCnC+avv0ZMXUFOf7AMhMnjQLlHHo+GDfM/0r6m//0ZCsqHpv8vB0A+sUA==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-image-loader@2.1.19":
  version "2.1.19"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-image-loader/-/g-plugin-image-loader-2.1.19.tgz#c94e63de91b99c7384ecd758687e78a21b1a202b"
  integrity sha512-ZjNs08RkzdDMLlEWGabJG1Lu1Q71afStSlhcIRhrDOLB4tH0UdYlq/f72tlzJ6KjtLnril/xQH3D7znPlfAoig==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-image-loader@2.1.22":
  version "2.1.22"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-image-loader/-/g-plugin-image-loader-2.1.22.tgz#1f8ee3da3a4cc7a51adc9bc281b12d00968ff43c"
  integrity sha512-moA+EnV8Gnofj5Kk6btQ6DrufPYiGMvGWXrkBqHNrowfIUD+bFfHPnLm4gV52hHKYEvwnIP2XL+ayHRvzVyffw==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-svg-picker@2.0.38":
  version "2.0.38"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-svg-picker/-/g-plugin-svg-picker-2.0.38.tgz#879289b50e6d76a0ba5b482b21b4e1404bc37411"
  integrity sha512-9XuT3VRFtUrdhMYmib7uB/sjXG9orQ7yGzIwYp+mCI734mnmJApOrB+J3UcSt3s+1PAIcABQkHT1MRxFII2w7w==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/g-plugin-svg-renderer" "2.2.20"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-svg-renderer@2.2.20":
  version "2.2.20"
  resolved "https://npm.corp.kuaishou.com/@antv/g-plugin-svg-renderer/-/g-plugin-svg-renderer-2.2.20.tgz#c98fbbc20dfe1e5880a1e7ee206a7b79cd646f88"
  integrity sha512-HjLyQMcMm/kRVhwkmdEkWGGZAHUhIuyztOzO0dzWucfGqXsusNZvKHpiWUMl3DBm6ID6qziYCRw5IIpqlsh3Jw==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-svg@^2.0.27":
  version "2.0.38"
  resolved "https://npm.corp.kuaishou.com/@antv/g-svg/-/g-svg-2.0.38.tgz#c74e32a49c6c55c466d70aba1548ef5e11aac972"
  integrity sha512-S11RB+4Yh3nel+wHChcbB4TlaJFyKl4gP9sUUgUzwgWiAFNxiHU4fM3+sb3f4AQyToIZZd1sqH0TscQ3psX5Yg==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/g-plugin-dom-interaction" "2.1.24"
    "@antv/g-plugin-svg-picker" "2.0.38"
    "@antv/g-plugin-svg-renderer" "2.2.20"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-web-animations-api@2.1.21":
  version "2.1.21"
  resolved "https://npm.corp.kuaishou.com/@antv/g-web-animations-api/-/g-web-animations-api-2.1.21.tgz#4f8fc78d766a0dc4d51d4e37a917a91c59eb02fb"
  integrity sha512-EkIjeEH3QzHkDJn3sz1Mk83PqVQXGe5440mJV42QmnxuFuFcxGVJMi9vS8Te7kCUJl4eSb/eqnNi5AWfDMWm+w==
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-web-animations-api@2.1.24":
  version "2.1.24"
  resolved "https://npm.corp.kuaishou.com/@antv/g-web-animations-api/-/g-web-animations-api-2.1.24.tgz#d310a3302908bd12e2b45940633a54abc5cbc158"
  integrity sha512-cxRvJblphBw3Mi1YsAZwvQveZlRyNnZ+nidIq/e0HKmQhOLr3dSq3KAwxanV/n84v/a3BqLkWnm3E1CuSKDG3g==
  dependencies:
    "@antv/g-lite" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g2-extension-plot@^0.2.1":
  version "0.2.2"
  resolved "https://npm.corp.kuaishou.com/@antv/g2-extension-plot/-/g2-extension-plot-0.2.2.tgz#3460de8b5a1d485edf3e02d2d32a0d97298b0f89"
  integrity sha512-KJXCXO7as+h0hDqirGXf1omrNuYzQmY3VmBmp7lIvkepbQ7sz3pPwy895r1FWETGF3vTk5UeFcAF5yzzBHWgbw==
  dependencies:
    "@antv/g2" "^5.1.8"
    "@antv/util" "^3.3.5"
    "@antv/vendor" "^1.0.10"

"@antv/g2@^5.1.8", "@antv/g2@^5.2.7":
  version "5.3.2"
  resolved "https://npm.corp.kuaishou.com/@antv/g2/-/g2-5.3.2.tgz#9deb9a523684d687920caba849f495d4b7b67e33"
  integrity sha512-V/+sWq2QdJaWTQjrQBe/TrtWszGZBIuA3SpZfk5DgNFyzxb5wz9+LfsGMMvtLNHKOgRnCIlxSvwbSsSO4sMkBA==
  dependencies:
    "@antv/component" "^2.1.2"
    "@antv/coord" "^0.4.7"
    "@antv/event-emitter" "^0.1.3"
    "@antv/expr" "^1.0.2"
    "@antv/g" "^6.1.23"
    "@antv/g-canvas" "^2.0.42"
    "@antv/g-plugin-dragndrop" "^2.0.34"
    "@antv/scale" "^0.4.16"
    "@antv/util" "^3.3.10"
    "@antv/vendor" "^1.0.8"
    flru "^1.0.2"
    fmin "0.0.2"
    pdfast "^0.2.0"

"@antv/g6-extension-react@^0.2.0":
  version "0.2.2"
  resolved "https://npm.corp.kuaishou.com/@antv/g6-extension-react/-/g6-extension-react-0.2.2.tgz#8c6a30d83c07e4eeadee89f3416115946bc78537"
  integrity sha512-O8bLyOwwkCTd0acoDPDpu6qJ8GNdHX5XYs2o+a75qG1qAvc2w9BH7Dc5W8FDW/9Ckz/Q2Zbht++mwOGN8TmwUw==
  dependencies:
    "@antv/g" "^6.1.14"
    "@antv/g-svg" "^2.0.27"

"@antv/g6@^5.0.28", "@antv/g6@^5.0.44":
  version "5.0.46"
  resolved "https://npm.corp.kuaishou.com/@antv/g6/-/g6-5.0.46.tgz#85fe8aac9a5bb4755d9d1a564aa88882abf8336e"
  integrity sha512-GPQkvDVbo/MABKf9yhFGkdDHLjuxOGfroHmw7AxdXptSr19//LYZTsoucSIBXwjOmfLTtMR0RP/i2rRWNjgyuA==
  dependencies:
    "@antv/algorithm" "^0.1.26"
    "@antv/component" "^2.1.2"
    "@antv/event-emitter" "^0.1.3"
    "@antv/g" "6.1.21"
    "@antv/g-canvas" "2.0.40"
    "@antv/g-plugin-dragndrop" "2.0.32"
    "@antv/graphlib" "^2.0.4"
    "@antv/hierarchy" "^0.6.14"
    "@antv/layout" "1.2.14-beta.9"
    "@antv/util" "^3.3.10"
    bubblesets-js "^2.3.4"

"@antv/g@6.1.21":
  version "6.1.21"
  resolved "https://npm.corp.kuaishou.com/@antv/g/-/g-6.1.21.tgz#d64e5dc8ab07a9ec6b14ed671923b7dfe4b4fc05"
  integrity sha512-3cWmsY1bYwDmVzsFmBeqN1tWVt+3JaWL6Uu54C1oF7qn1VXXa3V3KuXGEYCxuei8E8BMriN3D7fZosY5d+MQqw==
  dependencies:
    "@antv/g-camera-api" "2.0.35"
    "@antv/g-dom-mutation-observer-api" "2.0.32"
    "@antv/g-lite" "2.2.16"
    "@antv/g-web-animations-api" "2.1.21"
    "@babel/runtime" "^7.25.6"

"@antv/g@^6.1.11", "@antv/g@^6.1.14", "@antv/g@^6.1.23", "@antv/g@^6.1.7":
  version "6.1.24"
  resolved "https://npm.corp.kuaishou.com/@antv/g/-/g-6.1.24.tgz#2d1d3fee1cbb8afb32a599384f7a3960df08c2e5"
  integrity sha512-UshMiGnnF9CoaIN9pNhg6yWGU4KloKpGqLhEZU2wN5x11nMVX197yurcpU5vMzAbfdWRLcVW7TJatLQ4rKi1vw==
  dependencies:
    "@antv/g-camera-api" "2.0.38"
    "@antv/g-dom-mutation-observer-api" "2.0.35"
    "@antv/g-lite" "2.2.19"
    "@antv/g-web-animations-api" "2.1.24"
    "@babel/runtime" "^7.25.6"

"@antv/graphin@^3.0.4":
  version "3.0.5"
  resolved "https://npm.corp.kuaishou.com/@antv/graphin/-/graphin-3.0.5.tgz#1e619f16262fd164f1e0d3ed51dad7eb952193b2"
  integrity sha512-V/j8R8Ty44wUqxVIYLdpPuIO8WWCTIVq1eBJg5YRunL5t5o5qAFpC/qkQxslbBMWyKdIH0oWBnvHA74riGi7cw==
  dependencies:
    "@antv/g6" "^5.0.28"

"@antv/graphlib@^2.0.0", "@antv/graphlib@^2.0.4":
  version "2.0.4"
  resolved "https://npm.corp.kuaishou.com/@antv/graphlib/-/graphlib-2.0.4.tgz#7cc4352c91125f1a3ec13852220286fe590568ee"
  integrity sha512-zc/5oQlsdk42Z0ib1mGklwzhJ5vczLFiPa1v7DgJkTbgJ2YxRh9xdarf86zI49sKVJmgbweRpJs7Nu5bIiwv4w==
  dependencies:
    "@antv/event-emitter" "^0.1.3"

"@antv/hierarchy@^0.6.14":
  version "0.6.14"
  resolved "https://npm.corp.kuaishou.com/@antv/hierarchy/-/hierarchy-0.6.14.tgz#4e8b4966c9c2a44aaa6f9da7008c4bd44d490385"
  integrity sha512-V3uknf7bhynOqQDw2sg+9r9DwZ9pc6k/EcqyTFdfXB1+ydr7urisP0MipIuimucvQKN+Qkd+d6w601r1UIroqQ==

"@antv/layout@1.2.14-beta.9":
  version "1.2.14-beta.9"
  resolved "https://npm.corp.kuaishou.com/@antv/layout/-/layout-1.2.14-beta.9.tgz#5c66a0f22158c545aabd1654a50bfc8c3bf93f98"
  integrity sha512-wPlwBFMtq2lWZFc89/7Lzb8fjHnyKVZZ9zBb2h+zZIP0YWmVmHRE8+dqCiPKOyOGUXEdDtn813f1g107dCHZlg==
  dependencies:
    "@antv/event-emitter" "^0.1.3"
    "@antv/graphlib" "^2.0.0"
    "@antv/util" "^3.3.2"
    "@naoak/workerize-transferable" "^0.1.0"
    comlink "^4.4.1"
    d3-force "^3.0.0"
    d3-force-3d "^3.0.5"
    d3-octree "^1.0.2"
    d3-quadtree "^3.0.1"
    dagre "^0.8.5"
    ml-matrix "^6.10.4"
    tslib "^2.5.0"

"@antv/scale@^0.4.12", "@antv/scale@^0.4.16":
  version "0.4.16"
  resolved "https://npm.corp.kuaishou.com/@antv/scale/-/scale-0.4.16.tgz#60557470668ccfe5217e482a01f05c0cbb706b62"
  integrity sha512-5wg/zB5kXHxpTV5OYwJD3ja6R8yTiqIOkjOhmpEJiowkzRlbEC/BOyMvNUq5fqFIHnMCE9woO7+c3zxEQCKPjw==
  dependencies:
    "@antv/util" "^3.3.7"
    color-string "^1.5.5"
    fecha "^4.2.1"

"@antv/util@^2.0.13":
  version "2.0.17"
  resolved "https://npm.corp.kuaishou.com/@antv/util/-/util-2.0.17.tgz#e8ef42aca7892815b229269f3dd10c6b3c7597a9"
  integrity sha512-o6I9hi5CIUvLGDhth0RxNSFDRwXeywmt6ExR4+RmVAzIi48ps6HUy+svxOCayvrPBN37uE6TAc2KDofRo0nK9Q==
  dependencies:
    csstype "^3.0.8"
    tslib "^2.0.3"

"@antv/util@^3.3.10", "@antv/util@^3.3.2", "@antv/util@^3.3.5", "@antv/util@^3.3.7":
  version "3.3.10"
  resolved "https://npm.corp.kuaishou.com/@antv/util/-/util-3.3.10.tgz#6fb2560c0f42df61f824e1f995a1ed1bdb00eb9a"
  integrity sha512-basGML3DFA3O87INnzvDStjzS+n0JLEhRnRsDzP9keiXz8gT1z/fTdmJAZFOzMMWxy+HKbi7NbSt0+8vz/OsBQ==
  dependencies:
    fast-deep-equal "^3.1.3"
    gl-matrix "^3.3.0"
    tslib "^2.3.1"

"@antv/vendor@^1.0.10", "@antv/vendor@^1.0.3", "@antv/vendor@^1.0.8":
  version "1.0.11"
  resolved "https://npm.corp.kuaishou.com/@antv/vendor/-/vendor-1.0.11.tgz#8a87aa964410d9fcc431a74c8757021a942252f2"
  integrity sha512-LmhPEQ+aapk3barntaiIxJ5VHno/Tyab2JnfdcPzp5xONh/8VSfed4bo/9xKo5HcUAEydko38vYLfj6lJliLiw==
  dependencies:
    "@types/d3-array" "^3.2.1"
    "@types/d3-color" "^3.1.3"
    "@types/d3-dispatch" "^3.0.6"
    "@types/d3-dsv" "^3.0.7"
    "@types/d3-ease" "^3.0.2"
    "@types/d3-fetch" "^3.0.7"
    "@types/d3-force" "^3.0.10"
    "@types/d3-format" "^3.0.4"
    "@types/d3-geo" "^3.1.0"
    "@types/d3-hierarchy" "^3.1.7"
    "@types/d3-interpolate" "^3.0.4"
    "@types/d3-path" "^3.1.0"
    "@types/d3-quadtree" "^3.0.6"
    "@types/d3-random" "^3.0.3"
    "@types/d3-scale" "^4.0.9"
    "@types/d3-scale-chromatic" "^3.1.0"
    "@types/d3-shape" "^3.1.7"
    "@types/d3-time" "^3.0.4"
    "@types/d3-timer" "^3.0.2"
    d3-array "^3.2.4"
    d3-color "^3.1.0"
    d3-dispatch "^3.0.1"
    d3-dsv "^3.0.1"
    d3-ease "^3.0.1"
    d3-fetch "^3.0.1"
    d3-force "^3.0.0"
    d3-force-3d "^3.0.5"
    d3-format "^3.1.0"
    d3-geo "^3.1.1"
    d3-geo-projection "^4.0.0"
    d3-hierarchy "^3.1.2"
    d3-interpolate "^3.0.1"
    d3-path "^3.1.0"
    d3-quadtree "^3.0.1"
    d3-random "^3.0.1"
    d3-regression "^1.3.10"
    d3-scale "^4.0.2"
    d3-scale-chromatic "^3.1.0"
    d3-shape "^3.2.0"
    d3-time "^3.1.0"
    d3-timer "^3.0.1"

"@babel/code-frame@^7.26.2":
  version "7.26.2"
  resolved "https://npm.corp.kuaishou.com/@babel/code-frame/-/code-frame-7.26.2.tgz#4b5fab97d33338eff916235055f0ebc21e573a85"
  integrity sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.26.5":
  version "7.26.8"
  resolved "https://npm.corp.kuaishou.com/@babel/compat-data/-/compat-data-7.26.8.tgz#821c1d35641c355284d4a870b8a4a7b0c141e367"
  integrity sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==

"@babel/core@^7.25.2":
  version "7.26.8"
  resolved "https://npm.corp.kuaishou.com/@babel/core/-/core-7.26.8.tgz#7742f11c75acea6b08a8e24c5c0c8c89e89bf53e"
  integrity sha512-l+lkXCHS6tQEc5oUpK28xBOZ6+HwaH7YwoYQbLFiYb4nS2/l1tKnZEtEWkD0GuiYdvArf9qBS0XlQGXzPMsNqQ==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.8"
    "@babel/helper-compilation-targets" "^7.26.5"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.7"
    "@babel/parser" "^7.26.8"
    "@babel/template" "^7.26.8"
    "@babel/traverse" "^7.26.8"
    "@babel/types" "^7.26.8"
    "@types/gensync" "^1.0.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.20.14", "@babel/generator@^7.26.8":
  version "7.26.8"
  resolved "https://npm.corp.kuaishou.com/@babel/generator/-/generator-7.26.8.tgz#f9c5e770309e12e3099ad8271e52f6caa15442ab"
  integrity sha512-ef383X5++iZHWAXX0SXQR6ZyQhw/0KtTkrTz61WXRhFM6dhpHulO/RJz79L8S6ugZHJkOOkUrUdxgdF2YiPFnA==
  dependencies:
    "@babel/parser" "^7.26.8"
    "@babel/types" "^7.26.8"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-compilation-targets@^7.26.5":
  version "7.26.5"
  resolved "https://npm.corp.kuaishou.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.26.5.tgz#75d92bb8d8d51301c0d49e52a65c9a7fe94514d8"
  integrity sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==
  dependencies:
    "@babel/compat-data" "^7.26.5"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "https://npm.corp.kuaishou.com/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz#e7f8d20602ebdbf9ebbea0a0751fb0f2a4141715"
  integrity sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "https://npm.corp.kuaishou.com/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz#8ce54ec9d592695e58d84cd884b7b5c6a2fdeeae"
  integrity sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "https://npm.corp.kuaishou.com/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz#1aabb72ee72ed35789b4bbcad3ca2862ce614e8c"
  integrity sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "https://npm.corp.kuaishou.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz#24b64e2c3ec7cd3b3c547729b8d16871f22cbdc7"
  integrity sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "https://npm.corp.kuaishou.com/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz#86e45bd8a49ab7e03f276577f96179653d41da72"
  integrity sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==

"@babel/helpers@^7.26.7":
  version "7.26.7"
  resolved "https://npm.corp.kuaishou.com/@babel/helpers/-/helpers-7.26.7.tgz#fd1d2a7c431b6e39290277aacfd8367857c576a4"
  integrity sha512-8NHiL98vsi0mbPQmYAGWwfcFaOy4j2HY49fXJCfuDcdE7fMIsH9a7GdaeXpIBsbT7307WU8KCMp5pUVDNL4f9A==
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.7"

"@babel/parser@^7.25.6", "@babel/parser@^7.26.8":
  version "7.26.8"
  resolved "https://npm.corp.kuaishou.com/@babel/parser/-/parser-7.26.8.tgz#deca2b4d99e5e1b1553843b99823f118da6107c2"
  integrity sha512-TZIQ25pkSoaKEYYaHbbxkfL36GNsQ6iFiBbeuzAkLnXayKR1yP1zFe+NxuZWWsUyvt8icPU9CCq0sgWGXR1GEw==
  dependencies:
    "@babel/types" "^7.26.8"

"@babel/runtime@7.23.6":
  version "7.23.6"
  resolved "https://npm.corp.kuaishou.com/@babel/runtime/-/runtime-7.23.6.tgz#c05e610dc228855dc92ef1b53d07389ed8ab521d"
  integrity sha512-zHd0eUrf5GZoOWVCXp6koAKQTfZV07eit6bGPmJgnZdnSAvvZee6zniW2XMF7Cmc4ISOOnPy3QaSiIJGJkVEDQ==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.10.1", "@babel/runtime@^7.10.2", "@babel/runtime@^7.10.4", "@babel/runtime@^7.10.5", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.1", "@babel/runtime@^7.12.5", "@babel/runtime@^7.16.7", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.22.5", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.6", "@babel/runtime@^7.23.9", "@babel/runtime@^7.24.4", "@babel/runtime@^7.24.7", "@babel/runtime@^7.24.8", "@babel/runtime@^7.25.7", "@babel/runtime@^7.26.0", "@babel/runtime@^7.5.5", "@babel/runtime@^7.7.2", "@babel/runtime@^7.9.2":
  version "7.26.7"
  resolved "https://npm.corp.kuaishou.com/@babel/runtime/-/runtime-7.26.7.tgz#f4e7fe527cd710f8dc0618610b61b4b060c3c341"
  integrity sha512-AOPI3D+a8dXnja+iwsUqGRjr1BbZIe771sXdapOtYI531gSqpi92vXivKcq2asu/DFpdl1ceFAKZyRzK2PCVcQ==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.25.6":
  version "7.27.1"
  resolved "https://npm.corp.kuaishou.com/@babel/runtime/-/runtime-7.27.1.tgz#9fce313d12c9a77507f264de74626e87fd0dc541"
  integrity sha512-1x3D2xEk2fRo3PAhwQwu5UubzgiVWSXTBfWpVd2Mx2AzRqJuDJCsgaDVZ7HB5iGzDW1Hl1sWN2mFyKjmR9uAog==

"@babel/template@^7.25.9", "@babel/template@^7.26.8":
  version "7.26.8"
  resolved "https://npm.corp.kuaishou.com/@babel/template/-/template-7.26.8.tgz#db3898f47a17bab2f4c78ec1d0de38527c2ffe19"
  integrity sha512-iNKaX3ZebKIsCvJ+0jd6embf+Aulaa3vNBqZ41kM7iTWjx5qzWKXGHiJUW3+nTpQ18SG11hdF8OAzKrpXkb96Q==
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/parser" "^7.26.8"
    "@babel/types" "^7.26.8"

"@babel/traverse@^7.25.6", "@babel/traverse@^7.25.9", "@babel/traverse@^7.26.8":
  version "7.26.8"
  resolved "https://npm.corp.kuaishou.com/@babel/traverse/-/traverse-7.26.8.tgz#0a8a9c2b7cc9519eed14275f4fd2278ad46e8cc9"
  integrity sha512-nic9tRkjYH0oB2dzr/JoGIm+4Q6SuYeLEiIiZDwBscRMYFJ+tMAz98fuel9ZnbXViA2I0HVSSRRK8DW5fjXStA==
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.8"
    "@babel/parser" "^7.26.8"
    "@babel/template" "^7.26.8"
    "@babel/types" "^7.26.8"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.25.9", "@babel/types@^7.26.7", "@babel/types@^7.26.8":
  version "7.26.8"
  resolved "https://npm.corp.kuaishou.com/@babel/types/-/types-7.26.8.tgz#97dcdc190fab45be7f3dc073e3c11160d677c127"
  integrity sha512-eUuWapzEGWFEpHFxgEaBG8e3n6S8L3MSu0oda755rOfabWPnh0Our1AozNFVUxGFIhbKgd1ksprsoDGMinTOTA==
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@bloomberg/record-tuple-polyfill@0.0.4":
  version "0.0.4"
  resolved "https://npm.corp.kuaishou.com/@bloomberg/record-tuple-polyfill/-/record-tuple-polyfill-0.0.4.tgz#9ef3df44e472ceb9a0a2010d858a526f2021fefa"
  integrity sha512-h0OYmPR3A5Dfbetra/GzxBAzQk8sH7LhRkRUTdagX6nrtlUgJGYCTv4bBK33jsTQw9HDd8PE2x1Ma+iRKEDUsw==

"@ctrl/tinycolor@^3.4.0":
  version "3.6.1"
  resolved "https://npm.corp.kuaishou.com/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz#b6c75a56a1947cc916ea058772d666a2c8932f31"
  integrity sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==

"@emnapi/core@^1.4.0":
  version "1.4.0"
  resolved "https://npm.corp.kuaishou.com/@emnapi/core/-/core-1.4.0.tgz#8844b02d799198158ac1fea21ae2bc81b881da9a"
  integrity sha512-H+N/FqT07NmLmt6OFFtDfwe8PNygprzBikrEMyQfgqSmT0vzE515Pz7R8izwB9q/zsH/MA64AKoul3sA6/CzVg==
  dependencies:
    "@emnapi/wasi-threads" "1.0.1"
    tslib "^2.4.0"

"@emnapi/runtime@^1.4.0":
  version "1.4.0"
  resolved "https://npm.corp.kuaishou.com/@emnapi/runtime/-/runtime-1.4.0.tgz#8f509bf1059a5551c8fe829a1c4e91db35fdfbee"
  integrity sha512-64WYIf4UYcdLnbKn/umDlNjQDSS8AgZrI/R9+x5ilkUVFxXcA1Ebl+gQLc/6mERA4407Xof0R7wEyEuj091CVw==
  dependencies:
    tslib "^2.4.0"

"@emnapi/wasi-threads@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@emnapi/wasi-threads/-/wasi-threads-1.0.1.tgz#d7ae71fd2166b1c916c6cd2d0df2ef565a2e1a5b"
  integrity sha512-iIBu7mwkq4UQGeMEM8bLwNK962nXdhodeScX4slfQnRhEMMzvYivHhutCIk8uojvmASXXPC2WNEjwxFWk72Oqw==
  dependencies:
    tslib "^2.4.0"

"@emotion/hash@^0.8.0":
  version "0.8.0"
  resolved "https://npm.corp.kuaishou.com/@emotion/hash/-/hash-0.8.0.tgz#bbbff68978fefdbe68ccb533bc8cbe1d1afb5413"
  integrity sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow==

"@emotion/is-prop-valid@1.2.2":
  version "1.2.2"
  resolved "https://npm.corp.kuaishou.com/@emotion/is-prop-valid/-/is-prop-valid-1.2.2.tgz#d4175076679c6a26faa92b03bb786f9e52612337"
  integrity sha512-uNsoYd37AFmaCdXlg6EYD1KaPOaRWRByMCYzbKUX4+hhMfrxdVSelShywL4JVaAeM/eHUOSprYBQls+/neX3pw==
  dependencies:
    "@emotion/memoize" "^0.8.1"

"@emotion/memoize@^0.8.1":
  version "0.8.1"
  resolved "https://npm.corp.kuaishou.com/@emotion/memoize/-/memoize-0.8.1.tgz#c1ddb040429c6d21d38cc945fe75c818cfb68e17"
  integrity sha512-W2P2c/VRW1/1tLox0mVUalvnWXxavmv/Oum2aPsRcoDJuob75FC3Y8FbpfLwUegRcxINtGUMPq0tFCvYNTBXNA==

"@emotion/unitless@0.8.1":
  version "0.8.1"
  resolved "https://npm.corp.kuaishou.com/@emotion/unitless/-/unitless-0.8.1.tgz#182b5a4704ef8ad91bde93f7a860a88fd92c79a3"
  integrity sha512-KOEGMu6dmJZtpadb476IsZBclKvILjopjUii3V+7MnXIQCYh8W3NgNcgwo21n9LXZX6EDIKvqfjYxXebDwxKmQ==

"@emotion/unitless@^0.7.5":
  version "0.7.5"
  resolved "https://npm.corp.kuaishou.com/@emotion/unitless/-/unitless-0.7.5.tgz#77211291c1900a700b8a78cfafda3160d76949ed"
  integrity sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg==

"@es/design-components@^1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/design-components/-/@es/design-components-1.0.5.tgz#0c29d87de97df31b9912d074147962ed2a2cb425"
  integrity sha512-QwH8XHUlKHZlRDhw0ZsN4JEYJwvBLTaDnIW9mviA81d+rTgNmlN1g0he4G4r+Sr0QJMoq5K0GfvMjmLBkklLoA==
  dependencies:
    "@es/design-token" "1.0.2"
    "@es/kpro-context" "1.0.0"
    "@es/kpro-es-alert" "1.0.5"
    "@es/kpro-es-button" "1.0.5"
    "@es/kpro-es-checkbox" "1.0.5"
    "@es/kpro-es-modal" "1.0.5"
    "@es/kpro-es-select" "1.0.5"
    "@es/kpro-es-tabs" "1.0.5"
    "@es/kpro-seller-es-table" "1.0.5"
    "@es/kpro-tech-common-event-collector" "1.3.0"

"@es/design-token@1.0.2":
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/@es/design-token/-/@es/design-token-1.0.2.tgz#e47ff0aaf2b54789024e4e338884c85c1c91a12c"
  integrity sha512-kaZUb96d+Tm+2YgbBZ8qAvF9RZ7DuDOeCSKUtRHI+g03ymW+3fcc5ioCzsmsA4+l5V+SUyJ3zTSTmrkSsLMdBA==

"@es/design-token@^1.0.3":
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/@es/design-token/-/@es/design-token-1.2.0.tgz#e21edeef43bea0ea5c7b546d6c0a6c4d30fc57ba"
  integrity sha512-2wYmwYgxK6b3RM28GQBFdLKA5OYbBbaLOH7aqLzokx9BCxQlZeTVTrzicf1W4SqvTvUVSiyF1PGZJUFHBHpChg==

"@es/dilu-react@^1.12.1":
  version "1.15.4"
  resolved "https://npm.corp.kuaishou.com/@es/dilu-react/-/@es/dilu-react-1.15.4.tgz#bfb623431eda626d62b5a2f8e6bd337690d4dabe"
  integrity sha512-ey6/0H7YXUOsPqHd17OODvSbVmmgoVGMWL/WPqpDQldWmpXk7VoZs3D7JHD/v3xbPw+wekIcqA+hJSYAGNvPRg==
  dependencies:
    "@babel/runtime" "^7.12.1"
    "@es/dilu" "1.12.1"
    "@es/qiankun" "2.6.4"

"@es/dilu@1.12.1":
  version "1.12.1"
  resolved "https://npm.corp.kuaishou.com/@es/dilu/-/@es/dilu-1.12.1.tgz#50cba591ef4c00539ecb715350dcfc8536ca18c7"
  integrity sha512-flsZS8J3jyW4/cNm1KGZ85xW8U8QpgTOIeecAFj96huwU6aVsn/SjR6j2aRNGdQLY58cugEsIa37mYVDDbv7QA==
  dependencies:
    kill-port "^2.0.1"

"@es/domain-toolkit@1.2.7", "@es/domain-toolkit@^1.2.3", "@es/domain-toolkit@^1.2.7":
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/@es%2fdomain-toolkit/-/domain-toolkit-1.2.7.tgz#747d2f2e13f467342a70bfea7af31e6e4ba16ef8"
  integrity sha512-qJvObvmwVrHsqLDKNn5Z8Ar0l2ogodIpFqrDd8VhXDfRHjlTr1Gr7DGlwGSbLE+eTQ/DT3E2hkG/2pFNHqniHw==

"@es/driver.js@^2.0.3":
  version "2.0.4"
  resolved "https://npm.corp.kuaishou.com/@es/driver.js/-/@es/driver.js-2.0.4.tgz#3fcfe8061696612273d2639717a20a359e9f4497"
  integrity sha512-6vtpiVR5DAcyRcz0a/E91rlmJ5aVhMAvg2FVIVJrJghQIETsG1rhIqEmVo3BC2bjhwFZXNogWCNbgzjqw3hthg==

"@es/image@0.1.0":
  version "0.1.0"
  resolved "https://npm.corp.kuaishou.com/@es/image/-/@es/image-0.1.0.tgz#c769967c3029a3f2cd9e13d77c0dcb9b21e26f6c"
  integrity sha512-96EMnYpxaFGrCamWfaeMwNxz0RLQ2Jy0TSqsSiOSumAC6k2ifCtkQ/0LwXMYwVI8PW4+gvdw7auXE9vsor3rTg==
  dependencies:
    "@es/domain-toolkit" "^1.2.3"

"@es/image@1.1.2":
  version "1.1.2"
  resolved "https://npm.corp.kuaishou.com/@es/image/-/@es/image-1.1.2.tgz#91becdc3c6b575cb0cdecfbb90f19a6481ed8783"
  integrity sha512-twk+c7qBvl38zdDvy/6HDi6zj7g1fDd3slzs363RJ0/1YI+uyToA/Kj0sGCZQONKaRr/n/qHNvPfobnEtDjjzQ==
  dependencies:
    "@es/kpro-tech-common-event-collector" "1.3.0"
    deepmerge "^4.3.1"
    qs "^6.11.2"

"@es/image@^0.0.2-beta.12":
  version "0.0.2"
  resolved "https://npm.corp.kuaishou.com/@es/image/-/@es/image-0.0.2.tgz#100070eaf13ecc51573e2eb168eaccefc812b028"
  integrity sha512-Swb/qBQZK5KuT5ILMIhLaYsotTRx0PeNh0c0tYe2qKkOpmn7DrT2OO/9bi8mlMB8vEwi2H1ixrF4XNJdb8Uk0w==
  dependencies:
    "@es/domain-toolkit" "^1.2.3"

"@es/kconf-web@^1.0.4":
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/@es%2fkconf-web/-/kconf-web-1.0.4.tgz#0ddb97040cc5929f5b8544daf3ffaff7328a5afc"
  integrity sha512-x01ifnru6Uf9PreVWk5/YhgUdBggvR8t+CHw5UEzGMr3f8I/10uiPz4p3jjXce+XtRh2kLJY9NPJcnpjD7KlQQ==
  dependencies:
    abortcontroller-polyfill "^1.7.3"
    core-js "^3.6.5"
    lodash.isplainobject "^4.0.6"
    promise-polyfill "^8.2.1"
    promise.any "^2.0.3"
    whatwg-fetch "^3.6.2"

"@es/kpro-baomai-personnel-search@0.1.1-alpha.1":
  version "0.1.1-alpha.1"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-baomai-personnel-search/-/@es/kpro-baomai-personnel-search-0.1.1-alpha.1.tgz#da2faf12db9891cdf636197fd71c8c8481d77c96"
  integrity sha512-AUrrz22kj9xydOcxZtex51FDecwBRFbQapjiJSwwjFj5YRFOWpLhXgvdHaK4kyMi4VuHXtCp1+Y0GAbAiijG+w==
  dependencies:
    "@es/kpro-tech-common-event-collector" "^1.3.0"
    lodash-es "^4.17.21"

"@es/kpro-baomai-workbench@1.0.22-beta.13":
  version "1.0.22-beta.13"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-baomai-workbench/-/@es/kpro-baomai-workbench-1.0.22-beta.13.tgz#90c5373899065554c1f3dafa862860c9e2dbb41a"
  integrity sha512-8fWZ/lWuKfD3GGs1A/FZTUEK4w4zJxDCT9AlC+tSgUgC0OfSiwZdpnca4cmtqoQhtZNoHtvOu4R4Sr/s5veCrg==
  dependencies:
    "@es/domain-toolkit" "1.2.7"
    "@es/driver.js" "^2.0.3"
    "@es/kpro-baomai-personnel-search" "0.1.1-alpha.1"
    "@es/kpro-driver" "^3.0.2"
    "@es/kpro-lego-mini-pagination" "^0.1.0"
    "@es/kpro-lego-personnel-select" "1.0.2-alpha.2"
    "@es/kpro-lego-seller-search" "1.0.1-alpha.12"
    "@es/kpro-tech-common-event-collector" "^1.3.0"
    "@es/kpro-workbench" "0.1.1-alpha.2"
    "@es/micro-driver" "1.0.0"
    "@es/pro-components" "1.3.3-alpha.17"
    "@es/wanhua-core" "1.0.10-beta.3"
    "@es/wanhua-tools" "1.0.10-beta.3"
    "@es/wanhua-types" "1.0.10-beta.3"
    ahooks "^3.8.0"
    classnames "^2.5.1"
    dayjs "^1.11.13"
    fuse.js "^6.6.2"
    js-cookie "^3.0.1"
    lodash-es "^4.17.21"
    qs "^6.11.2"
    rc-resize-observer "^1.4.0"
    react-dnd "^15.1.1"
    react-dnd-html5-backend "^15.1.2"
    react-grid-layout "^1.4.4"
    react-hotkeys-hook "^4.5.0"
    react-window "^1.8.10"
    safe-json-parse-and-stringify "^0.2.0"
    uuid "^8.3.2"
    xss "^1.0.15"

"@es/kpro-browser-reminder@1.0.0":
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-browser-reminder/-/@es/kpro-browser-reminder-1.0.0.tgz#43dd21afd6b7f7c1502a25ffdafc245523dd76d4"
  integrity sha512-o5vCq/yhCQ3AkRgA1EW8uSDvffBKF5n+HhQo/Q3npanGwPXGEvD5Q/5nNIMRt1vkxQSpawPw8KWxOud5aFSw+Q==

"@es/kpro-context@1.0.0", "@es/kpro-context@^1.0.0":
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-context/-/@es/kpro-context-1.0.0.tgz#310cb2fd01aafbed7cdc9e0908fa98edaf9cd4b4"
  integrity sha512-0WF4oe8fBPCZX3+WO/g9yKREdSxGklGvuvSImyo8CZ4SBvg5IBgqV4nQOKIfs1AsvGUWypJlOlI/ux/1kzMZhg==

"@es/kpro-driver@^3.0.2":
  version "3.0.2"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-driver/-/@es/kpro-driver-3.0.2.tgz#6a1953f3d2d75c1cdbf4fda87a15e041f403a2a8"
  integrity sha512-nfsAT+HHdu3B+JzyLkt4Y/PA8V4Udj54NgQ05P0JZW+AzhLxwGYsQBAiFBDbwkff3IFEzSD669sCO4bIXOc6Mg==
  dependencies:
    "@m-ui/icons" "^2.0.1"

"@es/kpro-es-alert@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-es-alert/-/@es/kpro-es-alert-1.0.5.tgz#53f56b2676fd4504c9aae0905402a7191351e3dc"
  integrity sha512-X+RPlZScsZ5tCeYpLjs8hWVEo1zhW2JvIo8+Cj8vomhxMkxzA0eCOP+iq7iM7N/iVnm2cUenx941sNNt1ZSeJA==
  dependencies:
    "@es/image" "0.1.0"
    "@es/kpro-context" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"

"@es/kpro-es-button@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-es-button/-/@es/kpro-es-button-1.0.5.tgz#37df0dc7c98bb1295845a3b3cbd94c97df51c0d3"
  integrity sha512-Bk71spBhdjERTNj55ip649EZufbNWvZCqMvwiKV6yWp8ZSTeb3Eu/1X7C/4r6FtogNitqBtjd9hkeCjuSIgp4g==
  dependencies:
    "@es/kpro-context" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"

"@es/kpro-es-checkbox@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-es-checkbox/-/@es/kpro-es-checkbox-1.0.5.tgz#f14d63427f0455cd44b168a43d336960f07067cc"
  integrity sha512-WX72oMJP4Hoxu2+qm/tUPRMC0eWeeuUzW3w93h9sfaw6wWzaceXqEkUGWEjAcDdCDDdQI3nKIIfzkGyS+TCtxQ==
  dependencies:
    "@es/kpro-context" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"
    typescript "4.5.5"

"@es/kpro-es-modal@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-es-modal/-/@es/kpro-es-modal-1.0.5.tgz#2ac0ea109af873ef1bdde85a1da694fab4ff2c88"
  integrity sha512-ZVvWydHsnMmc9YQb8uKQhZNGVCx5VpzASSYimRIX8x8JQSejuAXnwshWHS6LzTWi62kwY/i3QzugIAUhaA6JBQ==
  dependencies:
    "@es/kpro-context" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"
    "@es/kwaishop-tech-component-logger-provider" "1.0.1"

"@es/kpro-es-select@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-es-select/-/@es/kpro-es-select-1.0.5.tgz#8ff93abbc3e1a43d8a4abd15718823e3bb855110"
  integrity sha512-ki9lHN9iiPtSAru6QFVcBQxnDBxodIqu3N5YMnDAYD1KAHHsmxX56ZHJQY/VCJcEahKoGFKbBGDcHNk3du4obQ==
  dependencies:
    "@es/kpro-context" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"

"@es/kpro-es-tabs@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-es-tabs/-/@es/kpro-es-tabs-1.0.5.tgz#54e86431370c35d0ca5a31fd57af11ebf0f0ef30"
  integrity sha512-JQNAtknxUtazRTf5bviD+lRpQVwPsrD/ZQYzBKbCzir9hjBg7JrRK7EDPxZ+Gbnxkfs/X8JFFLUlqU6Ct6mLaA==
  dependencies:
    "@es/kpro-context" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"

"@es/kpro-lego-mini-pagination@^0.1.0":
  version "0.1.0"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-lego-mini-pagination/-/@es/kpro-lego-mini-pagination-0.1.0.tgz#5edb6dc85d604ebb0f2d6aa261ece81b46e9f438"
  integrity sha512-wp1rL6ybB0KwJyfV8r5oSyGVFtPpCrILZAX2+9QpBpdnLKhAxq+O5g7XGAcSKMPqB60c6zCavxnJls8FH//8Fg==
  dependencies:
    "@es/kpro-context" "^1.0.0"
    "@es/kpro-tech-common-event-collector" "^1.2.6"

"@es/kpro-lego-personnel-select@1.0.2-alpha.2":
  version "1.0.2-alpha.2"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-lego-personnel-select/-/@es/kpro-lego-personnel-select-1.0.2-alpha.2.tgz#1f57cb2373991cf5d53ac51346a9ad92df414aa7"
  integrity sha512-ugT/5L6kMOj5q9jLfmHrk9lw2cCrqeNwS+Nwyn2e5C8zrKyCwvkBFSa0XIJV4KhWi+kzO97jH7mdkextI1lOpw==
  dependencies:
    "@es/kpro-context" "^1.0.0"
    "@es/kpro-tech-common-event-collector" "^1.3.0"

"@es/kpro-lego-seller-search@1.0.1-alpha.12":
  version "1.0.1-alpha.12"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-lego-seller-search/-/@es/kpro-lego-seller-search-1.0.1-alpha.12.tgz#17fa3e61f9da16af6a05eca9997897ae57ca7dca"
  integrity sha512-LlfFZymXu/YfFRK4UTjvgHZDbtgiLHqaiVZdA3whrJUTFKiymiwXGD6CrzPOfZurm8oZKhHg5t+iKRqtfssA0A==
  dependencies:
    "@es/kpro-context" "^1.0.0"
    "@es/kpro-tech-common-event-collector" "^1.3.0"
    lodash "^4.17.21"

"@es/kpro-seller-es-table@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-seller-es-table/-/@es/kpro-seller-es-table-1.0.5.tgz#2a7bc7e0bde092394a4005fc11962283ff33ed70"
  integrity sha512-dF/NMrrFVJ2twBUFDBPimqavKlU+2q1p9JJtsZRPqxTAUDMkqD0XTQaLLpUtqdibrT4FA2KSey4HqOC3IGeNhQ==
  dependencies:
    "@es/kpro-context" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"

"@es/kpro-tech-common-event-collector@1.3.0", "@es/kpro-tech-common-event-collector@^1.2.6", "@es/kpro-tech-common-event-collector@^1.3.0":
  version "1.3.0"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-tech-common-event-collector/-/@es/kpro-tech-common-event-collector-1.3.0.tgz#9af3ab778525b44a55447beb9b536d7db84af4a4"
  integrity sha512-A5IOETB7VD2dxwyQa+hXzePfVn78Od/EIN7Y0uImO3h9x4X2NLY1IDgU+sj5V7yhXkJ97yCi3INoxlQRx30s9g==
  dependencies:
    "@es/domain-toolkit" "^1.2.7"
    "@es/kpro-context" "^1.0.0"
    ahooks "^3.7.7"
    react-error-boundary "^3.1.4"
    react-router-dom "^6.4.3"

"@es/kpro-workbench@0.1.1-alpha.2":
  version "0.1.1-alpha.2"
  resolved "https://npm.corp.kuaishou.com/@es/kpro-workbench/-/@es/kpro-workbench-0.1.1-alpha.2.tgz#c02a983d1ec168d5a35d49fa4d05dac9389b0c4a"
  integrity sha512-Bzk8apFTfKCanMfDv/bZteT2tAZgdaR4W000uYUQLBKIpjGAqAaxvwS9arcIIEK5/sAtbTz7uZGr8OlHIiYw3w==
  dependencies:
    "@es/domain-toolkit" "^1.2.7"
    "@es/image" "^0.0.2-beta.12"
    "@es/kpro-browser-reminder" "1.0.0"
    "@es/kpro-tech-common-event-collector" "1.3.0"
    clsx "^1.2.1"
    lodash.debounce "^4.0.8"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    memoize-one "^6.0.0"
    qrcode.react "^3.1.0"

"@es/kwaishop-tech-component-logger-provider@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@es/kwaishop-tech-component-logger-provider/-/@es/kwaishop-tech-component-logger-provider-1.0.1.tgz#18a1ef317ad224ce4ff90865bd3e5385431d9c07"
  integrity sha512-+KGrqwZODx79U6PiwFbpkFXdPUJiqIxad/EdW8r9zGVYraTZF2pv1WY9vyCwmziJqyWdnVCv44jjq/djjoJe0w==

"@es/logger@2.2.6":
  version "2.2.6"
  resolved "https://npm.corp.kuaishou.com/@es/logger/-/@es/logger-2.2.6.tgz#c6d2965dd45e4e034d07c9b1ff5f2430f70c7dce"
  integrity sha512-F+9qjodyQ27sWasM8+9C6gyuthOXN1AyngD6UrFtIykkWp7IA380XwxhJLE47xae3HgoxTV422t1tnNae8wNvw==
  dependencies:
    "@ks-radar/radar" "1.2.12"
    "@ks/weblogger" "3.10.28"
    deepmerge "^4.3.1"
    safe-json-parse-and-stringify "^0.2.0"

"@es/micro-driver@1.0.0":
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/@es/micro-driver/-/@es/micro-driver-1.0.0.tgz#858e1a00bc52e69dddb347c1358861b72cc07d71"
  integrity sha512-ZKDpxQ/+kJC3uIy0k8SUA0VRZYjAhactTgBlUzqcphSUATPHdvTWyarY08x/bD33Ik6/MLJVPXwi8TQ7WkKBSA==

"@es/pro-components-fields@0.1.2-alpha.7":
  version "0.1.2-alpha.7"
  resolved "https://npm.corp.kuaishou.com/@es/pro-components-fields/-/@es/pro-components-fields-0.1.2-alpha.7.tgz#74ac6c4e4da60510af74b82f5d9fd59da3b0aa7b"
  integrity sha512-7E1CY6NID4jjpB8WCP4s5/HdhFqboVBw1uIiAyTrvkMD2YK01A8acTVy/f+mVzMBP/DbwEUmYSVYWetHS18x3w==
  dependencies:
    "@es/design-components" "^1.0.5"
    ahooks "^3.7.10"
    classnames "^2.5.1"
    lodash-es "^4.17.21"
    moment "^2.30.1"
    rc-util "^5.39.1"

"@es/pro-components@1.3.3-alpha.17":
  version "1.3.3-alpha.17"
  resolved "https://npm.corp.kuaishou.com/@es/pro-components/-/@es/pro-components-1.3.3-alpha.17.tgz#5fcaab07eb15e4015d017795fd047893fae84e12"
  integrity sha512-UFtbhq2tB+bgsqEUuPgjJd7ZYuB73x9mkd8Nt8wiX7lHNdWTqvDWbA5b/Cs3p9Ctm+9HEXw3+EXmziqkuH/YRA==
  dependencies:
    "@es/kpro-context" "^1.0.0"
    "@es/kpro-tech-common-event-collector" "^1.3.0"
    ahooks "3.7.2"
    dayjs "^1.11.6"
    decimal.js-light "^2.5.1"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    lodash.tonumber "^4.0.3"
    rc-resize-observer "^1.2.0"
    rc-util "^5.24.4"

"@es/pro-components@1.3.3-alpha.7":
  version "1.3.3-alpha.7"
  resolved "https://npm.corp.kuaishou.com/@es/pro-components/-/@es/pro-components-1.3.3-alpha.7.tgz#16fd4ee2ccbd2ad0ac5982ce0d450e88ac69c3ab"
  integrity sha512-zM8lY5cE6XnBwYhvM+WLrMZg+AnGnrAOGcPWujDMzJ0vqbkN2q/QbAVnLV2gI9o8Nw/TAl2b10rSMYLgeB3Vtg==
  dependencies:
    "@es/kpro-context" "^1.0.0"
    "@es/kpro-tech-common-event-collector" "^1.3.0"
    ahooks "3.7.2"
    dayjs "^1.11.6"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    lodash.tonumber "^4.0.3"
    rc-resize-observer "^1.2.0"
    rc-util "^5.24.4"

"@es/qiankun@2.6.4":
  version "2.6.4"
  resolved "https://npm.corp.kuaishou.com/@es%2fqiankun/-/qiankun-2.6.4.tgz#f314d7e65dbec684af8a828ed04fc4af1bcf7bf2"
  integrity sha512-lFHFGlTbA3PoafpvJikZj8gPCz/dkoI+vJRXHmiY15aEWsG18QM1k1yBQ4hU6PjTweuq7mDmtACNrFr+a554qA==
  dependencies:
    "@babel/runtime" "^7.10.5"
    import-html-entry "^1.9.0"
    lodash "^4.17.11"
    single-spa "^5.9.2"
    tslib "^1.10.0"

"@es/request@2.3.4":
  version "2.3.4"
  resolved "https://npm.corp.kuaishou.com/@es/request/-/@es/request-2.3.4.tgz#ff4ef5a18cf842906d47f2b39edfb2e274bdb22e"
  integrity sha512-0DXjynkKh7+nPW0oaMCTy91coloXGTf+2syZSBf5N8PjoXOAC5A3nc+2cGF3GDqBBIzXhJBGspo7yG+YI2bXMg==
  dependencies:
    "@es/universal-common" "0.0.9"
    "@kds/react-native-request" "^0.5.2"
    "@ks-cqc/fingerprint-generator" "^1.0.3"
    "@ks-cqc/h5-sig4-lite" "1.0.6"
    "@ks-radar/radar-core" "1.2.7"
    "@ks-radar/radar-event-collect" "1.2.7"
    "@ks-radar/radar-util" "1.2.7"
    "@ks/identity-verification" "1.0.0"
    lodash.isarray "^4.0.0"
    lodash.isboolean "^3.0.3"
    lodash.isnil "^4.0.0"
    lodash.isnumber "^3.0.3"
    lodash.isobject "^3.0.2"
    lodash.isstring "^4.0.1"
    uglify-js "^3.17.4"

"@es/traceid@^1.1.0":
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/@es%2ftraceid/-/traceid-1.1.0.tgz#6abb134306a017320ba204506e47e1f969f7effd"
  integrity sha512-uADkBm78k/4W8+c77WS/6FXD+TvG7/563G2eh5wIcJVx4Q9Pt37CCuYT69n9iAHjrBIc8YbghR0bq54YmoTv8A==

"@es/universal-common@0.0.9":
  version "0.0.9"
  resolved "https://npm.corp.kuaishou.com/@es%2funiversal-common/-/universal-common-0.0.9.tgz#1cb22f144825de61a00b1deb11e517c70cc3b16c"
  integrity sha512-sdHiIum4g7TtxvSUEYn5fHr/71i+VavpyNPO6o/UqWTySf7wY8jUFG2PxT2WpKZdU1o7Lr7rOT+b1i6J9qUD9Q==

"@es/wanhua-core@1.0.10-beta.3":
  version "1.0.10-beta.3"
  resolved "https://npm.corp.kuaishou.com/@es/wanhua-core/-/@es/wanhua-core-1.0.10-beta.3.tgz#88f2daf442fb362537cfe5336ee913d9fcb586b6"
  integrity sha512-IW1RRjUeqa3f6pUxrA2s76K0aj9gQs3RR8GANGrmW1ZyCTEts1cHcrMLrMgaPziZsSL3m0f5F9oehhaPVXe9nQ==
  dependencies:
    "@es/dilu-react" "^1.12.1"
    "@es/wanhua-nocode-renderer" "^1.0.10-beta.3"
    "@es/wanhua-tools" "^1.0.10-beta.3"
    "@es/wanhua-types" "^1.0.10-beta.3"
    ahooks "^3.7.10"
    qs "^6.11.2"
    safe-json-parse-and-stringify "^0.2.0"

"@es/wanhua-nocode-renderer@^1.0.10-beta.3":
  version "1.0.10-beta.3"
  resolved "https://npm.corp.kuaishou.com/@es/wanhua-nocode-renderer/-/@es/wanhua-nocode-renderer-1.0.10-beta.3.tgz#8e7507db32349d5a19b35a56e49cd35676f34353"
  integrity sha512-g3Cp7upO+UOIISRHuKL619lu/S5+4VaPwqKB4/4/KEWbgp0XfmGiPldnDPuVEGHpKdD+2rc5ktc+Jk6AYoAihQ==
  dependencies:
    "@es/pro-components" "1.3.3-alpha.7"
    "@es/pro-components-fields" "0.1.2-alpha.7"
    "@es/wanhua-types" "^1.0.10-beta.3"
    "@kael/designer-service" "1.0.56"
    "@kael/material-utils" "1.0.56"
    "@kael/renderer-react" "1.0.56"
    "@kael/renderer-shared" "1.0.56"
    "@kael/schema" "1.0.56"
    safe-json-parse-and-stringify "^0.2.0"

"@es/wanhua-tools@1.0.10-beta.3", "@es/wanhua-tools@^1.0.10-beta.3":
  version "1.0.10-beta.3"
  resolved "https://npm.corp.kuaishou.com/@es/wanhua-tools/-/@es/wanhua-tools-1.0.10-beta.3.tgz#3ab6853dfbf803500ad400655b30f37c4654c571"
  integrity sha512-pPapXCuR/FPuFzmUAeUWcvAlyErQkK6sFK7Ham+rM8iN/kdKYkTq/ezEP3cy/N0oGprbjCDiAbNHAZMzBeempw==
  dependencies:
    "@es/wanhua-types" "^1.0.10-beta.3"
    vm-browserify "^1.1.2"

"@es/wanhua-types@1.0.10-beta.3", "@es/wanhua-types@^1.0.10-beta.3":
  version "1.0.10-beta.3"
  resolved "https://npm.corp.kuaishou.com/@es/wanhua-types/-/@es/wanhua-types-1.0.10-beta.3.tgz#ff2a11466ec28fe359bc32a504205fbf94c9bc58"
  integrity sha512-7P1PL/XHSfVgamAJXeDVHTXjAzbqSBtWeJW5lyuOJUWEhqo/IcJlZZ+3fd+Qy2D3JET8QJ6Kq57WGiyOJi7b4Q==

"@esbuild/android-arm64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/android-arm64/-/android-arm64-0.17.19.tgz#bafb75234a5d3d1b690e7c2956a599345e84a2fd"
  integrity sha512-KBMWvEZooR7+kzY0BtbTQn0OAYY7CsiydT63pVEaPtVYF0hXbUaOyZog37DKxK7NF3XacBJOpYT4adIJh+avxA==

"@esbuild/android-arm@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/android-arm/-/android-arm-0.17.19.tgz#5898f7832c2298bc7d0ab53701c57beb74d78b4d"
  integrity sha512-rIKddzqhmav7MSmoFCmDIb6e2W57geRsM94gV2l38fzhXMwq7hZoClug9USI2pFRGL06f4IOPHHpFNOkWieR8A==

"@esbuild/android-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/android-x64/-/android-x64-0.17.19.tgz#658368ef92067866d95fb268719f98f363d13ae1"
  integrity sha512-uUTTc4xGNDT7YSArp/zbtmbhO0uEEK9/ETW29Wk1thYUJBz3IVnvgEiEwEa9IeLyvnpKrWK64Utw2bgUmDveww==

"@esbuild/darwin-arm64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/darwin-arm64/-/darwin-arm64-0.17.19.tgz#584c34c5991b95d4d48d333300b1a4e2ff7be276"
  integrity sha512-80wEoCfF/hFKM6WE1FyBHc9SfUblloAWx6FJkFWTWiCoht9Mc0ARGEM47e67W9rI09YoUxJL68WHfDRYEAvOhg==

"@esbuild/darwin-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/darwin-x64/-/darwin-x64-0.17.19.tgz#7751d236dfe6ce136cce343dce69f52d76b7f6cb"
  integrity sha512-IJM4JJsLhRYr9xdtLytPLSH9k/oxR3boaUIYiHkAawtwNOXKE8KoU8tMvryogdcT8AU+Bflmh81Xn6Q0vTZbQw==

"@esbuild/freebsd-arm64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.19.tgz#cacd171665dd1d500f45c167d50c6b7e539d5fd2"
  integrity sha512-pBwbc7DufluUeGdjSU5Si+P3SoMF5DQ/F/UmTSb8HXO80ZEAJmrykPyzo1IfNbAoaqw48YRpv8shwd1NoI0jcQ==

"@esbuild/freebsd-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/freebsd-x64/-/freebsd-x64-0.17.19.tgz#0769456eee2a08b8d925d7c00b79e861cb3162e4"
  integrity sha512-4lu+n8Wk0XlajEhbEffdy2xy53dpR06SlzvhGByyg36qJw6Kpfk7cp45DR/62aPH9mtJRmIyrXAS5UWBrJT6TQ==

"@esbuild/linux-arm64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-arm64/-/linux-arm64-0.17.19.tgz#38e162ecb723862c6be1c27d6389f48960b68edb"
  integrity sha512-ct1Tg3WGwd3P+oZYqic+YZF4snNl2bsnMKRkb3ozHmnM0dGWuxcPTTntAF6bOP0Sp4x0PjSF+4uHQ1xvxfRKqg==

"@esbuild/linux-arm@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-arm/-/linux-arm-0.17.19.tgz#1a2cd399c50040184a805174a6d89097d9d1559a"
  integrity sha512-cdmT3KxjlOQ/gZ2cjfrQOtmhG4HJs6hhvm3mWSRDPtZ/lP5oe8FWceS10JaSJC13GBd4eH/haHnqf7hhGNLerA==

"@esbuild/linux-ia32@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-ia32/-/linux-ia32-0.17.19.tgz#e28c25266b036ce1cabca3c30155222841dc035a"
  integrity sha512-w4IRhSy1VbsNxHRQpeGCHEmibqdTUx61Vc38APcsRbuVgK0OPEnQ0YD39Brymn96mOx48Y2laBQGqgZ0j9w6SQ==

"@esbuild/linux-loong64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-loong64/-/linux-loong64-0.17.19.tgz#0f887b8bb3f90658d1a0117283e55dbd4c9dcf72"
  integrity sha512-2iAngUbBPMq439a+z//gE+9WBldoMp1s5GWsUSgqHLzLJ9WoZLZhpwWuym0u0u/4XmZ3gpHmzV84PonE+9IIdQ==

"@esbuild/linux-mips64el@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-mips64el/-/linux-mips64el-0.17.19.tgz#f5d2a0b8047ea9a5d9f592a178ea054053a70289"
  integrity sha512-LKJltc4LVdMKHsrFe4MGNPp0hqDFA1Wpt3jE1gEyM3nKUvOiO//9PheZZHfYRfYl6AwdTH4aTcXSqBerX0ml4A==

"@esbuild/linux-ppc64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-ppc64/-/linux-ppc64-0.17.19.tgz#876590e3acbd9fa7f57a2c7d86f83717dbbac8c7"
  integrity sha512-/c/DGybs95WXNS8y3Ti/ytqETiW7EU44MEKuCAcpPto3YjQbyK3IQVKfF6nbghD7EcLUGl0NbiL5Rt5DMhn5tg==

"@esbuild/linux-riscv64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-riscv64/-/linux-riscv64-0.17.19.tgz#7f49373df463cd9f41dc34f9b2262d771688bf09"
  integrity sha512-FC3nUAWhvFoutlhAkgHf8f5HwFWUL6bYdvLc/TTuxKlvLi3+pPzdZiFKSWz/PF30TB1K19SuCxDTI5KcqASJqA==

"@esbuild/linux-s390x@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-s390x/-/linux-s390x-0.17.19.tgz#e2afd1afcaf63afe2c7d9ceacd28ec57c77f8829"
  integrity sha512-IbFsFbxMWLuKEbH+7sTkKzL6NJmG2vRyy6K7JJo55w+8xDk7RElYn6xvXtDW8HCfoKBFK69f3pgBJSUSQPr+4Q==

"@esbuild/linux-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/linux-x64/-/linux-x64-0.17.19.tgz#8a0e9738b1635f0c53389e515ae83826dec22aa4"
  integrity sha512-68ngA9lg2H6zkZcyp22tsVt38mlhWde8l3eJLWkyLrp4HwMUr3c1s/M2t7+kHIhvMjglIBrFpncX1SzMckomGw==

"@esbuild/netbsd-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/netbsd-x64/-/netbsd-x64-0.17.19.tgz#c29fb2453c6b7ddef9a35e2c18b37bda1ae5c462"
  integrity sha512-CwFq42rXCR8TYIjIfpXCbRX0rp1jo6cPIUPSaWwzbVI4aOfX96OXY8M6KNmtPcg7QjYeDmN+DD0Wp3LaBOLf4Q==

"@esbuild/openbsd-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/openbsd-x64/-/openbsd-x64-0.17.19.tgz#95e75a391403cb10297280d524d66ce04c920691"
  integrity sha512-cnq5brJYrSZ2CF6c35eCmviIN3k3RczmHz8eYaVlNasVqsNY+JKohZU5MKmaOI+KkllCdzOKKdPs762VCPC20g==

"@esbuild/sunos-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/sunos-x64/-/sunos-x64-0.17.19.tgz#722eaf057b83c2575937d3ffe5aeb16540da7273"
  integrity sha512-vCRT7yP3zX+bKWFeP/zdS6SqdWB8OIpaRq/mbXQxTGHnIxspRtigpkUcDMlSCOejlHowLqII7K2JKevwyRP2rg==

"@esbuild/win32-arm64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/win32-arm64/-/win32-arm64-0.17.19.tgz#9aa9dc074399288bdcdd283443e9aeb6b9552b6f"
  integrity sha512-yYx+8jwowUstVdorcMdNlzklLYhPxjniHWFKgRqH7IFlUEa0Umu3KuYplf1HUZZ422e3NU9F4LGb+4O0Kdcaag==

"@esbuild/win32-ia32@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/win32-ia32/-/win32-ia32-0.17.19.tgz#95ad43c62ad62485e210f6299c7b2571e48d2b03"
  integrity sha512-eggDKanJszUtCdlVs0RB+h35wNlb5v4TWEkq4vZcmVt5u/HiDZrTXe2bWFQUez3RgNHwx/x4sk5++4NSSicKkw==

"@esbuild/win32-x64@0.17.19":
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/@esbuild/win32-x64/-/win32-x64-0.17.19.tgz#8cfaf2ff603e9aabb910e9c0558c26cf32744061"
  integrity sha512-lAhycmKnVOuRYNtRtatQR1LPQf2oYCkRGkSFnseDAKPl8lu5SOsK/e1sXe5a0Pc5kHIHe6P2I/ilntNv2xf3cA==

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://npm.corp.kuaishou.com/@jest/schemas/-/schemas-29.6.3.tgz#430b5ce8a4e0044a7e3819663305a7b3091c8e03"
  integrity sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://npm.corp.kuaishou.com/@jest/types/-/types-29.6.3.tgz#1131f8cf634e7e84c5e77bab12f052af585fba59"
  integrity sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "https://npm.corp.kuaishou.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz#4f0e06362e01362f823d348f1872b08f666d8142"
  integrity sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://npm.corp.kuaishou.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/@jridgewell/set-array/-/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15":
  version "1.5.0"
  resolved "https://npm.corp.kuaishou.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://npm.corp.kuaishou.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@kael/designer-core@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/designer-core/-/@kael/designer-core-1.0.56.tgz#1cd77385cdb5014b4efa9f85ab1ec89fade81667"
  integrity sha512-aqUTXVA2hQyCsXxdIZfvMZI9OyTLJ8X4ldsauerT7blbn+wn2I6SwizR0W69TeVBcT51iFD1tQgXhms3sNqwxw==
  dependencies:
    "@kael/di" "1.0.56"
    "@kael/performance" "1.0.56"
    "@kael/shared" "1.0.56"
    "@lumino/algorithm" "^1.9.2"
    "@lumino/commands" "^1.21.1"
    "@lumino/coreutils" "^1.12.1"
    "@lumino/disposable" "^1.10.4"
    "@lumino/domutils" "^1.8.2"
    "@lumino/dragdrop" "^1.14.5"
    "@lumino/keyboard" "^1.8.2"
    "@lumino/messaging" "^1.10.3"
    "@lumino/properties" "^1.8.2"
    "@lumino/signaling" "^1.11.1"
    "@lumino/virtualdom" "^1.14.3"
    "@lumino/widgets" "^1.37.2"

"@kael/designer-service@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/designer-service/-/@kael/designer-service-1.0.56.tgz#99676cd22d267bad75f9e1c2f6d489f6597f47c5"
  integrity sha512-10C1jSKzBITIyZpNknNme1GJZimLVl3pNs8uhe/ET9VYLcATyndgeYDiMU6qzf1Kxjg/zlxv7qJW6PmM9j+LiQ==
  dependencies:
    "@ad/ad-front-trace" "1.0.10-alpha.1"
    "@kael/designer-core" "1.0.56"
    "@kael/di" "1.0.56"
    "@kael/dragon" "1.0.56"
    "@kael/flow-schema" "1.0.56"
    "@kael/material-schema" "1.0.56"
    "@kael/material-utils" "1.0.56"
    "@kael/schema" "1.0.56"
    "@kael/schema-utils" "1.0.56"
    "@kael/shared" "1.0.56"
    "@remote-ui/rpc" "^1.4.4"
    chalk "^5.3.0"
    eventemitter3 "^5.0.1"
    lodash-es "^4.17.21"
    mobx "^6.10.2"

"@kael/di@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/di/-/@kael/di-1.0.56.tgz#c0e1a42452df9c0ec78e2ecb789986178283250b"
  integrity sha512-LD5YWYzmnMKFE67NUp3wRnj9BFe49kj+uBM09l54uB7HwZ8X0qPvFjYI06fEHJ6fZBW1bADKWTovJKJ9h7sU2g==

"@kael/dragon@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/dragon/-/@kael/dragon-1.0.56.tgz#8d8cd59e52c97f133810256f18d7fe24ead0512b"
  integrity sha512-WMlNCaoZFkEIGN4d/MjDovjldslzuL5EiGQWzzhDO7cO3hnK8i26j1a8WZSKrh6o5oON2z2gJTYYbeyi/H8jkw==
  dependencies:
    "@types/lodash-es" "^4.17.9"
    lodash-es "^4.17.21"

"@kael/flow-schema@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/flow-schema/-/@kael/flow-schema-1.0.56.tgz#abee4f83c9550fc5037552065eb26f7db0932044"
  integrity sha512-H38bKrDOJcokn3kRNrrbQEkhMTkDiBSSlLrVzYWzB0HYJkcENb9yUV7YW0nUQTUafXq1F7ouJyT15es8FvRFfA==
  dependencies:
    "@kael/schema" "1.0.56"

"@kael/material-schema@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/material-schema/-/@kael/material-schema-1.0.56.tgz#b209f07e8b13250f552859bc78d6faec66661b0d"
  integrity sha512-p9mC2eZ1CdPfX1O/ksx3v83Q54JzF2Fr5h64BL2fQRKz5j2waZWFbMKb/SsTOfQdz0mTxJN5J8VDZBUn47+Ntg==
  dependencies:
    "@kael/schema" "1.0.56"

"@kael/material-utils@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/material-utils/-/@kael/material-utils-1.0.56.tgz#50a19f8b333a203c84e21a0be2ff918409c69090"
  integrity sha512-XGpUEfDGziCGv1G9IKZgzA+EovwyG7rzb6ohpm6A0ZpU25L0iiIwviFOeTCqrn/TZt1R4MZahtphwxroILi+aw==
  dependencies:
    "@kael/material-schema" "1.0.56"
    "@kael/schema" "1.0.56"
    "@kael/schema-utils" "1.0.56"
    "@kael/shared" "1.0.56"
    axios "^0.27.2"
    lodash-es "^4.17.21"
    mobx "^6.10.2"
    semver "^7.5.4"

"@kael/performance@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/performance/-/@kael/performance-1.0.56.tgz#266c9163a778e10bc35df3dbe984031d6d8d661d"
  integrity sha512-MqfNThUFjqTiR+Hqopy2hNyTIeELaVtx+XuVAgHKakvltklHHVAHK6g/LhL08h5imSlC1gpyEKMrCCBW90ONLA==
  dependencies:
    lodash "^4.17.21"

"@kael/renderer-react@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/renderer-react/-/@kael/renderer-react-1.0.56.tgz#45bcbf8fc1d438e7e37a6cf07cba242741e63e25"
  integrity sha512-VYlZ8fHv0j2PIo9e+3tWGsTziGujTE2kBeYwdNWaOdSpHk6o4x48BGRDJ28K+jnn9I6cpIy5EqnSSlj8XSUCVA==
  dependencies:
    "@kael/designer-service" "1.0.56"
    "@kael/di" "1.0.56"
    "@kael/renderer-shared" "1.0.56"
    "@kael/runtime" "1.0.56"
    "@kael/runtime-plugin-component-links" "1.0.56"
    "@kael/runtime-plugin-expr-ext-array" "1.0.56"
    "@kael/runtime-plugin-expr-ext-js-fn" "1.0.56"
    "@kael/runtime-plugin-expr-ext-object" "1.0.56"
    "@kael/runtime-plugin-expr-ext-render-fn-react" "1.0.56"
    "@kael/runtime-plugin-id" "1.0.56"
    "@kael/schema" "1.0.56"
    "@kael/shared" "1.0.56"
    lodash-es "^4.17.21"
    mobx "^6.10.2"
    mobx-react-lite "^4.0.5"
    react-use "^17.4.0"

"@kael/renderer-shared@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/renderer-shared/-/@kael/renderer-shared-1.0.56.tgz#7e849de58df94dc5c39c1bd6837a48a166bcd761"
  integrity sha512-1PJbY1VcmqmJvP79SVKOL1qCRZVR6K1KL11y9pLmYe6ok6VXofe2wZOSgvEIcFVP4YDhM2s9HgzmwPdMlDSyxg==
  dependencies:
    "@kael/designer-service" "1.0.56"
    "@kael/runtime" "1.0.56"
    "@kael/runtime-plugin-id" "1.0.56"
    "@kael/schema" "1.0.56"
    "@kael/schema-utils" "1.0.56"
    "@kael/shared" "1.0.56"
    lodash-es "^4.17.21"
    mobx "^6.10.1"
    mock-json-schema "^1.1.1"

"@kael/runtime-plugin-component-links@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-component-links/-/@kael/runtime-plugin-component-links-1.0.56.tgz#28b74bcee0d134abf4f980baf7c097a5c859e17c"
  integrity sha512-Can27+7vlFsEuSpjJYJ9TUO8V4VS4Y1w4CjQGkfTxnYemMMRlfksw8bvfwITc8vLLZ4IPFNveOOCtDCZg+1Giw==
  dependencies:
    "@kael/runtime" "1.0.56"
    "@kael/schema" "1.0.56"
    eventemitter3 "^5.0.1"
    mobx "^6.10.2"

"@kael/runtime-plugin-expr-ext-array@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-expr-ext-array/-/@kael/runtime-plugin-expr-ext-array-1.0.56.tgz#134df5fbe56c317044f0d72acda36581e6e36c22"
  integrity sha512-53nwGh9sm3rU4JP/tvTP4ovkt8D10MpKPfEUxw3jQj+A4IrNOs3vF6SSf+uYz4v5I/9sS/wQNJNiXycom60raA==
  dependencies:
    "@kael/runtime" "1.0.56"
    "@kael/schema" "1.0.56"

"@kael/runtime-plugin-expr-ext-js-fn@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-expr-ext-js-fn/-/@kael/runtime-plugin-expr-ext-js-fn-1.0.56.tgz#5fb6c2831c69fd7230c6a5887c325e9cbbf5d15b"
  integrity sha512-84kW9y4U5rEqyFj/ZZzTMhuYZSRQHcSdJzH2g7yisyQCAAPIGzGxL7AjY9lp9GTA4V/h+EbEfxpFaNfzXoEInA==
  dependencies:
    "@kael/runtime" "1.0.56"
    "@kael/schema" "1.0.56"

"@kael/runtime-plugin-expr-ext-object@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-expr-ext-object/-/@kael/runtime-plugin-expr-ext-object-1.0.56.tgz#228285f1172b16ad8355cc899725877b98db435e"
  integrity sha512-zRRVAylnu8uVdTqwI1Q7NDVUFTPRv0KzZrqkf9RoRm7OXIa7eZ0NbjW3lWQBxrXkuziOEBjYApypp3EtIgTE3A==
  dependencies:
    "@kael/runtime" "1.0.56"
    "@kael/schema" "1.0.56"

"@kael/runtime-plugin-expr-ext-render-fn-react@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-expr-ext-render-fn-react/-/@kael/runtime-plugin-expr-ext-render-fn-react-1.0.56.tgz#47fd778096f04743917392900896c0b02afd973b"
  integrity sha512-OhFZt6MYb4xWjaE4lefHiThliGwfDcdemyOxt1D96pigzPwa3qb9YowSf8xvTwZXYnrNVzGdoi7/+92Ran/Jlw==
  dependencies:
    "@kael/runtime" "1.0.56"
    "@kael/runtime-plugin-loop" "1.0.56"
    "@kael/runtime-plugin-react-renderer" "1.0.56"
    "@kael/schema" "1.0.56"
    "@kael/schema-utils" "1.0.56"

"@kael/runtime-plugin-id@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-id/-/@kael/runtime-plugin-id-1.0.56.tgz#cd1311130356be5808f20db8f4aa64aadf6159b7"
  integrity sha512-pNOeRUpVOZTHRiWPCVR1yNxkC9HV3zx12dvYNoc3vkb590plYVCoG6GNpVytDzG/FQlBSGtbEkn7OJ0nIWDKCA==
  dependencies:
    "@kael/runtime" "1.0.56"
    "@kael/schema" "1.0.56"

"@kael/runtime-plugin-loop@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-loop/-/@kael/runtime-plugin-loop-1.0.56.tgz#8495e87729c27a8c805251a814b5441541998770"
  integrity sha512-kGpu1DYG+naiySYxYODBmBAMhJZWhU7ktj1hKEPoyd1iWz4TLm0tMKzsmAuADMGm+nCcDE7EZiUpmYyyFtBU1w==
  dependencies:
    "@kael/runtime" "1.0.56"
    "@kael/runtime-plugin-react-renderer" "1.0.56"
    "@kael/schema" "1.0.56"
    "@kael/schema-utils" "1.0.56"
    "@types/lodash" "^4.14.195"
    lodash "^4.17.21"

"@kael/runtime-plugin-react-renderer@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime-plugin-react-renderer/-/@kael/runtime-plugin-react-renderer-1.0.56.tgz#91719ebaaa9999c3505d6e41e8d2f812eaa39a52"
  integrity sha512-IKSJfprsNQTlkKFTNxRHZwLrkS9o/q6Nubf3JElKPDAveWZ0uQm12kUr+9PMcgBr51PLVySu73galmKnAn1O6Q==
  dependencies:
    "@kael/runtime" "1.0.56"

"@kael/runtime@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/runtime/-/@kael/runtime-1.0.56.tgz#dbd615580f54e1ca7c2e4aaf74b5c9293a5571ac"
  integrity sha512-IazQ1wPP3bxpnptPM4W8riQj+uWurkT8aoTW/ykQTuIdIluEaLDCfIkqyY3bizrBbfhBbxTMYmYNYag63AwXWg==
  dependencies:
    "@kael/schema" "1.0.56"
    "@kael/schema-utils" "1.0.56"

"@kael/schema-utils@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/schema-utils/-/@kael/schema-utils-1.0.56.tgz#45bb3236f8df9688da0dec9bfbdd2c1984e72cb5"
  integrity sha512-bTBUIPEMJS+BgFca/h0OYn1318gaJZ4znFQpJ3174PmMnQeDuhFYwh3aydXe42rcQJVLRe0ZGBKzkfQmbq5VvQ==
  dependencies:
    "@kael/schema" "1.0.56"
    "@types/lodash" "^4.14.195"
    lodash "^4.17.21"

"@kael/schema@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/schema/-/@kael/schema-1.0.56.tgz#44d52753ec59ab988af6ddf81b97f88b32d1d770"
  integrity sha512-MYhqMapfOreK5Er0PN+8o4sRVXIpjarhWaLT8DOkHtHLeMbzcEBp6uxyXKYRIHf/+bgj1TwybBeB9Bw7L1BYEA==

"@kael/shared@1.0.56":
  version "1.0.56"
  resolved "https://npm.corp.kuaishou.com/@kael/shared/-/@kael/shared-1.0.56.tgz#5bcd331a75beec5507aa5a257b9d71cbd5292a8f"
  integrity sha512-Wrs3w8hpUAvbNXmC8F4cZ5JugdgUTBHrvkf3fb8g6jQ5iiKhkkoe/TRt9iRhXBNuOV732eO6wyO2FfE5CPJwEQ==
  dependencies:
    "@babel/core" "^7.25.2"
    "@babel/generator" "^7.20.14"
    "@babel/parser" "^7.25.6"
    "@babel/traverse" "^7.25.6"
    "@kael/dragon" "1.0.56"
    "@kael/flow-schema" "1.0.56"
    "@kael/runtime" "1.0.56"
    "@kael/schema" "1.0.56"
    "@kael/schema-utils" "1.0.56"
    "@remote-ui/rpc" "^1.4.4"
    "@types/lodash-es" "^4.17.9"
    case-anything "2.1.10"
    lodash-es "^4.17.21"
    mock-json-schema "^1.1.1"
    nanoid "^3.3.6"

"@kds/react-native-request@^0.5.2":
  version "0.5.2"
  resolved "https://npm.corp.kuaishou.com/@kds/react-native-request/-/@kds/react-native-request-0.5.2.tgz#1b06def5999ae5bb5172ecf6c924e451b58fbc68"
  integrity sha512-0TgOaQTBN3uGURz/Sx9nWIWekYPXLTVEZ4ywDdm7EBNSIHbOYXlSyqnmXI4avPMYVoVQR5uhKsgr2sF4gb3xag==

"@kmi/ast@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/ast/-/@kmi/ast-2.0.29.tgz#3f951b04ee989515d04589c77e0ec7746e34002f"
  integrity sha512-VGNJuJqkH4pGqi/3yIYVimFYyGaX71ygWo2OUo1bvViOsJe0209p3PHN3ZJbTEG+eR6Q2p2GIkdA+2epaqS3Ag==
  dependencies:
    "@kmi/bundler-compiled" "2.0.29"

"@kmi/babel-preset-react@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/babel-preset-react/-/@kmi/babel-preset-react-2.0.29.tgz#df12365c8aee7825b99c8b27ab04f812464666b9"
  integrity sha512-lnRYhKbeAXVFnae3PDoaHQqALipg/M0CqQaRh9PQ8jU8x07lrVZGFGeO3zpDu2RpgSq9qAC+auKn0l5tEb456g==
  dependencies:
    "@babel/runtime" "7.23.6"
    "@bloomberg/record-tuple-polyfill" "0.0.4"
    "@kmi/bundler-compiled" "2.0.29"
    "@kmi/shared" "2.0.29"
    core-js "3.28.0"

"@kmi/bundler-compiled@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/bundler-compiled/-/@kmi/bundler-compiled-2.0.29.tgz#809dcb72591f9707eb80f9fe84165f8558143775"
  integrity sha512-ymU6KJrQbAKosAddxFi4kCM/AmO6VnH8/C/pK748yh0whBDW+ZCSHJwb2jJgaNWndxgNn+9QvT7BFTi2Hg4MqQ==
  dependencies:
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/shared" "2.0.29"
    caniuse-lite "1.0.30001690"
    jest-worker "29.4.3"
    loader-utils "2.0.4"
    postcss "8.4.21"
    regenerate "1.4.2"
    regenerate-unicode-properties "10.2.0"

"@kmi/bundler-rspack@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/bundler-rspack/-/@kmi/bundler-rspack-2.0.29.tgz#529b5fc37ab17e477b5639fb6615339f52f21a2c"
  integrity sha512-6qP1WU5Y0HKETb04s7tWNXCKhU8F3mgVmvEvrsh/F9mPOC89xBrKeVjlhk9tsYB1x8lxlkgYOLhnnnDGDaJzfQ==
  dependencies:
    "@kmi/bundler-compiled" "2.0.29"
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/bundler-shared-config" "2.0.29"
    "@kmi/shared" "2.0.29"
    "@ksuni/swc-plugin-auto-css-modules" "0.1.0"
    "@swc/helpers" "0.5.15"
    postcss "8.4.21"
    react-error-overlay "6.0.9"

"@kmi/bundler-shared-config@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/bundler-shared-config/-/@kmi/bundler-shared-config-2.0.29.tgz#de0a01f2d707681f8c291449d3cb78056168b1ce"
  integrity sha512-7HZ+CeSy0o32fP+AuTMmsjmV74JjzSlJBDFpQa6IxlD4PuA4o4JMpDr0Uc4bYQ4FkbrScEUXnh39D5xp7N/O3Q==
  dependencies:
    "@kmi/bundler-compiled" "2.0.29"
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/shared" "2.0.29"
    node-stdlib-browser "1.3.0"

"@kmi/bundler-shared@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/bundler-shared/-/@kmi/bundler-shared-2.0.29.tgz#b9aa50649f81c8b1ec1bd4b3b9ce707095c7d15f"
  integrity sha512-LpmCTi+3E1Ldo0c4D69gF9LmJg4yEpw7wLUPQ7r/UMz6lJW90YaHjHI/WroIBiu2O12v/AR0QU7UMWbu0T6kCg==
  dependencies:
    "@kmi/shared" "2.0.29"
    "@rspack/core" "1.2.8"
    es5-imcompatible-versions "^0.1.78"
    esbuild "0.17.19"
    oxc-resolver "1.12.0"
    piscina "4.7.0"
    rs-module-lexer "2.5.0"

"@kmi/bundler-webpack@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/bundler-webpack/-/@kmi/bundler-webpack-2.0.29.tgz#374bb1c5e8cdcbb0b73be2c2826ce53c3df2b460"
  integrity sha512-/id+u+y3tszxhvOstUgoFvRG1PAT3ssJ0U+x678Sef6F+oHU9gM9TiUGg8v+UPdYDwgmx8rekkK7vXOl1czauA==
  dependencies:
    "@kmi/bundler-compiled" "2.0.29"
    "@kmi/bundler-rspack" "2.0.29"
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/bundler-shared-config" "2.0.29"
    "@kmi/shared" "2.0.29"
    core-js-pure "3.23.3"
    loader-utils "2.0.4"
    postcss "8.4.21"
    react-error-overlay "6.0.9"
    react-refresh "0.14.0"

"@kmi/core@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/core/-/@kmi/core-2.0.29.tgz#3ffb162880438c065dfd5b362493196799b8ffa2"
  integrity sha512-rApQDud8wR8ECclrzV49thVmEjwrNn2K+CdfMAofGbe/6B+BU5qHHzUiAtzJbOOcXZa2jzgRzDA83ASTjfMb+A==
  dependencies:
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/remote-plugin-manager" "2.0.29"
    "@kmi/shared" "2.0.29"

"@kmi/es@^2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/es/-/@kmi/es-2.0.29.tgz#fa7fdebd49a0b4c1686b599d1a8b26b2d2635f36"
  integrity sha512-ZU/UJPILK9nA2vgWHTPtNdFjBuxvZ7VlecEsCe+VNvS9y/e96c3R54Qqj+V4hD3Fp9jeHJ4UzOsACOTAOur/cw==
  dependencies:
    "@kmi/kmijs" "2.0.29"
    "@kmi/plugin-bundler-react" "2.0.29"
    "@kmi/plugin-svgr" "2.0.29"
    "@kmi/preset-bundler" "2.0.29"
    "@kmi/preset-kmi" "2.0.29"

"@kmi/kmijs@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/kmijs/-/@kmi/kmijs-2.0.29.tgz#595f4511769467e28524f2aa0b156d7985ebb6dd"
  integrity sha512-ymeiZxvNFOEGN7exHeDxUUxb1URaasMDuGXMfTtKR/XkoP499pJbyDFb338jzhmvOYPilr/Ci6c0gplcAut+xw==
  dependencies:
    "@kmi/core" "2.0.29"
    "@kmi/preset-uni" "2.0.29"
    "@kmi/server" "2.0.29"
    "@kmi/shared" "2.0.29"
    "@kmi/types" "2.0.29"

"@kmi/plugin-browser-check@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/plugin-browser-check/-/@kmi/plugin-browser-check-2.0.29.tgz#3846caf0576332493c7a489370eb153821838753"
  integrity sha512-3/qCKaVUy7Z5vxwbXxKAIVt6lUmcxK3ZqhLoj3v3lZ49B/MTmlzDzR7LaZ4iOIhHu5aeU7x8LTikIho0xOjGEA==
  dependencies:
    caniuse-lite "1.0.30001690"

"@kmi/plugin-bundler-react@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/plugin-bundler-react/-/@kmi/plugin-bundler-react-2.0.29.tgz#56dc7f84c1c74b3fb6264e818adc0896f75749d1"
  integrity sha512-fV/G937pXDzKgBm5qS8VntcTSsddwBjOMSToFnFMzhORxv0BRi2CL0NfJHadyAz1FZIcC350CUIKkwTljzIxFA==
  dependencies:
    "@kmi/babel-preset-react" "2.0.29"
    "@rspack/plugin-react-refresh" "^1.0.1"
    react-refresh "0.14.0"

"@kmi/plugin-cdn-dt@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/plugin-cdn-dt/-/@kmi/plugin-cdn-dt-2.0.29.tgz#2446eff87801c0078bcd5daf6362df6e2d6d34b7"
  integrity sha512-5GDuMNhEWsOzFniBda86Rs4yVPB2Bgqio+a/JbJVa0gzdzTWtDRokB/CwjrlTJLT+avNdi9TcsVcT5sAhNOfXg==
  dependencies:
    "@kmi/bundler-compiled" "2.0.29"

"@kmi/plugin-svgr@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/plugin-svgr/-/@kmi/plugin-svgr-2.0.29.tgz#627bcb8c80c540b8f3f0425bc4aa4f3163332d80"
  integrity sha512-GenLm1Ipt/L331iAAecYAAWaK13ZZyB6KfPKDCi3ESFDJE3Thh89PtZ2mdqvlQ+tcJkfCZVZcDHcQLWoYCoetg==
  dependencies:
    "@kmi/bundler-compiled" "2.0.29"
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/shared" "2.0.29"
    loader-utils "2.0.4"

"@kmi/plugin-telemetry@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/plugin-telemetry/-/@kmi/plugin-telemetry-2.0.29.tgz#23281110837627f9c9b71080487c0f70074b434c"
  integrity sha512-yT33HS5nMJzVp8siF2oXXAjYB3cchzeYFgRMfA5vo7JeUchYG81qjaRk1DrrrnmDw9xTmDmfOzAXVXOZwRIXjg==

"@kmi/preset-bundler@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/preset-bundler/-/@kmi/preset-bundler-2.0.29.tgz#e8cf1dc1e0414882ea3f446018eea169f8615b1b"
  integrity sha512-o+qje3QQ+V/xVnSN28y1uiTzqH0BXmDOFGrH0jTv+4NCI2kxPTU5mr5SQvOSinlrQPGrYQLjXx1E3Xmkus/wUw==
  dependencies:
    "@kmi/bundler-compiled" "2.0.29"
    "@kmi/bundler-rspack" "2.0.29"
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/bundler-webpack" "2.0.29"
    "@kmi/server" "2.0.29"
    "@kmi/shared" "2.0.29"
    "@kmi/types" "2.0.29"
    "@umijs/es-module-parser" "0.0.7"
    "@umijs/zod2ts" "4.3.28"
    core-js "3.28.0"
    core-js-compat "3.28.0"
    regenerator-runtime "0.13.11"

"@kmi/preset-h5@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/preset-h5/-/@kmi/preset-h5-2.0.29.tgz#155889d55f80b8f754cb4d53c9fda24963fb6bcf"
  integrity sha512-Cl5ujbI3r3bme3csbAkJfxqe3YnjAptZ0iD6pn2GT6oan7Zfwac8Luz3hCPr9liD5VRcFczrCP4nwUakDDDFbg==

"@kmi/preset-kmi@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/preset-kmi/-/@kmi/preset-kmi-2.0.29.tgz#0388495f21d155ae700c3ba3c737b31ba36dd717"
  integrity sha512-8iTL97Fao5QcBEKuLeSNQzyWwbB1isDws8n3rw+N+aAzdqjg99NzCarozaTrwZtClLYY6FcaFo2ACYf6A/3ZoQ==
  dependencies:
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/core" "2.0.29"
    "@kmi/plugin-browser-check" "2.0.29"
    "@kmi/plugin-cdn-dt" "2.0.29"
    "@kmi/plugin-telemetry" "2.0.29"
    "@kmi/preset-h5" "2.0.29"
    dayjs "1.11.13"

"@kmi/preset-uni@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/preset-uni/-/@kmi/preset-uni-2.0.29.tgz#d6b24434afe746e18de73637881bcdbc267ddfc3"
  integrity sha512-Tm0Eva2LI+ZP17aPViEB5HBWNT9G9qbDzyXhQHK06g81A+QR1OcAxZcoXi1v5DOkRk3irzZcJoAcS+w7Mw0wuQ==
  dependencies:
    "@kmi/ast" "2.0.29"
    "@kmi/core" "2.0.29"
    "@kmi/shared" "2.0.29"
    "@kmi/types" "2.0.29"

"@kmi/remote-plugin-manager@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/remote-plugin-manager/-/@kmi/remote-plugin-manager-2.0.29.tgz#7a3558784a1b24d41a0bb85a506ec3e6e7454a96"
  integrity sha512-9VIELhs1UyQSEK+b7BjGqnybxy4ytQRf49FhGWUgUuldFTILoSn0ruiVWP0E2B+MLUtmiZ4iIG+jJpeOm6W2vg==
  dependencies:
    "@kmi/shared" "2.0.29"

"@kmi/server@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/server/-/@kmi/server-2.0.29.tgz#5e3522f36a10669c23705a2ba6912d6e8d1bdf44"
  integrity sha512-clt7c9nKOE3+l01HZ+Gy8NK9SoI6tw9L058xZyi2yEp8l4vXM5whCXwi8v392AKJZX+V0iKQKYhfwdC/1THUsA==
  dependencies:
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/shared" "2.0.29"
    "@kmi/types" "2.0.29"

"@kmi/shared@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/shared/-/@kmi/shared-2.0.29.tgz#d070111e7e1a7a4873ca3ba75cbd776d2b836ba2"
  integrity sha512-eLjvKAaDgkccce+i0K+t/EST3672DC2iP76ZXr61LhgnUsCawmdxr7QlO2r0PnZH7SiMotGDFE9N8uOmBi7C5g==
  dependencies:
    pino "7.11.0"

"@kmi/types@2.0.29":
  version "2.0.29"
  resolved "https://npm.corp.kuaishou.com/@kmi/types/-/@kmi/types-2.0.29.tgz#0f96f012204d6019c8982782e285d935d0314909"
  integrity sha512-SQiHcGYKEPGtkZoXPNKlQ/PUmyzDZ8t/0RzkcArz8SYaS//aG6tkqESfbXo9kzk7VbFvu+4TyH6JHclUB5w7cQ==
  dependencies:
    "@kmi/bundler-rspack" "2.0.29"
    "@kmi/bundler-shared" "2.0.29"
    "@kmi/core" "2.0.29"
    "@kmi/shared" "2.0.29"

"@ks-cqc/fingerprint-generator@^1.0.3":
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/@ks-cqc/fingerprint-generator/-/@ks-cqc/fingerprint-generator-1.0.4.tgz#ba67b158db681e801d72a16d9207031d91a03b6f"
  integrity sha512-mlieTPeSZSetHiydCI1OHeSRIGCMLXe5PyfQgMnZKjblsbt6hBvsFb6FUNbYl6FgCHw0NREglJkIUMPyLSj9FQ==

"@ks-cqc/h5-sig4-lite@1.0.6":
  version "1.0.6"
  resolved "https://npm.corp.kuaishou.com/@ks-cqc/h5-sig4-lite/-/@ks-cqc/h5-sig4-lite-1.0.6.tgz#71ba595ba203b669ae1eb49c5f01708114e05fbb"
  integrity sha512-0WwSEtf8LsDzsjTpiA6EKmecxxiNyCJWIJ0v5e0ZUX6sYElW/wzATjQYwyWW5dfiJiyLQiMI/N1vSK3/G/6q8w==

"@ks-hourglass/datamask@1.0.12":
  version "1.0.12"
  resolved "https://npm.corp.kuaishou.com/@ks-hourglass/datamask/-/@ks-hourglass/datamask-1.0.12.tgz#889abd119bb03a6302203c5dcbde414da231e9bf"
  integrity sha512-r7KuDXglBMm0s67Ea9EbytUgkZekK3ZRf39vEO0fLo/AgpfOfeSep1f5XGHmvk+k0qurJuAC4A6aMhbT+SUA2g==

"@ks-hourglass/record@2.5.5-beta.1":
  version "2.5.5-beta.1"
  resolved "https://npm.corp.kuaishou.com/@ks-hourglass/record/-/@ks-hourglass/record-2.5.5-beta.1.tgz#e81d00fa6d7851752491c5677ad46542fb91e14a"
  integrity sha512-/oco5zMrxFJFsngmQ3/QP0p7L8v76Lu/OakcXXNM+F+YlRXjHpkPfJUJSvFK1lOjmWeeASHPvirAf+T1NiGPDg==
  dependencies:
    "@ks-hourglass/datamask" "1.0.12"
    "@ks-hourglass/shared" "1.0.21"
    "@xstate/fsm" "^2.0.0"
    comlink "^4.3.1"
    idb "^8.0.0"
    lodash-es "^4.17.21"
    socket.io-client "^4.7.5"

"@ks-hourglass/shared@1.0.21":
  version "1.0.21"
  resolved "https://npm.corp.kuaishou.com/@ks-hourglass/shared/-/@ks-hourglass/shared-1.0.21.tgz#66b394889a78f65631f380e142621d7f898082e7"
  integrity sha512-oktw0hHLAVexfgQtCZWf6EKKadPAtMAMVIy+n/lNoUVfHuMgafMHU+KcMtzCtoRkPQBhYUvW5JcOt1oIVl3i0g==
  dependencies:
    nanoid "^3.3.1"

"@ks-radar/radar-api-collect@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-api-collect/-/@ks-radar/radar-api-collect-1.2.12.tgz#31b7ac88de509e23c0447eaba0e6eb22e99a01d3"
  integrity sha512-w9l+KiiEGT5P2R/2SL0ITXPhqV7hG1aOLel3H5+2oI8SRhQBjx5JzQTkwaobXQ776hByXKCk/J4xu/MyygTPFA==

"@ks-radar/radar-chrome-metrics-collect@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-chrome-metrics-collect/-/@ks-radar/radar-chrome-metrics-collect-1.2.12.tgz#b025f68eee31335a65ecf6dc3a0cd27c974c92ed"
  integrity sha512-bojS6/zimel8OL1ys5magi9ZJax3kI3880KDCeSYJbUIW35q3/LwYaD5eFuPlfIGpdVQeryvFgOVGorydKQYsQ==

"@ks-radar/radar-core@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-core/-/@ks-radar/radar-core-1.2.12.tgz#00248cc5d59ee935d285966702d71344555ce749"
  integrity sha512-Vq2PM0Fgjvjpk6DW4/s+p+tK6+m7H6z2KcELPYQiqlUsRlWgAR5FD6FfS2bYOOgmQaIu5pnc20whNqHYJeIJDw==

"@ks-radar/radar-core@1.2.7":
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-core/-/@ks-radar/radar-core-1.2.7.tgz#4f89a5b0e2adb9b91bfb7bb8fd2e61402c195027"
  integrity sha512-XftYQdQ3Yf4CA60IbXvX2rqAuECFMGF6ePMhJ1Ztgi/3/GL0ykykYdMCQ+GC2TAmxdO8xxfzMA3x+7GXVBqi1Q==

"@ks-radar/radar-core@^1.2.9":
  version "1.2.16"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-core/-/@ks-radar/radar-core-1.2.16.tgz#c9b97a043b5075e680f2192f6ac607e4ccb4f8c6"
  integrity sha512-JKI/CqoY4O9gRGDAcVuynTJzC0LyoNKAyOvJWSzhYUuz7Chfiofh500zljekJh2W81T6GW2LsY0fJ8eqvX0TVA==

"@ks-radar/radar-error-collect@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-error-collect/-/@ks-radar/radar-error-collect-1.2.12.tgz#e80974e8caa8496f5b2683c13416be1baa84a5a4"
  integrity sha512-LQ+eI07Pin1e+F46TTfY+4VVKnC7jw71Quq5RWiAWVg/HZGfc3PmPMPSUiBYWVpaSV1whLZYOgX/vE+8lGfORw==

"@ks-radar/radar-event-collect@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-event-collect/-/@ks-radar/radar-event-collect-1.2.12.tgz#6a0e2bd027040bb00bf1fe7326e14d9db9f9b803"
  integrity sha512-Fvi6Ub2K5vUpHwIH9jnB8V6gEjscxIw9m52Nez68o+ZHtknJOu8g7HJnCGOYehR/XBgfvQDvhvgsj6B/zbxMXw==

"@ks-radar/radar-event-collect@1.2.7":
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-event-collect/-/@ks-radar/radar-event-collect-1.2.7.tgz#266b0d7e4015ac84bf3cbac437150fb65b483231"
  integrity sha512-1UC8XAwjk564M/vn2dKPAi0qviSPx1KHqPnHAIofpUKEHkLoVWAWPGGq8/RGwPzLCUrozBBj2u67Rr2Sn5IBMg==

"@ks-radar/radar-event-collect@^1.2.9":
  version "1.2.16"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-event-collect/-/@ks-radar/radar-event-collect-1.2.16.tgz#6314047f0d7f64c6fc7b79d50119f36ec411ea00"
  integrity sha512-Q9zQ2cTwdpG1/I6JaBRX9X2A6r9A+nKK36ZMyr8S0UBFyXkJ0peogPfHVi/HgKe/qATvsI+gYhfQyIjSD9G9wg==

"@ks-radar/radar-navigation-collect@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-navigation-collect/-/@ks-radar/radar-navigation-collect-1.2.12.tgz#375cfcdd6a79c577e608c7621c07d748ccb6c7c9"
  integrity sha512-FgnGRJSuFqPRiydQBlmKolahrDzSnV4//2V2jFKdH5kHdOuYsoyw2Hez1d2Dq4dE7+SvYjWRjvVcTzkrif64Qg==

"@ks-radar/radar-resource-collect@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-resource-collect/-/@ks-radar/radar-resource-collect-1.2.12.tgz#6b86d2a622ae0c55d2f633a861661ebc1657e25b"
  integrity sha512-ISuHWy0oxiwD+NPs+SWtDm+LDJE016GRGu8GOPOHH6/ksEx5K2oBKYuaLvvc26OaCvCCtsWd9l4cO2YYq5XOmw==

"@ks-radar/radar-util@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-util/-/@ks-radar/radar-util-1.2.12.tgz#a36f717dce53908486f83ecaed1985465f1f19a2"
  integrity sha512-/gArX7j/gMuZO201xCY0es4lxL3a0PPyntWhXoPnHkAEv1BBBYK6u/f80dYnH7ealIoMmXTCs0XTGsslX0ZtUA==

"@ks-radar/radar-util@1.2.7":
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-util/-/@ks-radar/radar-util-1.2.7.tgz#8db3ef4b3097f108ecde4aef06e180f60040ffe2"
  integrity sha512-4x47CEAtAvKZNfsZiXRhXiAW3BnIjX8n99DLhEa6WqS4kZuZyMSUVhgHkB4qIAl0pEimZWNquDEuND1Ujzhg9w==

"@ks-radar/radar-util@^1.2.9":
  version "1.2.16"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar-util/-/@ks-radar/radar-util-1.2.16.tgz#8ef98e99c9dc66bcd234b423ae6f9ddf8206df8b"
  integrity sha512-unyFqDKiGcjfKRjTrrqNocFVDze3bkaLaoLRl7E90ecreAETJSLN9wQHuNoy+SX9w0nUEveuI76995a7y4byKA==

"@ks-radar/radar@1.2.12":
  version "1.2.12"
  resolved "https://npm.corp.kuaishou.com/@ks-radar/radar/-/@ks-radar/radar-1.2.12.tgz#47485baaaa558faef584d8736c98fcfaaee836b3"
  integrity sha512-a1/E3rw7jiG6dIksdXtOqJyFMgWe+OmCmb9GBofrshGI1BDP6QyB6b48evNG2B3XAb/Et5HwxQ5PQXy/CDW5YQ==
  dependencies:
    "@ks-radar/radar-api-collect" "1.2.12"
    "@ks-radar/radar-chrome-metrics-collect" "1.2.12"
    "@ks-radar/radar-core" "1.2.12"
    "@ks-radar/radar-error-collect" "1.2.12"
    "@ks-radar/radar-event-collect" "1.2.12"
    "@ks-radar/radar-navigation-collect" "1.2.12"
    "@ks-radar/radar-resource-collect" "1.2.12"
    "@ks-radar/radar-util" "1.2.12"
    "@ks/radar-spa-fmp-sdk" "^0.0.2-alpha.7"

"@ks-video/kwai-player-web@1.1.29":
  version "1.1.29"
  resolved "https://npm.corp.kuaishou.com/@ks-video/kwai-player-web/-/@ks-video/kwai-player-web-1.1.29.tgz#a47907be1ffd21a01add1be801adc2e315f1ed21"
  integrity sha512-SuAdl/65rVM5jZRrkN3P+78FXLS2O2hEiGOTzJcnPXAfXpASK1m/YgT55v7YbaBs7x8F748A8R0Ajn6XyGaXjA==

"@ks/identity-verification@1.0.0":
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/@ks%2fidentity-verification/-/identity-verification-1.0.0.tgz#5049c8aae75581cbc30f474535bc0f176fb3ec68"
  integrity sha512-0nGrSAsI36TPFGPNQfCiRTXF5slkdJovcxqzRC8v7i7SBQYeNYilV501GzUqgJrQfZdfmvEzncSxQV3DxP+TYA==
  dependencies:
    "@mfe/sdk-utils" "^1.0.1"

"@ks/radar-spa-fmp-sdk@^0.0.2-alpha.7":
  version "0.0.2-alpha.7"
  resolved "https://npm.corp.kuaishou.com/@ks/radar-spa-fmp-sdk/-/@ks/radar-spa-fmp-sdk-0.0.2-alpha.7.tgz#7e93a7b10e0de8a68e591dfe3f9a6400c37eb1b1"
  integrity sha512-jGulSY0LlfQlMRURQYL3S7mV+6x2lvgvt/eHmjAge8aaJE7DALIotf//Uowu92WSD9Dk3UYhaI/ogXzHbIh4TA==

"@ks/weblogger@3.10.28":
  version "3.10.28"
  resolved "https://npm.corp.kuaishou.com/@ks/weblogger/-/@ks/weblogger-3.10.28.tgz#7142487a46c92eed168d5767545731fb205e1a59"
  integrity sha512-mCWxeEoPwlv9/cT3PiDOznt5cfBb8yhfKWSVYV9Sk4wACkDF0+ySuv5U2kNaV7rMgjPrnF23IHv5+Vbw4MrhBA==
  dependencies:
    blueimp-md5 "^2.10.0"
    crypto-es "^1.2.7"
    utility-types "^3.9.0"
    web-vitals "^0.2.4"

"@ksuni/swc-plugin-auto-css-modules@0.1.0":
  version "0.1.0"
  resolved "https://npm.corp.kuaishou.com/@ksuni/swc-plugin-auto-css-modules/-/@ksuni/swc-plugin-auto-css-modules-0.1.0.tgz#07e39b3bd59d795f5d8a667784f0227d9abef77d"
  integrity sha512-z5L11GyL33mDf+a8wqlze/kWwhpWHpVIkL4OsqfUHAkMUds2F5b3Dgr52L1z+ir+sBtKPegF7qnQUjdLP1LhMg==

"@ljharb/resumer@~0.0.1":
  version "0.0.1"
  resolved "https://npm.corp.kuaishou.com/@ljharb/resumer/-/resumer-0.0.1.tgz#8a940a9192dd31f6a1df17564bbd26dc6ad3e68d"
  integrity sha512-skQiAOrCfO7vRTq53cxznMpks7wS1va95UCidALlOVWqvBAzwPVErwizDwoMqNVMEn1mDq0utxZd02eIrvF1lw==
  dependencies:
    "@ljharb/through" "^2.3.9"

"@ljharb/through@^2.3.9", "@ljharb/through@~2.3.9":
  version "2.3.14"
  resolved "https://npm.corp.kuaishou.com/@ljharb/through/-/through-2.3.14.tgz#a5df44295f44dc23bfe106af59426dd0677760b1"
  integrity sha512-ajBvlKpWucBB17FuQYUShqpqy8GRgYEpJW0vWJbUu1CV9lWyrDCapy0lScU8T8Z6qn49sSwJB3+M+evYIdGg+A==
  dependencies:
    call-bind "^1.0.8"

"@lumino/algorithm@^1.9.2":
  version "1.9.2"
  resolved "https://npm.corp.kuaishou.com/@lumino/algorithm/-/algorithm-1.9.2.tgz#b95e6419aed58ff6b863a51bfb4add0f795141d3"
  integrity sha512-Z06lp/yuhz8CtIir3PNTGnuk7909eXt4ukJsCzChsGuot2l5Fbs96RJ/FOHgwCedaX74CtxPjXHXoszFbUA+4A==

"@lumino/collections@^1.9.3":
  version "1.9.3"
  resolved "https://npm.corp.kuaishou.com/@lumino/collections/-/collections-1.9.3.tgz#370dc2d50aa91371288a4f7376bea5a3191fc5dc"
  integrity sha512-2i2Wf1xnfTgEgdyKEpqM16bcYRIhUOGCDzaVCEZACVG9R1CgYwOe3zfn71slBQOVSjjRgwYrgLXu4MBpt6YK+g==
  dependencies:
    "@lumino/algorithm" "^1.9.2"

"@lumino/commands@^1.21.1":
  version "1.21.1"
  resolved "https://npm.corp.kuaishou.com/@lumino/commands/-/commands-1.21.1.tgz#eda8b3cf5ef73b9c8ce93b3b5cf66bb053df2a76"
  integrity sha512-d1zJmwz5bHU0BM/Rl3tRdZ7/WgXnFB0bM7x7Bf0XDlmX++jnU9k0j3mh6/5JqCGLmIApKCRwVqSaV7jPmSJlcQ==
  dependencies:
    "@lumino/algorithm" "^1.9.2"
    "@lumino/coreutils" "^1.12.1"
    "@lumino/disposable" "^1.10.4"
    "@lumino/domutils" "^1.8.2"
    "@lumino/keyboard" "^1.8.2"
    "@lumino/signaling" "^1.11.1"
    "@lumino/virtualdom" "^1.14.3"

"@lumino/coreutils@^1.12.1":
  version "1.12.1"
  resolved "https://npm.corp.kuaishou.com/@lumino/coreutils/-/coreutils-1.12.1.tgz#79860c9937483ddf6cda87f6c2b9da8eb1a5d768"
  integrity sha512-JLu3nTHzJk9N8ohZ85u75YxemMrmDzJdNgZztfP7F7T7mxND3YVNCkJG35a6aJ7edu1sIgCjBxOvV+hv27iYvQ==

"@lumino/disposable@^1.10.4":
  version "1.10.4"
  resolved "https://npm.corp.kuaishou.com/@lumino/disposable/-/disposable-1.10.4.tgz#73b452044fecf988d7fa73fac9451b1a7f987323"
  integrity sha512-4ZxyYcyzUS+ZeB2KAH9oAH3w0DUUceiVr+FIZHZ2TAYGWZI/85WlqJtfm0xjwEpCwLLW1TDqJrISuZu3iMmVMA==
  dependencies:
    "@lumino/algorithm" "^1.9.2"
    "@lumino/signaling" "^1.11.1"

"@lumino/domutils@^1.8.2":
  version "1.8.2"
  resolved "https://npm.corp.kuaishou.com/@lumino/domutils/-/domutils-1.8.2.tgz#d15cdbae12bea52852bbc13c4629360f9f05b7f5"
  integrity sha512-QIpMfkPJrs4GrWBuJf2Sn1fpyVPmvqUUAeD8xAQo8+4V5JAT0vUDLxZ9HijefMgNCi3+Bs8Z3lQwRCrz+cFP1A==

"@lumino/dragdrop@^1.14.5":
  version "1.14.5"
  resolved "https://npm.corp.kuaishou.com/@lumino/dragdrop/-/dragdrop-1.14.5.tgz#1db76c8a01f74cb1b0428db6234e820bb58b93ba"
  integrity sha512-LC5xB82+xGF8hFyl716TMpV32OIMIMl+s3RU1PaqDkD6B7PkgiVk6NkJ4X9/GcEvl2igkvlGQt/3L7qxDAJNxw==
  dependencies:
    "@lumino/coreutils" "^1.12.1"
    "@lumino/disposable" "^1.10.4"

"@lumino/keyboard@^1.8.2":
  version "1.8.2"
  resolved "https://npm.corp.kuaishou.com/@lumino/keyboard/-/keyboard-1.8.2.tgz#714dbe671f0718f516d1ec23188b31a9ccd82fb2"
  integrity sha512-Dy+XqQ1wXbcnuYtjys5A0pAqf4SpAFl9NY6owyIhXAo0Va7w3LYp3jgiP1xAaBAwMuUppiUAfrbjrysZuZ625g==

"@lumino/messaging@^1.10.3":
  version "1.10.3"
  resolved "https://npm.corp.kuaishou.com/@lumino/messaging/-/messaging-1.10.3.tgz#b6227bdfc178a8542571625ecb68063691b6af3c"
  integrity sha512-F/KOwMCdqvdEG8CYAJcBSadzp6aI7a47Fr60zAKGqZATSRRRV41q53iXU7HjFPqQqQIvdn9Z7J32rBEAyQAzww==
  dependencies:
    "@lumino/algorithm" "^1.9.2"
    "@lumino/collections" "^1.9.3"

"@lumino/properties@^1.8.2":
  version "1.8.2"
  resolved "https://npm.corp.kuaishou.com/@lumino/properties/-/properties-1.8.2.tgz#91131f2ca91a902faa138771eb63341db78fc0fd"
  integrity sha512-EkjI9Cw8R0U+xC9HxdFSu7X1tz1H1vKu20cGvJ2gU+CXlMB1DvoYJCYxCThByHZ+kURTAap4SE5x8HvKwNPbig==

"@lumino/signaling@^1.11.1":
  version "1.11.1"
  resolved "https://npm.corp.kuaishou.com/@lumino/signaling/-/signaling-1.11.1.tgz#438f447a1b644fd286549804f9851b5aec9679a2"
  integrity sha512-YCUmgw08VoyMN5KxzqPO3KMx+cwdPv28tAN06C0K7Q/dQf+oufb1XocuhZb5selTrTmmuXeizaYxgLIQGdS1fA==
  dependencies:
    "@lumino/algorithm" "^1.9.2"
    "@lumino/properties" "^1.8.2"

"@lumino/virtualdom@^1.14.3":
  version "1.14.3"
  resolved "https://npm.corp.kuaishou.com/@lumino/virtualdom/-/virtualdom-1.14.3.tgz#e490c36ff506d877cf45771d6968e3e26a8919fd"
  integrity sha512-5joUC1yuxeXbpfbSBm/OR8Mu9HoTo6PDX0RKqzlJ9o97iml7zayFN/ynzcxScKGQAo9iaXOY8uVIvGUT8FnsGw==
  dependencies:
    "@lumino/algorithm" "^1.9.2"

"@lumino/widgets@^1.37.2":
  version "1.37.2"
  resolved "https://npm.corp.kuaishou.com/@lumino/widgets/-/widgets-1.37.2.tgz#b408fae221ecec2f1b028607782fbe1e82588bce"
  integrity sha512-NHKu1NBDo6ETBDoNrqSkornfUCwc8EFFzw6+LWBfYVxn2PIwciq2SdiJGEyNqL+0h/A9eVKb5ui5z4cwpRekmQ==
  dependencies:
    "@lumino/algorithm" "^1.9.2"
    "@lumino/commands" "^1.21.1"
    "@lumino/coreutils" "^1.12.1"
    "@lumino/disposable" "^1.10.4"
    "@lumino/domutils" "^1.8.2"
    "@lumino/dragdrop" "^1.14.5"
    "@lumino/keyboard" "^1.8.2"
    "@lumino/messaging" "^1.10.3"
    "@lumino/properties" "^1.8.2"
    "@lumino/signaling" "^1.11.1"
    "@lumino/virtualdom" "^1.14.3"

"@m-ui/icons-svg@^2.5.3":
  version "2.5.3"
  resolved "https://npm.corp.kuaishou.com/@m-ui/icons-svg/-/@m-ui/icons-svg-2.5.3.tgz#d22ea6707e6f8cf534eacac3346d8f6d0366f335"
  integrity sha512-tWtn1bIezCeE4P6KTpvM3Qt6JNiZApzSPxB7QM98/3VlSdWlvAIt1zjvJArQCE74/c4cCLkULOOphBBxoS342A==

"@m-ui/icons-svg@^4.0.2":
  version "4.0.2"
  resolved "https://npm.corp.kuaishou.com/@m-ui/icons-svg/-/@m-ui/icons-svg-4.0.2.tgz#393f386b8788736e2976b22f6b0ec677c6a50fe8"
  integrity sha512-EhDSiTcernz0FDGCVQ4JjQ4MlGC1pbno8h4synRMnMYIal5sE+VwQ/BLPnfMrqdCzOCXPAz9xi+iClkvWp1uNA==

"@m-ui/icons@4.0.2":
  version "4.0.2"
  resolved "https://npm.corp.kuaishou.com/@m-ui/icons/-/@m-ui/icons-4.0.2.tgz#2b7bb24812ee7efea386ff0598ab7ab61c0ee76f"
  integrity sha512-/z9qd6rBiQOFaEEiPD/AoVTFOh0QS475+C1GYPzk3RBfVO2Fsp0bZzkl4juoxyZt4y4Oa/0YRdNWItjtTMNrYw==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@babel/runtime" "^7.11.2"
    "@m-ui/icons-svg" "^4.0.2"
    classnames "^2.2.6"
    rc-util "^5.9.4"

"@m-ui/icons@^2.0.1":
  version "2.5.3"
  resolved "https://npm.corp.kuaishou.com/@m-ui/icons/-/@m-ui/icons-2.5.3.tgz#3601a17efbcd5300c78b56d9bed3a506905f873f"
  integrity sha512-9pxGFoP6h1JJW7NfmHwU5lt+QDcW+CHFaGVHftOs5kwo3CpSxxzYyW6PQiyb9Xmqh5Li4F8cX430CfkNM2X9TA==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@babel/runtime" "^7.11.2"
    "@m-ui/icons-svg" "^2.5.3"
    classnames "^2.2.6"
    rc-util "^5.9.4"

"@m-ui/rc-select@^13.2.2":
  version "13.2.2"
  resolved "https://npm.corp.kuaishou.com/@m-ui%2frc-select/-/rc-select-13.2.2.tgz#dab12de8a0cbed76bde55ab6b3d65d0fd68d1245"
  integrity sha512-igqRLLhkVyEbou5QsRaHb87Mk5XAe/yN0ugT67zB74ohRifhgnHTqg71FnAREEorRcU12Kh8YKd4S4BeOrY4wg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.0.0"
    rc-trigger "^5.0.4"
    rc-util "^5.9.8"
    rc-virtual-list "^3.2.0"

"@m-ui/react@2.2.2":
  version "2.2.2"
  resolved "https://npm.corp.kuaishou.com/@m-ui/react/-/@m-ui/react-2.2.2.tgz#bee1383d1daf78883442e7a59707db9de8742f0d"
  integrity sha512-mPR8NMn1FyIG70tYU6BL/MVZZ3k3pM+mA9j0doW138852sd+LzYO46C5vePr5bmQufVBLYCx12nZe+4Zu1vDgQ==
  dependencies:
    "@ant-design/colors" "^6.0.0"
    "@ant-design/react-slick" "~0.28.1"
    "@babel/runtime" "^7.12.5"
    "@ctrl/tinycolor" "^3.4.0"
    "@m-ui/icons" "4.0.2"
    "@m-ui/rc-select" "^13.2.2"
    classnames "^2.2.6"
    copy-to-clipboard "^3.2.0"
    goober "^2.0.41"
    lodash "^4.17.21"
    memoize-one "^6.0.0"
    moment "^2.25.3"
    rc-cascader "~3.6.0"
    rc-checkbox "~2.3.0"
    rc-collapse "~3.1.0"
    rc-dialog "~8.6.0"
    rc-drawer "~4.4.2"
    rc-dropdown "~3.2.0"
    rc-field-form "~1.27.0"
    rc-image "~5.2.5"
    rc-input-number "~7.3.0"
    rc-mentions "~1.6.1"
    rc-menu "~9.8.1"
    rc-motion "^2.4.4"
    rc-notification "~4.5.7"
    rc-pagination "~3.1.9"
    rc-picker "~2.5.17"
    rc-progress "~3.1.0"
    rc-rate "~2.9.0"
    rc-resize-observer "^1.1.2"
    rc-select "~13.2.1"
    rc-slider "~9.7.4"
    rc-steps "~4.1.0"
    rc-switch "~3.2.0"
    rc-table "~7.23.0"
    rc-tabs "~11.10.0"
    rc-textarea "~0.4.5"
    rc-tooltip "~5.1.1"
    rc-tree "~5.6.5"
    rc-tree-select "~4.8.0"
    rc-trigger "^5.2.10"
    rc-upload "~4.3.0"
    rc-util "^5.14.0"
    scroll-into-view-if-needed "^2.2.25"

"@mfe/sdk-utils@^1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@mfe%2fsdk-utils/-/sdk-utils-1.0.1.tgz#f93d8cfc867ae5540b556b6e97fd83cf1d6a3920"
  integrity sha512-AteCIRG6bHwAcf28rRQX9H85lXYvLpQjkHviI05wzuOlYILrBWommwhelN+6y+3BoXAPBq4E0RQyqeC6eESivg==

"@module-federation/error-codes@0.8.4":
  version "0.8.4"
  resolved "https://npm.corp.kuaishou.com/@module-federation/error-codes/-/error-codes-0.8.4.tgz#c66ead0da86bc010fa53187462c704b3e0d5a256"
  integrity sha512-55LYmrDdKb4jt+qr8qE8U3al62ZANp3FhfVaNPOaAmdTh0jHdD8M3yf5HKFlr5xVkVO4eV/F/J2NCfpbh+pEXQ==

"@module-federation/runtime-tools@0.8.4":
  version "0.8.4"
  resolved "https://npm.corp.kuaishou.com/@module-federation/runtime-tools/-/runtime-tools-0.8.4.tgz#ddf8461fe9b5d5e962511f4e5b622008ee46bde8"
  integrity sha512-fjVOsItJ1u5YY6E9FnS56UDwZgqEQUrWFnouRiPtK123LUuqUI9FH4redZoKWlE1PB0ir1Z3tnqy8eFYzPO38Q==
  dependencies:
    "@module-federation/runtime" "0.8.4"
    "@module-federation/webpack-bundler-runtime" "0.8.4"

"@module-federation/runtime@0.8.4":
  version "0.8.4"
  resolved "https://npm.corp.kuaishou.com/@module-federation/runtime/-/runtime-0.8.4.tgz#7fc63e1b7dda0506bb2a70c1a52aa73513c5b508"
  integrity sha512-yZeZ7z2Rx4gv/0E97oLTF3V6N25vglmwXGgoeju/W2YjsFvWzVtCDI7zRRb0mJhU6+jmSM8jP1DeQGbea/AiZQ==
  dependencies:
    "@module-federation/error-codes" "0.8.4"
    "@module-federation/sdk" "0.8.4"

"@module-federation/sdk@0.8.4":
  version "0.8.4"
  resolved "https://npm.corp.kuaishou.com/@module-federation/sdk/-/sdk-0.8.4.tgz#956e178e104d640482e5afe93c7e3a095a589807"
  integrity sha512-waABomIjg/5m1rPDBWYG4KUhS5r7OUUY7S+avpaVIY/tkPWB3ibRDKy2dNLLAMaLKq0u+B1qIdEp4NIWkqhqpg==
  dependencies:
    isomorphic-rslog "0.0.6"

"@module-federation/webpack-bundler-runtime@0.8.4":
  version "0.8.4"
  resolved "https://npm.corp.kuaishou.com/@module-federation/webpack-bundler-runtime/-/webpack-bundler-runtime-0.8.4.tgz#c01f5a5c5d61664c21ac6c479ebe9d8bf09d22d6"
  integrity sha512-HggROJhvHPUX7uqBD/XlajGygMNM1DG0+4OAkk8MBQe4a18QzrRNzZt6XQbRTSG4OaEoyRWhQHvYD3Yps405tQ==
  dependencies:
    "@module-federation/runtime" "0.8.4"
    "@module-federation/sdk" "0.8.4"

"@monaco-editor/loader@^1.5.0":
  version "1.5.0"
  resolved "https://npm.corp.kuaishou.com/@monaco-editor/loader/-/loader-1.5.0.tgz#dcdbc7fe7e905690fb449bed1c251769f325c55d"
  integrity sha512-hKoGSM+7aAc7eRTRjpqAZucPmoNOC4UUbknb/VNoTkEIkCPhqV8LfbsgM1webRM7S/z21eHEx9Fkwx8Z/C/+Xw==
  dependencies:
    state-local "^1.0.6"

"@monaco-editor/react@^4.7.0":
  version "4.7.0"
  resolved "https://npm.corp.kuaishou.com/@monaco-editor/react/-/react-4.7.0.tgz#35a1ec01bfe729f38bfc025df7b7bac145602a60"
  integrity sha512-cyzXQCtO47ydzxpQtCGSQGOC8Gk3ZUeBXFAxD+CWXYFo5OqZyZUonFl0DwUlTyAfRHntBfw2p3w4s9R6oe1eCA==
  dependencies:
    "@monaco-editor/loader" "^1.5.0"

"@naoak/workerize-transferable@^0.1.0":
  version "0.1.0"
  resolved "https://npm.corp.kuaishou.com/@naoak/workerize-transferable/-/workerize-transferable-0.1.0.tgz#864cc8241b977bffd8661c0be1441da9b4bfb633"
  integrity sha512-fDLfuP71IPNP5+zSfxFb52OHgtjZvauRJWbVnpzQ7G7BjcbLjTny0OW1d3ZO806XKpLWNKmeeW3MhE0sy8iwYQ==

"@napi-rs/nice-android-arm-eabi@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-android-arm-eabi/-/nice-android-arm-eabi-1.0.1.tgz#9a0cba12706ff56500df127d6f4caf28ddb94936"
  integrity sha512-5qpvOu5IGwDo7MEKVqqyAxF90I6aLj4n07OzpARdgDRfz8UbBztTByBp0RC59r3J1Ij8uzYi6jI7r5Lws7nn6w==

"@napi-rs/nice-android-arm64@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-android-arm64/-/nice-android-arm64-1.0.1.tgz#32fc32e9649bd759d2a39ad745e95766f6759d2f"
  integrity sha512-GqvXL0P8fZ+mQqG1g0o4AO9hJjQaeYG84FRfZaYjyJtZZZcMjXW5TwkL8Y8UApheJgyE13TQ4YNUssQaTgTyvA==

"@napi-rs/nice-darwin-arm64@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-darwin-arm64/-/nice-darwin-arm64-1.0.1.tgz#d3c44c51b94b25a82d45803e2255891e833e787b"
  integrity sha512-91k3HEqUl2fsrz/sKkuEkscj6EAj3/eZNCLqzD2AA0TtVbkQi8nqxZCZDMkfklULmxLkMxuUdKe7RvG/T6s2AA==

"@napi-rs/nice-darwin-x64@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-darwin-x64/-/nice-darwin-x64-1.0.1.tgz#f1b1365a8370c6a6957e90085a9b4873d0e6a957"
  integrity sha512-jXnMleYSIR/+TAN/p5u+NkCA7yidgswx5ftqzXdD5wgy/hNR92oerTXHc0jrlBisbd7DpzoaGY4cFD7Sm5GlgQ==

"@napi-rs/nice-freebsd-x64@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-freebsd-x64/-/nice-freebsd-x64-1.0.1.tgz#4280f081efbe0b46c5165fdaea8b286e55a8f89e"
  integrity sha512-j+iJ/ezONXRQsVIB/FJfwjeQXX7A2tf3gEXs4WUGFrJjpe/z2KB7sOv6zpkm08PofF36C9S7wTNuzHZ/Iiccfw==

"@napi-rs/nice-linux-arm-gnueabihf@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-arm-gnueabihf/-/nice-linux-arm-gnueabihf-1.0.1.tgz#07aec23a9467ed35eb7602af5e63d42c5d7bd473"
  integrity sha512-G8RgJ8FYXYkkSGQwywAUh84m946UTn6l03/vmEXBYNJxQJcD+I3B3k5jmjFG/OPiU8DfvxutOP8bi+F89MCV7Q==

"@napi-rs/nice-linux-arm64-gnu@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-arm64-gnu/-/nice-linux-arm64-gnu-1.0.1.tgz#038a77134cc6df3c48059d5a5e199d6f50fb9a90"
  integrity sha512-IMDak59/W5JSab1oZvmNbrms3mHqcreaCeClUjwlwDr0m3BoR09ZiN8cKFBzuSlXgRdZ4PNqCYNeGQv7YMTjuA==

"@napi-rs/nice-linux-arm64-musl@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-arm64-musl/-/nice-linux-arm64-musl-1.0.1.tgz#715d0906582ba0cff025109f42e5b84ea68c2bcc"
  integrity sha512-wG8fa2VKuWM4CfjOjjRX9YLIbysSVV1S3Kgm2Fnc67ap/soHBeYZa6AGMeR5BJAylYRjnoVOzV19Cmkco3QEPw==

"@napi-rs/nice-linux-ppc64-gnu@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-ppc64-gnu/-/nice-linux-ppc64-gnu-1.0.1.tgz#ac1c8f781c67b0559fa7a1cd4ae3ca2299dc3d06"
  integrity sha512-lxQ9WrBf0IlNTCA9oS2jg/iAjQyTI6JHzABV664LLrLA/SIdD+I1i3Mjf7TsnoUbgopBcCuDztVLfJ0q9ubf6Q==

"@napi-rs/nice-linux-riscv64-gnu@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-riscv64-gnu/-/nice-linux-riscv64-gnu-1.0.1.tgz#b0a430549acfd3920ffd28ce544e2fe17833d263"
  integrity sha512-3xs69dO8WSWBb13KBVex+yvxmUeEsdWexxibqskzoKaWx9AIqkMbWmE2npkazJoopPKX2ULKd8Fm9veEn0g4Ig==

"@napi-rs/nice-linux-s390x-gnu@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-s390x-gnu/-/nice-linux-s390x-gnu-1.0.1.tgz#5b95caf411ad72a965885217db378c4d09733e97"
  integrity sha512-lMFI3i9rlW7hgToyAzTaEybQYGbQHDrpRkg+1gJWEpH0PLAQoZ8jiY0IzakLfNWnVda1eTYYlxxFYzW8Rqczkg==

"@napi-rs/nice-linux-x64-gnu@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-x64-gnu/-/nice-linux-x64-gnu-1.0.1.tgz#a98cdef517549f8c17a83f0236a69418a90e77b7"
  integrity sha512-XQAJs7DRN2GpLN6Fb+ZdGFeYZDdGl2Fn3TmFlqEL5JorgWKrQGRUrpGKbgZ25UeZPILuTKJ+OowG2avN8mThBA==

"@napi-rs/nice-linux-x64-musl@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-linux-x64-musl/-/nice-linux-x64-musl-1.0.1.tgz#5e26843eafa940138aed437c870cca751c8a8957"
  integrity sha512-/rodHpRSgiI9o1faq9SZOp/o2QkKQg7T+DK0R5AkbnI/YxvAIEHf2cngjYzLMQSQgUhxym+LFr+UGZx4vK4QdQ==

"@napi-rs/nice-win32-arm64-msvc@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-win32-arm64-msvc/-/nice-win32-arm64-msvc-1.0.1.tgz#bd62617d02f04aa30ab1e9081363856715f84cd8"
  integrity sha512-rEcz9vZymaCB3OqEXoHnp9YViLct8ugF+6uO5McifTedjq4QMQs3DHz35xBEGhH3gJWEsXMUbzazkz5KNM5YUg==

"@napi-rs/nice-win32-ia32-msvc@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-win32-ia32-msvc/-/nice-win32-ia32-msvc-1.0.1.tgz#b8b7aad552a24836027473d9b9f16edaeabecf18"
  integrity sha512-t7eBAyPUrWL8su3gDxw9xxxqNwZzAqKo0Szv3IjVQd1GpXXVkb6vBBQUuxfIYaXMzZLwlxRQ7uzM2vdUE9ULGw==

"@napi-rs/nice-win32-x64-msvc@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice-win32-x64-msvc/-/nice-win32-x64-msvc-1.0.1.tgz#37d8718b8f722f49067713e9f1e85540c9a3dd09"
  integrity sha512-JlF+uDcatt3St2ntBG8H02F1mM45i5SF9W+bIKiReVE6wiy3o16oBP/yxt+RZ+N6LbCImJXJ6bXNO2kn9AXicg==

"@napi-rs/nice@^1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/nice/-/nice-1.0.1.tgz#483d3ff31e5661829a1efb4825591a135c3bfa7d"
  integrity sha512-zM0mVWSXE0a0h9aKACLwKmD6nHcRiKrPpCfvaKqG1CqDEyjEawId0ocXxVzPMCAm6kkWr2P025msfxXEnt8UGQ==
  optionalDependencies:
    "@napi-rs/nice-android-arm-eabi" "1.0.1"
    "@napi-rs/nice-android-arm64" "1.0.1"
    "@napi-rs/nice-darwin-arm64" "1.0.1"
    "@napi-rs/nice-darwin-x64" "1.0.1"
    "@napi-rs/nice-freebsd-x64" "1.0.1"
    "@napi-rs/nice-linux-arm-gnueabihf" "1.0.1"
    "@napi-rs/nice-linux-arm64-gnu" "1.0.1"
    "@napi-rs/nice-linux-arm64-musl" "1.0.1"
    "@napi-rs/nice-linux-ppc64-gnu" "1.0.1"
    "@napi-rs/nice-linux-riscv64-gnu" "1.0.1"
    "@napi-rs/nice-linux-s390x-gnu" "1.0.1"
    "@napi-rs/nice-linux-x64-gnu" "1.0.1"
    "@napi-rs/nice-linux-x64-musl" "1.0.1"
    "@napi-rs/nice-win32-arm64-msvc" "1.0.1"
    "@napi-rs/nice-win32-ia32-msvc" "1.0.1"
    "@napi-rs/nice-win32-x64-msvc" "1.0.1"

"@napi-rs/wasm-runtime@^0.2.4":
  version "0.2.8"
  resolved "https://npm.corp.kuaishou.com/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.8.tgz#642e8390ee78ed21d6b79c467aa610e249224ed6"
  integrity sha512-OBlgKdX7gin7OIq4fadsjpg+cp2ZphvAIKucHsNfTdJiqdOmOEwQd/bHi0VwNrcw5xpBJyUw6cK/QilCqy1BSg==
  dependencies:
    "@emnapi/core" "^1.4.0"
    "@emnapi/runtime" "^1.4.0"
    "@tybys/wasm-util" "^0.9.0"

"@oxc-resolver/binding-darwin-arm64@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-darwin-arm64/-/binding-darwin-arm64-1.12.0.tgz#efa73e417e58f4c29b540a5b709543723614e3ef"
  integrity sha512-wYe+dlF8npM7cwopOOxbdNjtmJp17e/xF5c0K2WooQXy5VOh74icydM33+Uh/SZDgwyum09/U1FVCX5GdeQk+A==

"@oxc-resolver/binding-darwin-x64@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-darwin-x64/-/binding-darwin-x64-1.12.0.tgz#4c6fb861e43187a10e84416a18a9cf0a443fab63"
  integrity sha512-FZxxp99om+SlvBr1cjzF8A3TjYcS0BInCqjUlM+2f9m9bPTR2Bng9Zq5Q09ZQyrKJjfGKqlOEHs3akuVOnrx3Q==

"@oxc-resolver/binding-freebsd-x64@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-freebsd-x64/-/binding-freebsd-x64-1.12.0.tgz#83a8586f0cf767fb566b50b1bf20d186bbf59f25"
  integrity sha512-BZi0iU6IEOnXGSkqt1OjTTkN9wfyaK6kTpQwL/axl8eCcNDc7wbv1vloHgILf7ozAY1TP75nsLYlASYI4B5kGA==

"@oxc-resolver/binding-linux-arm-gnueabihf@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-linux-arm-gnueabihf/-/binding-linux-arm-gnueabihf-1.12.0.tgz#a1365e73432bfd25b6c37a77eb4b037ed978cfb3"
  integrity sha512-L2qnMEnZAqxbG9b1J3di/w/THIm+1fMVfbbTMWIQNMMXdMeqqDN6ojnOLDtuP564rAh4TBFPdLyEfGhMz6ipNA==

"@oxc-resolver/binding-linux-arm64-gnu@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-linux-arm64-gnu/-/binding-linux-arm64-gnu-1.12.0.tgz#6cbba2f1002c1d4f0df63c3942432a866b3e9f4f"
  integrity sha512-otVbS4zeo3n71zgGLBYRTriDzc0zpruC0WI3ICwjpIk454cLwGV0yzh4jlGYWQJYJk0BRAmXFd3ooKIF+bKBHw==

"@oxc-resolver/binding-linux-arm64-musl@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-linux-arm64-musl/-/binding-linux-arm64-musl-1.12.0.tgz#64640c9b574252650b0c4ef84d75fe359e7c2460"
  integrity sha512-IStQDjIT7Lzmqg1i9wXvPL/NsYsxF24WqaQFS8b8rxra+z0VG7saBOsEnOaa4jcEY8MVpLYabFhTV+fSsA2vnA==

"@oxc-resolver/binding-linux-x64-gnu@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-linux-x64-gnu/-/binding-linux-x64-gnu-1.12.0.tgz#0d76538cfd36e7ebfc9af8ff06b9cd742b09fe53"
  integrity sha512-SipT7EVORz8pOQSFwemOm91TpSiBAGmOjG830/o+aLEsvQ4pEy223+SAnCfITh7+AahldYsJnVoIs519jmIlKQ==

"@oxc-resolver/binding-linux-x64-musl@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-linux-x64-musl/-/binding-linux-x64-musl-1.12.0.tgz#5cc5f7dfd711a23c7301f8643aaa2e2700ba884f"
  integrity sha512-mGh0XfUzKdn+WFaqPacziNraCWL5znkHRfQVxG9avGS9zb2KC/N1EBbPzFqutDwixGDP54r2gx4q54YCJEZ4iQ==

"@oxc-resolver/binding-wasm32-wasi@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-wasm32-wasi/-/binding-wasm32-wasi-1.12.0.tgz#d99656021b92190ed43902431fdc5a3b88a2cd77"
  integrity sha512-SZN6v7apKmQf/Vwiqb6e/s3Y2Oacw8uW8V2i1AlxtyaEFvnFE0UBn89zq6swEwE3OCajNWs0yPvgAXUMddYc7Q==
  dependencies:
    "@napi-rs/wasm-runtime" "^0.2.4"

"@oxc-resolver/binding-win32-arm64-msvc@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-win32-arm64-msvc/-/binding-win32-arm64-msvc-1.12.0.tgz#65a65ccb7dabf2a8720527143e2352f43d57ad5b"
  integrity sha512-GRe4bqCfFsyghruEn5bv47s9w3EWBdO2q72xCz5kpQ0LWbw+enPHtTjw3qX5PUcFYpKykM55FaO0hFDs1yzatw==

"@oxc-resolver/binding-win32-x64-msvc@1.12.0":
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/@oxc-resolver/binding-win32-x64-msvc/-/binding-win32-x64-msvc-1.12.0.tgz#4e9b68dda8481a6d7d3e249ebfddd2c39728e923"
  integrity sha512-Z3llHH0jfJP4mlWq3DT7bK6qV+/vYe0+xzCgfc67+Tc/U3eYndujl880bexeGdGNPh87JeYznpZAOJ44N7QVVQ==

"@rc-component/async-validator@^5.0.3":
  version "5.0.4"
  resolved "https://npm.corp.kuaishou.com/@rc-component/async-validator/-/async-validator-5.0.4.tgz#5291ad92f00a14b6766fc81735c234277f83e948"
  integrity sha512-qgGdcVIF604M9EqjNF0hbUTz42bz/RDtxWdWuU5EQe3hi7M8ob54B6B35rOsvX5eSvIHIzT9iH1R3n+hk3CGfg==
  dependencies:
    "@babel/runtime" "^7.24.4"

"@rc-component/color-picker@~2.0.1":
  version "2.0.1"
  resolved "https://npm.corp.kuaishou.com/@rc-component/color-picker/-/color-picker-2.0.1.tgz#6b9b96152466a9d4475cbe72b40b594bfda164be"
  integrity sha512-WcZYwAThV/b2GISQ8F+7650r5ZZJ043E57aVBFkQ+kSY4C6wdofXgB0hBx+GPGpIU0Z81eETNoDUJMr7oy/P8Q==
  dependencies:
    "@ant-design/fast-color" "^2.0.6"
    "@babel/runtime" "^7.23.6"
    classnames "^2.2.6"
    rc-util "^5.38.1"

"@rc-component/context@^1.4.0":
  version "1.4.0"
  resolved "https://npm.corp.kuaishou.com/@rc-component/context/-/context-1.4.0.tgz#dc6fb021d6773546af8f016ae4ce9aea088395e8"
  integrity sha512-kFcNxg9oLRMoL3qki0OMxK+7g5mypjgaaJp/pkOis/6rVxma9nJBF/8kCIuTYHUQNr0ii7MxqE33wirPZLJQ2w==
  dependencies:
    "@babel/runtime" "^7.10.1"
    rc-util "^5.27.0"

"@rc-component/mini-decimal@^1.0.1":
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/@rc-component/mini-decimal/-/mini-decimal-1.1.0.tgz#7b7a362b14a0a54cb5bc6fd2b82731f29f11d9b0"
  integrity sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ==
  dependencies:
    "@babel/runtime" "^7.18.0"

"@rc-component/mutate-observer@^1.1.0":
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/@rc-component/mutate-observer/-/mutate-observer-1.1.0.tgz#ee53cc88b78aade3cd0653609215a44779386fd8"
  integrity sha512-QjrOsDXQusNwGZPf4/qRQasg7UFEj06XiCJ8iuiq/Io7CrHrgVi6Uuetw60WAMG1799v+aM8kyc+1L/GBbHSlw==
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/portal@^1.0.0-8", "@rc-component/portal@^1.0.0-9", "@rc-component/portal@^1.0.2", "@rc-component/portal@^1.1.0", "@rc-component/portal@^1.1.1":
  version "1.1.2"
  resolved "https://npm.corp.kuaishou.com/@rc-component/portal/-/portal-1.1.2.tgz#55db1e51d784e034442e9700536faaa6ab63fc71"
  integrity sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/qrcode@~1.0.0":
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/@rc-component/qrcode/-/qrcode-1.0.0.tgz#48a8de5eb11d0e65926f1377c4b1ef4c888997f5"
  integrity sha512-L+rZ4HXP2sJ1gHMGHjsg9jlYBX/SLN2D6OxP9Zn3qgtpMWtO2vUfxVFwiogHpAIqs54FnALxraUy/BCO1yRIgg==
  dependencies:
    "@babel/runtime" "^7.24.7"
    classnames "^2.3.2"
    rc-util "^5.38.0"

"@rc-component/tour@~1.15.1":
  version "1.15.1"
  resolved "https://npm.corp.kuaishou.com/@rc-component/tour/-/tour-1.15.1.tgz#9b79808254185fc19e964172d99e25e8c6800ded"
  integrity sha512-Tr2t7J1DKZUpfJuDZWHxyxWpfmj8EZrqSgyMZ+BCdvKZ6r1UDsfU46M/iWAAFBy961Ssfom2kv5f3UcjIL2CmQ==
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/portal" "^1.0.0-9"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/trigger@^2.0.0", "@rc-component/trigger@^2.1.1", "@rc-component/trigger@^2.2.6":
  version "2.2.6"
  resolved "https://npm.corp.kuaishou.com/@rc-component/trigger/-/trigger-2.2.6.tgz#bfe6602313b3fadd659687746511f813299d5ea4"
  integrity sha512-/9zuTnWwhQ3S3WT1T8BubuFTT46kvnXgaERR9f4BTKyn61/wpf/BvbImzYBubzJibU707FxwbKszLlHjcLiv1Q==
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@rc-component/portal" "^1.1.0"
    classnames "^2.3.2"
    rc-motion "^2.0.0"
    rc-resize-observer "^1.3.1"
    rc-util "^5.44.0"

"@react-dnd/asap@4.0.1":
  version "4.0.1"
  resolved "https://npm.corp.kuaishou.com/@react-dnd/asap/-/asap-4.0.1.tgz#5291850a6b58ce6f2da25352a64f1b0674871aab"
  integrity sha512-kLy0PJDDwvwwTXxqTFNAAllPHD73AycE9ypWeln/IguoGBEbvFcPDbCV03G52bEcC5E+YgupBE0VzHGdC8SIXg==

"@react-dnd/invariant@3.0.1":
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/@react-dnd/invariant/-/invariant-3.0.1.tgz#7e70be19ea21b539e8bf1da28466f4f05df2a4cc"
  integrity sha512-blqduwV86oiKw2Gr44wbe3pj3Z/OsXirc7ybCv9F/pLAR+Aih8F3rjeJzK0ANgtYKv5lCpkGVoZAeKitKDaD/g==

"@react-dnd/shallowequal@3.0.1":
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/@react-dnd/shallowequal/-/shallowequal-3.0.1.tgz#8056fe046a8d10a275e321ec0557ae652d7a4d06"
  integrity sha512-XjDVbs3ZU16CO1h5Q3Ew2RPJqmZBDE/EVf1LYp6ePEffs3V/MX9ZbL5bJr8qiK5SbGmUMuDoaFgyKacYz8prRA==

"@remix-run/router@1.0.5":
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/@remix-run/router/-/router-1.0.5.tgz#d5c65626add4c3c185a89aa5bd38b1e42daec075"
  integrity sha512-my0Mycd+jruq/1lQuO5LBB6WTlL/e8DTCYWp44DfMTDcXz8DcTlgF0ISaLsGewt+ctHN+yA8xMq3q/N7uWJPug==

"@remix-run/router@1.22.0":
  version "1.22.0"
  resolved "https://npm.corp.kuaishou.com/@remix-run/router/-/router-1.22.0.tgz#dd8096cb055c475a4de6b35322b8d3b118c17b43"
  integrity sha512-MBOl8MeOzpK0HQQQshKB7pABXbmyHizdTpqnrIseTbsv0nAepwC2ENZa1aaBExNQcpLoXmWthhak8SABLzvGPw==

"@remote-ui/rpc@^1.4.4":
  version "1.4.5"
  resolved "https://npm.corp.kuaishou.com/@remote-ui/rpc/-/rpc-1.4.5.tgz#20328970c314374d96fdaae1cf93aca4b47fefee"
  integrity sha512-Cr+06niG/vmE4A9YsmaKngRuuVSWKMY42NMwtZfy+gctRWGu6Wj9BWuMJg5CEp+JTkRBPToqT5rqnrg1G/Wvow==

"@rspack/binding-darwin-arm64@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-darwin-arm64/-/binding-darwin-arm64-1.2.8.tgz#2a04ca9821a5d570449c19fc8b5c07970574d467"
  integrity sha512-bDlrlroY3iMlzna/3i1gD6eRmhJW2zRyC3Ov6aR1micshVQ9RteigYZWkjZuQfyC5Z8dCcLUQJVojz+pqp0JXg==

"@rspack/binding-darwin-x64@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-darwin-x64/-/binding-darwin-x64-1.2.8.tgz#59d2ecce08efba92df840f935f64a9c16d501074"
  integrity sha512-0/qOVbMuzZ+WbtDa4TbH46R4vph/W6MHcXbrXDO+vpdTMFDVJ64DnZXT7aqvGcY+7vTCIGm0GT+6ooR4KaIX8A==

"@rspack/binding-linux-arm64-gnu@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-linux-arm64-gnu/-/binding-linux-arm64-gnu-1.2.8.tgz#a18beccc9d9388b4d67dd58e942b53f88d785845"
  integrity sha512-En/SMl45s19iUVb1/ZDFQvFDxIjnlfk7yqV3drMWWAL5HSgksNejaTIFTO52aoohIBbmwuk5wSGcbU0G0IFiPg==

"@rspack/binding-linux-arm64-musl@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-linux-arm64-musl/-/binding-linux-arm64-musl-1.2.8.tgz#276d6673b66b4aa13cfa1f5969898c45b7b6f6fd"
  integrity sha512-N1oZsXfJ9VLLcK7p1PS65cxLYQCZ7iqHW2OP6Ew2+hlz/d1hzngxgzrtZMCXFOHXDvTzVu5ff6jGS2v7+zv2tA==

"@rspack/binding-linux-x64-gnu@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-linux-x64-gnu/-/binding-linux-x64-gnu-1.2.8.tgz#26ae969a2d1a9f408b0d89a5f56050f12044c2cf"
  integrity sha512-BdPaepoLKuaVwip4QK/nGqNi1xpbCWSxiycPbKRrGqKgt/QGihxxFgiqr4EpWQVIJNIMy4nCsg4arO0+H1KWGQ==

"@rspack/binding-linux-x64-musl@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-linux-x64-musl/-/binding-linux-x64-musl-1.2.8.tgz#3d7f4fe79219eff27b8f42814841c9e41435a074"
  integrity sha512-GFv0Bod268OcXIcjeLoPlK0oz8rClEIxIRFkz+ejhbvfCwRJ+Fd+EKaaKQTBfZQujPqc0h2GctIF25nN5pFTmA==

"@rspack/binding-win32-arm64-msvc@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-win32-arm64-msvc/-/binding-win32-arm64-msvc-1.2.8.tgz#5526de5b3f2fac2887b31c37f04139fc70259445"
  integrity sha512-aEU+uJdbvJJGrzzAsjbjrPeNbG/bcG8JoXK2kSsUB+/sWHTIkHX0AQ3oX3aV/lcLKgZWrUxLAfLoCXEnIHMEyQ==

"@rspack/binding-win32-ia32-msvc@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-win32-ia32-msvc/-/binding-win32-ia32-msvc-1.2.8.tgz#7e6b4a7f9d41bb36033f613077c3b64cb33e1666"
  integrity sha512-GHYzNOSoiLyG9elLTmMqADJMQzjll+co4irp5AgZ+KHG9EVq0qEHxDqDIJxZnUA15U8JDvCgo6YAo3T0BFEL0Q==

"@rspack/binding-win32-x64-msvc@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding-win32-x64-msvc/-/binding-win32-x64-msvc-1.2.8.tgz#1aca451784c0a95a49f3b79cb55a5d8d8ede771d"
  integrity sha512-EigKLhKLH1kfv1e/ZgXuSKlIjkbyneJtiLbNDz7EeEVFGV1XMM6bsCea1sb2WOxsPYiOX4Q5JmR1j1KGrZS/LA==

"@rspack/binding@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/binding/-/binding-1.2.8.tgz#60b2e740fcce292ceab0136744fc72b583342735"
  integrity sha512-T3FMB3N9P1AbSAryfkSRJkPtmeSYs/Gj9zUZoPz1ckPEIcWZmpUOQbJylldjbw5waxtCL1haHNbi0pcSvxiaJw==
  optionalDependencies:
    "@rspack/binding-darwin-arm64" "1.2.8"
    "@rspack/binding-darwin-x64" "1.2.8"
    "@rspack/binding-linux-arm64-gnu" "1.2.8"
    "@rspack/binding-linux-arm64-musl" "1.2.8"
    "@rspack/binding-linux-x64-gnu" "1.2.8"
    "@rspack/binding-linux-x64-musl" "1.2.8"
    "@rspack/binding-win32-arm64-msvc" "1.2.8"
    "@rspack/binding-win32-ia32-msvc" "1.2.8"
    "@rspack/binding-win32-x64-msvc" "1.2.8"

"@rspack/core@1.2.8":
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/@rspack/core/-/core-1.2.8.tgz#ea9aaa42393794459528ab32e0712b6f587d0e98"
  integrity sha512-ppj3uQQtkhgrYDLrUqb33YbpNEZCpAudpfVuOHGsvUrAnu1PijbfJJymoA5ZvUhM+HNMvPI5D1ie97TXyb0UVg==
  dependencies:
    "@module-federation/runtime-tools" "0.8.4"
    "@rspack/binding" "1.2.8"
    "@rspack/lite-tapable" "1.0.1"
    caniuse-lite "^1.0.30001702"

"@rspack/lite-tapable@1.0.1":
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/@rspack/lite-tapable/-/lite-tapable-1.0.1.tgz#d4540a5d28bd6177164bc0ba0bee4bdec0458591"
  integrity sha512-VynGOEsVw2s8TAlLf/uESfrgfrq2+rcXB1muPJYBWbsm1Oa6r5qVQhjA5ggM6z/coYPrsVMgovl3Ff7Q7OCp1w==

"@rspack/plugin-react-refresh@^1.0.1":
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/@rspack/plugin-react-refresh/-/plugin-react-refresh-1.2.0.tgz#730165e5ca0c02c523705ccd422f23d105b4bcd4"
  integrity sha512-DTsbtggCfsiXE5QQtYMS8rKfEF8GIjwPDbgIT6Kg8BlAjpJY4jT5IisyhfIi7YOT3d5RIvu60iFB6Kr9sSMsnA==
  dependencies:
    error-stack-parser "^2.1.4"
    html-entities "^2.6.0"

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://npm.corp.kuaishou.com/@sinclair/typebox/-/typebox-0.27.8.tgz#6667fac16c436b5434a387a34dedb013198f6e6e"
  integrity sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==

"@socket.io/component-emitter@~3.1.0":
  version "3.1.2"
  resolved "https://npm.corp.kuaishou.com/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz#821f8442f4175d8f0467b9daf26e3a18e2d02af2"
  integrity sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==

"@swc/helpers@0.5.15":
  version "0.5.15"
  resolved "https://npm.corp.kuaishou.com/@swc/helpers/-/helpers-0.5.15.tgz#79efab344c5819ecf83a43f3f9f811fc84b516d7"
  integrity sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==
  dependencies:
    tslib "^2.8.0"

"@tybys/wasm-util@^0.9.0":
  version "0.9.0"
  resolved "https://npm.corp.kuaishou.com/@tybys/wasm-util/-/wasm-util-0.9.0.tgz#3e75eb00604c8d6db470bf18c37b7d984a0e3355"
  integrity sha512-6+7nlbMVX/PVDCwaIQ8nTOPveOcFLSt8GcXdx8hD0bt39uWxYT88uXzqTd4fTvqta7oeUJqudepapKNt2DYJFw==
  dependencies:
    tslib "^2.4.0"

"@types/d3-array@^3.2.1":
  version "3.2.1"
  resolved "https://npm.corp.kuaishou.com/@types/d3-array/-/d3-array-3.2.1.tgz#1f6658e3d2006c4fceac53fde464166859f8b8c5"
  integrity sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==

"@types/d3-color@*", "@types/d3-color@^3.1.3":
  version "3.1.3"
  resolved "https://npm.corp.kuaishou.com/@types/d3-color/-/d3-color-3.1.3.tgz#368c961a18de721da8200e80bf3943fb53136af2"
  integrity sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==

"@types/d3-dispatch@^3.0.6":
  version "3.0.6"
  resolved "https://npm.corp.kuaishou.com/@types/d3-dispatch/-/d3-dispatch-3.0.6.tgz#096efdf55eb97480e3f5621ff9a8da552f0961e7"
  integrity sha512-4fvZhzMeeuBJYZXRXrRIQnvUYfyXwYmLsdiN7XXmVNQKKw1cM8a5WdID0g1hVFZDqT9ZqZEY5pD44p24VS7iZQ==

"@types/d3-dsv@*", "@types/d3-dsv@^3.0.7":
  version "3.0.7"
  resolved "https://npm.corp.kuaishou.com/@types/d3-dsv/-/d3-dsv-3.0.7.tgz#0a351f996dc99b37f4fa58b492c2d1c04e3dac17"
  integrity sha512-n6QBF9/+XASqcKK6waudgL0pf/S5XHPPI8APyMLLUHd8NqouBGLsU8MgtO7NINGtPBtk9Kko/W4ea0oAspwh9g==

"@types/d3-ease@^3.0.2":
  version "3.0.2"
  resolved "https://npm.corp.kuaishou.com/@types/d3-ease/-/d3-ease-3.0.2.tgz#e28db1bfbfa617076f7770dd1d9a48eaa3b6c51b"
  integrity sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==

"@types/d3-fetch@^3.0.7":
  version "3.0.7"
  resolved "https://npm.corp.kuaishou.com/@types/d3-fetch/-/d3-fetch-3.0.7.tgz#c04a2b4f23181aa376f30af0283dbc7b3b569980"
  integrity sha512-fTAfNmxSb9SOWNB9IoG5c8Hg6R+AzUHDRlsXsDZsNp6sxAEOP0tkP3gKkNSO/qmHPoBFTxNrjDprVHDQDvo5aA==
  dependencies:
    "@types/d3-dsv" "*"

"@types/d3-force@^3.0.10":
  version "3.0.10"
  resolved "https://npm.corp.kuaishou.com/@types/d3-force/-/d3-force-3.0.10.tgz#6dc8fc6e1f35704f3b057090beeeb7ac674bff1a"
  integrity sha512-ZYeSaCF3p73RdOKcjj+swRlZfnYpK1EbaDiYICEEp5Q6sUiqFaFQ9qgoshp5CzIyyb/yD09kD9o2zEltCexlgw==

"@types/d3-format@^3.0.4":
  version "3.0.4"
  resolved "https://npm.corp.kuaishou.com/@types/d3-format/-/d3-format-3.0.4.tgz#b1e4465644ddb3fdf3a263febb240a6cd616de90"
  integrity sha512-fALi2aI6shfg7vM5KiR1wNJnZ7r6UuggVqtDA+xiEdPZQwy/trcQaHnwShLuLdta2rTymCNpxYTiMZX/e09F4g==

"@types/d3-geo@^3.1.0":
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/@types/d3-geo/-/d3-geo-3.1.0.tgz#b9e56a079449174f0a2c8684a9a4df3f60522440"
  integrity sha512-856sckF0oP/diXtS4jNsiQw/UuK5fQG8l/a9VVLeSouf1/PPbBE1i1W852zVwKwYCBkFJJB7nCFTbk6UMEXBOQ==
  dependencies:
    "@types/geojson" "*"

"@types/d3-hierarchy@^3.1.7":
  version "3.1.7"
  resolved "https://npm.corp.kuaishou.com/@types/d3-hierarchy/-/d3-hierarchy-3.1.7.tgz#6023fb3b2d463229f2d680f9ac4b47466f71f17b"
  integrity sha512-tJFtNoYBtRtkNysX1Xq4sxtjK8YgoWUNpIiUee0/jHGRwqvzYxkq0hGVbbOGSz+JgFxxRu4K8nb3YpG3CMARtg==

"@types/d3-interpolate@^3.0.4":
  version "3.0.4"
  resolved "https://npm.corp.kuaishou.com/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz#412b90e84870285f2ff8a846c6eb60344f12a41c"
  integrity sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*", "@types/d3-path@^3.1.0":
  version "3.1.1"
  resolved "https://npm.corp.kuaishou.com/@types/d3-path/-/d3-path-3.1.1.tgz#f632b380c3aca1dba8e34aa049bcd6a4af23df8a"
  integrity sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==

"@types/d3-quadtree@^3.0.6":
  version "3.0.6"
  resolved "https://npm.corp.kuaishou.com/@types/d3-quadtree/-/d3-quadtree-3.0.6.tgz#d4740b0fe35b1c58b66e1488f4e7ed02952f570f"
  integrity sha512-oUzyO1/Zm6rsxKRHA1vH0NEDG58HrT5icx/azi9MF1TWdtttWl0UIUsjEQBBh+SIkrpd21ZjEv7ptxWys1ncsg==

"@types/d3-random@^3.0.3":
  version "3.0.3"
  resolved "https://npm.corp.kuaishou.com/@types/d3-random/-/d3-random-3.0.3.tgz#ed995c71ecb15e0cd31e22d9d5d23942e3300cfb"
  integrity sha512-Imagg1vJ3y76Y2ea0871wpabqp613+8/r0mCLEBfdtqC7xMSfj9idOnmBYyMoULfHePJyxMAw3nWhJxzc+LFwQ==

"@types/d3-scale-chromatic@^3.1.0":
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/@types/d3-scale-chromatic/-/d3-scale-chromatic-3.1.0.tgz#dc6d4f9a98376f18ea50bad6c39537f1b5463c39"
  integrity sha512-iWMJgwkK7yTRmWqRB5plb1kadXyQ5Sj8V/zYlFGMUBbIPKQScw+Dku9cAAMgJG+z5GYDoMjWGLVOvjghDEFnKQ==

"@types/d3-scale@^4.0.9":
  version "4.0.9"
  resolved "https://npm.corp.kuaishou.com/@types/d3-scale/-/d3-scale-4.0.9.tgz#57a2f707242e6fe1de81ad7bfcccaaf606179afb"
  integrity sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==
  dependencies:
    "@types/d3-time" "*"

"@types/d3-shape@^3.1.7":
  version "3.1.7"
  resolved "https://npm.corp.kuaishou.com/@types/d3-shape/-/d3-shape-3.1.7.tgz#2b7b423dc2dfe69c8c93596e673e37443348c555"
  integrity sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.4":
  version "3.0.4"
  resolved "https://npm.corp.kuaishou.com/@types/d3-time/-/d3-time-3.0.4.tgz#8472feecd639691450dd8000eb33edd444e1323f"
  integrity sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==

"@types/d3-timer@^3.0.2":
  version "3.0.2"
  resolved "https://npm.corp.kuaishou.com/@types/d3-timer/-/d3-timer-3.0.2.tgz#70bbda77dc23aa727413e22e214afa3f0e852f70"
  integrity sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==

"@types/gensync@^1.0.0":
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/@types/gensync/-/gensync-1.0.4.tgz#7122d8f0cd3bf437f9725cc95b180197190cf50b"
  integrity sha512-C3YYeRQWp2fmq9OryX+FoDy8nXS6scQ7dPptD8LnFDAUNcKWJjXQKDNJD3HVm+kOUsXhTOkpi69vI4EuAr95bA==

"@types/geojson@*":
  version "7946.0.16"
  resolved "https://npm.corp.kuaishou.com/@types/geojson/-/geojson-7946.0.16.tgz#8ebe53d69efada7044454e3305c19017d97ced2a"
  integrity sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==

"@types/history@^4.7.11":
  version "4.7.11"
  resolved "https://npm.corp.kuaishou.com/@types/history/-/history-4.7.11.tgz#56588b17ae8f50c53983a524fc3cc47437969d64"
  integrity sha512-qjDJRrmvBMiTx+jyLxvLfJU7UznFuokDv4f3WRuriHKERccVpFU+8XMQUAbDzoiJCsmexxRExQeMwwCdamSKDA==

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
  version "2.0.6"
  resolved "https://npm.corp.kuaishou.com/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz#7739c232a1fee9b4d3ce8985f314c0c6d33549d7"
  integrity sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://npm.corp.kuaishou.com/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz#53047614ae72e19fc0401d872de3ae2b4ce350bf"
  integrity sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://npm.corp.kuaishou.com/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz#0f03e3d2f670fbdac586e34b433783070cc16f54"
  integrity sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/js-cookie@^2.2.6", "@types/js-cookie@^2.x.x":
  version "2.2.7"
  resolved "https://npm.corp.kuaishou.com/@types/js-cookie/-/js-cookie-2.2.7.tgz#226a9e31680835a6188e887f3988e60c04d3f6a3"
  integrity sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA==

"@types/json-stringify-safe@^5.0.0":
  version "5.0.3"
  resolved "https://npm.corp.kuaishou.com/@types/json-stringify-safe/-/json-stringify-safe-5.0.3.tgz#7eea481de6f248249c1452b5e056ca55df537f1e"
  integrity sha512-oNOjRxLfPeYbBSQ60maucaFNqbslVOPU4WWs5t/sHvAh6tyo/CThXSG+E24tEzkgh/fzvxyDrYdOJufgeNy1sQ==

"@types/lodash-es@^4.17.9":
  version "4.17.12"
  resolved "https://npm.corp.kuaishou.com/@types/lodash-es/-/lodash-es-4.17.12.tgz#65f6d1e5f80539aa7cfbfc962de5def0cf4f341b"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash.clonedeep@^4.5.6":
  version "4.5.9"
  resolved "https://npm.corp.kuaishou.com/@types/lodash.clonedeep/-/lodash.clonedeep-4.5.9.tgz#ea48276c7cc18d080e00bb56cf965bcceb3f0fc1"
  integrity sha512-19429mWC+FyaAhOLzsS8kZUsI+/GmBAQ0HFiCPsKGU+7pBXOQWhyrY6xNNDwUSX8SMZMJvuFVMF9O5dQOlQK9Q==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@4.14", "@types/lodash@^4.14.195":
  version "4.14.202"
  resolved "https://npm.corp.kuaishou.com/@types/lodash/-/lodash-4.14.202.tgz#f09dbd2fb082d507178b2f2a5c7e74bd72ff98f8"
  integrity sha512-OvlIYQK9tNneDlS0VN54LLd5uiPCBOp7gS5Z0f1mjoJYBrtStzgmJBxONW3U6OZqdtNzZPmn9BS/7WI7BFFcFQ==

"@types/node@*":
  version "22.14.0"
  resolved "https://npm.corp.kuaishou.com/@types/node/-/node-22.14.0.tgz#d3bfa3936fef0dbacd79ea3eb17d521c628bb47e"
  integrity sha512-Kmpl+z84ILoG+3T/zQFyAJsU6EPTmOCj8/2+83fSN6djd6I4o7uOuGIH6vq3PrjY5BGitSbFuMN18j3iknubbA==
  dependencies:
    undici-types "~6.21.0"

"@types/node@^17.0.34":
  version "17.0.45"
  resolved "https://npm.corp.kuaishou.com/@types/node/-/node-17.0.45.tgz#2c0fafd78705e7a18b7906b5201a522719dc5190"
  integrity sha512-w+tIMs3rq2afQdsPJlODhoUEKzFP1ayaoyl1CcnwtIlsVe7K7bA1NGm4s3PraqTLlXnbIN84zuBlxBWo1u9BLw==

"@types/prop-types@*":
  version "15.7.14"
  resolved "https://npm.corp.kuaishou.com/@types/prop-types/-/prop-types-15.7.14.tgz#1433419d73b2a7ebfc6918dcefd2ec0d5cd698f2"
  integrity sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ==

"@types/react-dom@^17.0.11":
  version "17.0.26"
  resolved "https://npm.corp.kuaishou.com/@types/react-dom/-/react-dom-17.0.26.tgz#fa7891ba70fd39ddbaa7e85b6ff9175bb546bc1b"
  integrity sha512-Z+2VcYXJwOqQ79HreLU/1fyQ88eXSSFh6I3JdrEHQIfYSI0kCQpTGvOrbE6jFGGYXKsHuwY9tBa/w5Uo6KzrEg==

"@types/react-router-config@^5.0.6":
  version "5.0.11"
  resolved "https://npm.corp.kuaishou.com/@types/react-router-config/-/react-router-config-5.0.11.tgz#2761a23acc7905a66a94419ee40294a65aaa483a"
  integrity sha512-WmSAg7WgqW7m4x8Mt4N6ZyKz0BubSj/2tVUMsAHp+Yd2AMwcSbeFq9WympT19p5heCFmF97R9eD5uUR/t4HEqw==
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"
    "@types/react-router" "^5.1.0"

"@types/react-router-dom@^5.3.3":
  version "5.3.3"
  resolved "https://npm.corp.kuaishou.com/@types/react-router-dom/-/react-router-dom-5.3.3.tgz#e9d6b4a66fcdbd651a5f106c2656a30088cc1e83"
  integrity sha512-kpqnYK4wcdm5UaWI3fLcELopqLrHgLqNsdpHauzlQktfkHL3npOSwtj1Uz9oKBAzs7lFtVkV8j83voAz2D8fhw==
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router@*", "@types/react-router@^5.1.0":
  version "5.1.20"
  resolved "https://npm.corp.kuaishou.com/@types/react-router/-/react-router-5.1.20.tgz#88eccaa122a82405ef3efbcaaa5dcdd9f021387c"
  integrity sha512-jGjmu/ZqS7FjSH6owMcD5qpq19+1RS9DeVRqfl1FeBMxTDQAGwlMWOcs52NDoXaNKyG3d1cYQFMs9rCrb88o9Q==
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"

"@types/react@*", "@types/react@17.0.80":
  version "17.0.80"
  resolved "https://npm.corp.kuaishou.com/@types/react/-/react-17.0.80.tgz#a5dfc351d6a41257eb592d73d3a85d3b7dbcbb41"
  integrity sha512-LrgHIu2lEtIo8M7d1FcI3BdwXWoRQwMoXOZ7+dPTW0lYREjmlHl3P0U1VD0i/9tppOuv8/sam7sOjx34TxSFbA==
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "^0.16"
    csstype "^3.0.2"

"@types/scheduler@^0.16":
  version "0.16.8"
  resolved "https://npm.corp.kuaishou.com/@types/scheduler/-/scheduler-0.16.8.tgz#ce5ace04cfeabe7ef87c0091e50752e36707deff"
  integrity sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==

"@types/stylis@4.2.5":
  version "4.2.5"
  resolved "https://npm.corp.kuaishou.com/@types/stylis/-/stylis-4.2.5.tgz#1daa6456f40959d06157698a653a9ab0a70281df"
  integrity sha512-1Xve+NMN7FWjY14vLoY5tL3BVEQ/n42YLwaqJIPYhotZ9uBHt87VceMwWQpzmdEt2TNXIorIFG+YeCUUW7RInw==

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://npm.corp.kuaishou.com/@types/yargs-parser/-/yargs-parser-21.0.3.tgz#815e30b786d2e8f0dcd85fd5bcf5e1a04d008f15"
  integrity sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==

"@types/yargs@^17.0.8":
  version "17.0.33"
  resolved "https://npm.corp.kuaishou.com/@types/yargs/-/yargs-17.0.33.tgz#8c32303da83eec050a84b3c7ae7b9f922d13e32d"
  integrity sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==
  dependencies:
    "@types/yargs-parser" "*"

"@umijs/es-module-parser-darwin-arm64@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-darwin-arm64/-/es-module-parser-darwin-arm64-0.0.7.tgz#7278f3487c586a3ee63bbf45f8504490bef2ebe0"
  integrity sha512-1QeNupekuVYVvL4UHyCRq4ISP2PNk4rDd9UOPONW+KpqTyP9p7RfgGpwB0VLPaFSu2ADtm0XZyIaYEGPY6zuDw==

"@umijs/es-module-parser-darwin-x64@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-darwin-x64/-/es-module-parser-darwin-x64-0.0.7.tgz#e6e154ad19909d817ce36a65b3bcb1a4d99168f3"
  integrity sha512-FBFmfigmToPc9qBCW7wHiTYpqnLdPbAvoMGOydzAu2NspdPEF7TfILcr8vCPNbNe3vCobS+T/YM1dP+SagERlA==

"@umijs/es-module-parser-linux-arm-gnueabihf@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-linux-arm-gnueabihf/-/es-module-parser-linux-arm-gnueabihf-0.0.7.tgz#6ab6b28f5abbb97b84cb4c9665279cd0dc67f7fe"
  integrity sha512-AXfmg3htkadLGsXUyiyrTig4omGCWIN4l+HS7Qapqv0wlfFYSpC0KPemjyBQgzXO70tDcT+1FNhGjIy+yr2pIQ==

"@umijs/es-module-parser-linux-arm64-gnu@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-linux-arm64-gnu/-/es-module-parser-linux-arm64-gnu-0.0.7.tgz#a3c3bac9d6718a362f4612b16826c4daaff24a6a"
  integrity sha512-2wSdChFc39fPJwvS8tRq+jx8qNlIwrjRk1hb3N5o0rJR+rqt+ceAyNPbYwpNBmUHW7xtmDQvJUeinvr7hIBP+w==

"@umijs/es-module-parser-linux-arm64-musl@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-linux-arm64-musl/-/es-module-parser-linux-arm64-musl-0.0.7.tgz#0c9ea0d46e7e14eafb6e93b940c9f3887025ee5a"
  integrity sha512-cqQffARWkmQ3n1RYNKZR3aD6X8YaP6u1maASjDgPQOpZMAlv/OSDrM/7iGujWTs0PD0haockNG9/DcP6lgPHMw==

"@umijs/es-module-parser-linux-x64-gnu@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-linux-x64-gnu/-/es-module-parser-linux-x64-gnu-0.0.7.tgz#07c35db7eba4ff7b6f34ce7539fe38875ae27129"
  integrity sha512-PHrKHtT665Za0Ydjch4ACrNpRU+WIIden12YyF1CtMdhuLDSoU6UfdhF3NoDbgEUcXVDX/ftOqmj0SbH3R1uew==

"@umijs/es-module-parser-linux-x64-musl@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-linux-x64-musl/-/es-module-parser-linux-x64-musl-0.0.7.tgz#0954cdde0d3a0c15f22d8712f52f31142b1b577e"
  integrity sha512-cyZvUK5lcECLWzLp/eU1lFlCETcz+LEb+wrdARQSST1dgoIGZsT4cqM1WzYmdZNk3o883tiZizLt58SieEiHBQ==

"@umijs/es-module-parser-win32-arm64-msvc@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-win32-arm64-msvc/-/es-module-parser-win32-arm64-msvc-0.0.7.tgz#a3d07a733843e2b287a8135714fbd51950ae0de5"
  integrity sha512-V7WxnUI88RboSl0RWLNQeKBT7EDW35fW6Tn92zqtoHHxrhAIL9DtDyvC8REP4qTxeZ6Oej/Ax5I6IjsLx3yTOg==

"@umijs/es-module-parser-win32-x64-msvc@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser-win32-x64-msvc/-/es-module-parser-win32-x64-msvc-0.0.7.tgz#0b7dbfd611c1f6e2a067d56a0da69f129d42e408"
  integrity sha512-X3Pqy0l38hg6wMPquPeMHuoHU+Cx+wzyz32SVYCta+RPJQ7n9PjrEBiIuVAw5+GJZjSABN7LVr8u/n0RZT9EQA==

"@umijs/es-module-parser@0.0.7":
  version "0.0.7"
  resolved "https://npm.corp.kuaishou.com/@umijs/es-module-parser/-/es-module-parser-0.0.7.tgz#7f6d7573a1725204dd6f2a67bb883cf20deed8e4"
  integrity sha512-x47CMi/Hw7Nkz3RXTUqlldH/UM+Tcmw2PziV3k+itJqTFJc8oVx3lzdUgCnG+eL3ZtmLPbOEBhPb30V0NytNDQ==
  optionalDependencies:
    "@umijs/es-module-parser-darwin-arm64" "0.0.7"
    "@umijs/es-module-parser-darwin-x64" "0.0.7"
    "@umijs/es-module-parser-linux-arm-gnueabihf" "0.0.7"
    "@umijs/es-module-parser-linux-arm64-gnu" "0.0.7"
    "@umijs/es-module-parser-linux-arm64-musl" "0.0.7"
    "@umijs/es-module-parser-linux-x64-gnu" "0.0.7"
    "@umijs/es-module-parser-linux-x64-musl" "0.0.7"
    "@umijs/es-module-parser-win32-arm64-msvc" "0.0.7"
    "@umijs/es-module-parser-win32-x64-msvc" "0.0.7"

"@umijs/zod2ts@4.3.28":
  version "4.3.28"
  resolved "https://npm.corp.kuaishou.com/@umijs/zod2ts/-/zod2ts-4.3.28.tgz#2641a289bb499e14ddeb1503110e9bdb6f445d64"
  integrity sha512-LNs6YjtFYtJD6yvPJ3+ZMoM8WapYm9dghTze5XJxBbnMBGWBRrhFb4pLjGgicD5bJ/Pq2G3El+7v1EZPOuMiRg==

"@xn-sakina/rml-darwin-arm64@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-darwin-arm64/-/rml-darwin-arm64-2.5.0.tgz#9c17f032f0629a8d743775f53c0e8a86a8e27261"
  integrity sha512-JC1ODK8KdsrtadS/efvIXVAknllyLjt7Aov67TymUwD9sqZMQUivziPmsKrdh8FO2a7vxSZoSzuokWVEN0xgdw==

"@xn-sakina/rml-darwin-x64@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-darwin-x64/-/rml-darwin-x64-2.5.0.tgz#9f8d925108a187121e623797de3e461c2adfabfe"
  integrity sha512-OgZ8MTSD9PPwAhbeXV0s3u1QhsUQloBgrIk9OWNqwgtvqJLZhlASmFXGRIntbZnlYgySyV9rhxJfXZkCrIL8Cg==

"@xn-sakina/rml-linux-arm-gnueabihf@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-linux-arm-gnueabihf/-/rml-linux-arm-gnueabihf-2.5.0.tgz#1f4c40f13434fec746c02284d0213c303ab99791"
  integrity sha512-/ns7pt0KRWUuhjdCoQuGBKVH0O8bXyc3UKcvbsHCxCq3+6XwcErijt0n6DGkBblo8iWKV03rLOquRGjcgHxVtg==

"@xn-sakina/rml-linux-arm64-gnu@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-linux-arm64-gnu/-/rml-linux-arm64-gnu-2.5.0.tgz#a736cafd645e9459ff7f72e0285a7e612cff3272"
  integrity sha512-BwVelPhv554HaF2m/ztoAi47WXehBT+jlNguH2bFBoQ/ZCFJzvvygQ4DgSBN5GnoWjdTB3K6gUB2UHnVPuaRHg==

"@xn-sakina/rml-linux-arm64-musl@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-linux-arm64-musl/-/rml-linux-arm64-musl-2.5.0.tgz#3c4e2be7505d58810ac7efdd91af27be433712c8"
  integrity sha512-4U+djV+229GpZP5aXHq2oNSeyrg2lXGqe3gFVGy1Ag3uw1+Q2wLrHB1D6D4Vhx2pporH+/nJ92Tldz3lnhP1vw==

"@xn-sakina/rml-linux-x64-gnu@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-linux-x64-gnu/-/rml-linux-x64-gnu-2.5.0.tgz#455bae9931f70b91bd7bde05b9d525e28975286c"
  integrity sha512-9I09DOuAaQX8AfsLJaqy7kC20LHRSaZgVj1q6oHa5Z2ChE7GAUE9KQpel2RRKS/gQzwArsACMsdkQ8KrOUMS2A==

"@xn-sakina/rml-linux-x64-musl@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-linux-x64-musl/-/rml-linux-x64-musl-2.5.0.tgz#6de3e952906943be2ed3380cf91f34eab443bb12"
  integrity sha512-QoVii5PTvTrWcKZEUp1lMsr3WlxmN84RgQqr7kjngIhWEqWiWWUUQ3se7y848+pAVGvJsqw44Cy0MdfyJyuexQ==

"@xn-sakina/rml-win32-arm64-msvc@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-win32-arm64-msvc/-/rml-win32-arm64-msvc-2.5.0.tgz#672d181c1ee1b332d47c3969ca78969a6c1ef3f1"
  integrity sha512-Y9+WXxLSuW23/7d36i5uJeOJkxKVPpLTp7w6lpPXOTP19bIfDKGhhNEQ3+r1eBU4cOf9xsScGA5jcaMGdFtcjA==

"@xn-sakina/rml-win32-x64-msvc@2.5.0":
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/@xn-sakina/rml-win32-x64-msvc/-/rml-win32-x64-msvc-2.5.0.tgz#5c097143b764f35f03aa80cc5998dac0fdf200c7"
  integrity sha512-MCdLOANVS4gVrkRaZ9UIC2ZU7yGVNUXuxFhrnp6SlNqa7u5Dy1TPJ2SnCXjddt1GzBW/1r+1NckzmPuSVkExvg==

"@xobotyi/scrollbar-width@^1.9.5":
  version "1.9.5"
  resolved "https://npm.corp.kuaishou.com/@xobotyi/scrollbar-width/-/scrollbar-width-1.9.5.tgz#80224a6919272f405b87913ca13b92929bdf3c4d"
  integrity sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ==

"@xstate/fsm@^2.0.0":
  version "2.1.0"
  resolved "https://npm.corp.kuaishou.com/@xstate/fsm/-/fsm-2.1.0.tgz#269f0bc411f3eac0dd04e55d51cbd07411a04f9a"
  integrity sha512-oJlc0iD0qZvAM7If/KlyJyqUt7wVI8ocpsnlWzAPl97evguPbd+oJbRM9R4A1vYJffYH96+Bx44nLDE6qS8jQg==

abortcontroller-polyfill@^1.7.3:
  version "1.7.8"
  resolved "https://npm.corp.kuaishou.com/abortcontroller-polyfill/-/abortcontroller-polyfill-1.7.8.tgz#fe8d4370403f02e2aa37e3d2b0b178bae9d83f49"
  integrity sha512-9f1iZ2uWh92VcrU9Y8x+LdM4DLj75VE0MJB8zuF1iUnroEptStw+DQ8EQPMUdfe5k+PkB1uUfDQfWbhstH8LrQ==

ahooks-v3-count@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/ahooks-v3-count/-/ahooks-v3-count-1.0.0.tgz#ddeb392e009ad6e748905b3cbf63a9fd8262ca80"
  integrity sha512-V7uUvAwnimu6eh/PED4mCDjE7tokeZQLKlxg9lCTMPhN+NjsSbtdacByVlR1oluXQzD3MOw55wylDmQo4+S9ZQ==

ahooks@3.7.2:
  version "3.7.2"
  resolved "https://npm.corp.kuaishou.com/ahooks/-/ahooks-3.7.2.tgz#0afa42625e77ae1cc4b60b19c45cf12a8cf29b56"
  integrity sha512-nJPsQJcmJnGaNXiqgZdfO7UMs+o926LQg6VyDYt2vzKhXU8Ze/U87NsA/FeIvlIZB0rQr/j7uotFb1bGPp627A==
  dependencies:
    "@types/js-cookie" "^2.x.x"
    ahooks-v3-count "^1.0.0"
    dayjs "^1.9.1"
    intersection-observer "^0.12.0"
    js-cookie "^2.x.x"
    lodash "^4.17.21"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"

ahooks@^3.7.10, ahooks@^3.7.7, ahooks@^3.8.0:
  version "3.8.4"
  resolved "https://npm.corp.kuaishou.com/ahooks/-/ahooks-3.8.4.tgz#ee2a22d52b6ee57743a1f6ab51c91a7c36bcd7c6"
  integrity sha512-39wDEw2ZHvypaT14EpMMk4AzosHWt0z9bulY0BeDsvc9PqJEV+Kjh/4TZfftSsotBMq52iYIOFPd3PR56e0ZJg==
  dependencies:
    "@babel/runtime" "^7.21.0"
    dayjs "^1.9.1"
    intersection-observer "^0.12.0"
    js-cookie "^3.0.5"
    lodash "^4.17.21"
    react-fast-compare "^3.2.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"
    tslib "^2.4.1"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "https://npm.corp.kuaishou.com/align-text/-/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  integrity sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg==
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/amdefine/-/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"
  integrity sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg==

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://npm.corp.kuaishou.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://npm.corp.kuaishou.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://npm.corp.kuaishou.com/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

antd@^5.24.0:
  version "5.24.0"
  resolved "https://npm.corp.kuaishou.com/antd/-/antd-5.24.0.tgz#55e05c47b1baf4a51947c6cfca9ba8620ec68950"
  integrity sha512-05PZBIf6ijLHAQskBTW3nwS2t7tQmyLA6Xq8vK2Sk5tsgCsH/UE1cNCDYnKFGRJ7cKYuWJ565JDo9LejbiO42A==
  dependencies:
    "@ant-design/colors" "^7.2.0"
    "@ant-design/cssinjs" "^1.23.0"
    "@ant-design/cssinjs-utils" "^1.1.3"
    "@ant-design/fast-color" "^2.0.6"
    "@ant-design/icons" "^5.6.1"
    "@ant-design/react-slick" "~1.1.2"
    "@babel/runtime" "^7.26.0"
    "@rc-component/color-picker" "~2.0.1"
    "@rc-component/mutate-observer" "^1.1.0"
    "@rc-component/qrcode" "~1.0.0"
    "@rc-component/tour" "~1.15.1"
    "@rc-component/trigger" "^2.2.6"
    classnames "^2.5.1"
    copy-to-clipboard "^3.3.3"
    dayjs "^1.11.11"
    rc-cascader "~3.33.0"
    rc-checkbox "~3.5.0"
    rc-collapse "~3.9.0"
    rc-dialog "~9.6.0"
    rc-drawer "~7.2.0"
    rc-dropdown "~4.2.1"
    rc-field-form "~2.7.0"
    rc-image "~7.11.0"
    rc-input "~1.7.2"
    rc-input-number "~9.4.0"
    rc-mentions "~2.19.1"
    rc-menu "~9.16.0"
    rc-motion "^2.9.5"
    rc-notification "~5.6.3"
    rc-pagination "~5.1.0"
    rc-picker "~4.11.0"
    rc-progress "~4.0.0"
    rc-rate "~2.13.1"
    rc-resize-observer "^1.4.3"
    rc-segmented "~2.7.0"
    rc-select "~14.16.6"
    rc-slider "~11.1.8"
    rc-steps "~6.0.1"
    rc-switch "~4.1.0"
    rc-table "~7.50.2"
    rc-tabs "~15.5.1"
    rc-textarea "~1.9.0"
    rc-tooltip "~6.4.0"
    rc-tree "~5.13.0"
    rc-tree-select "~5.27.0"
    rc-upload "~4.8.1"
    rc-util "^5.44.4"
    scroll-into-view-if-needed "^3.1.0"
    throttle-debounce "^5.0.2"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz#384d12a37295aec3769ab022ad323a18a51ccf8b"
  integrity sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-tree-filter@^2.1.0:
  version "2.1.0"
  resolved "https://npm.corp.kuaishou.com/array-tree-filter/-/array-tree-filter-2.1.0.tgz#873ac00fec83749f255ac8dd083814b4f6329190"
  integrity sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==

array.prototype.map@^1.0.5:
  version "1.0.8"
  resolved "https://npm.corp.kuaishou.com/array.prototype.map/-/array.prototype.map-1.0.8.tgz#22f4aae44d3081ce3cd1dd6fd37532e7a3433451"
  integrity sha512-YocPM7bYYu2hXGxWpb5vwZ8cMeudNHYtYBcUDY4Z1GWa53qcnQMWSl25jeBHNzitjl9HW2AWW4ro/S/nftUaOQ==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-array-method-boxes-properly "^1.0.0"
    es-object-atoms "^1.0.0"
    is-string "^1.1.1"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz#9d760d84dbdd06d0cbf92c8849615a1a7ab3183c"
  integrity sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

asap@~2.0.3:
  version "2.0.6"
  resolved "https://npm.corp.kuaishou.com/asap/-/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==

asn1.js@^4.10.1:
  version "4.10.1"
  resolved "https://npm.corp.kuaishou.com/asn1.js/-/asn1.js-4.10.1.tgz#b9c2bf5805f1e64aadeed6df3a2bfafb5a73f5a0"
  integrity sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

assert@^2.0.0:
  version "2.1.0"
  resolved "https://npm.corp.kuaishou.com/assert/-/assert-2.1.0.tgz#6d92a238d05dc02e7427c881fb8be81c8448b2dd"
  integrity sha512-eLHpSK/Y4nhMJ07gDaAzoX/XAKS8PSaojml3M0DM4JpV1LAi5JOJ/p6H/XWrl8L+DzVEvVCW1z3vWAaB9oTsQw==
  dependencies:
    call-bind "^1.0.2"
    is-nan "^1.3.2"
    object-is "^1.1.5"
    object.assign "^4.1.4"
    util "^0.12.5"

async-function@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/async-function/-/async-function-1.0.0.tgz#509c9fca60eaf85034c6829838188e4e4c8ffb2b"
  integrity sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==

async-validator@^4.1.0:
  version "4.2.5"
  resolved "https://npm.corp.kuaishou.com/async-validator/-/async-validator-4.2.5.tgz#c96ea3332a521699d0afaaceed510a54656c6339"
  integrity sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://npm.corp.kuaishou.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atomic-sleep@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/atomic-sleep/-/atomic-sleep-1.0.0.tgz#eb85b77a601fc932cfe432c5acd364a9e2c9075b"
  integrity sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://npm.corp.kuaishou.com/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==
  dependencies:
    possible-typed-array-names "^1.0.0"

axios@^0.27.2:
  version "0.27.2"
  resolved "https://npm.corp.kuaishou.com/axios/-/axios-0.27.2.tgz#207658cc8621606e586c85db4b41a750e756d972"
  integrity sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==
  dependencies:
    follow-redirects "^1.14.9"
    form-data "^4.0.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/balanced-match/-/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://npm.corp.kuaishou.com/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://npm.corp.kuaishou.com/big.js/-/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

blueimp-md5@^2.10.0:
  version "2.19.0"
  resolved "https://npm.corp.kuaishou.com/blueimp-md5/-/blueimp-md5-2.19.0.tgz#b53feea5498dcb53dc6ec4b823adb84b729c4af0"
  integrity sha512-DRQrD6gJyy8FbiE4s+bDoXS9hiW3Vbx5uCdwvcCf3zLHL+Iv7LtGHLpr+GZV8rHG8tK766FGYBwRbu8pELTt+w==

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.11.9:
  version "4.12.1"
  resolved "https://npm.corp.kuaishou.com/bn.js/-/bn.js-4.12.1.tgz#215741fe3c9dba2d7e12c001d0cfdbae43975ba7"
  integrity sha512-k8TVBiPkPJT9uHLdOKfFpqcfprwBFOAAXXozRubr7R7PfIuKvQlzcI4M0pALeqXN09vdaMbUdUj+pass+uULAg==

bn.js@^5.2.1:
  version "5.2.1"
  resolved "https://npm.corp.kuaishou.com/bn.js/-/bn.js-5.2.1.tgz#0bc527a6a0d18d0aa8d5b0538ce4a77dccfa7b70"
  integrity sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://npm.corp.kuaishou.com/brace-expansion/-/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braft-convert@^2.3.0:
  version "2.3.0"
  resolved "https://npm.corp.kuaishou.com/braft-convert/-/braft-convert-2.3.0.tgz#27d5905136c334903d083b7a2352a72045627888"
  integrity sha512-5km+dLHk8iYDv2iEYDrDQ2ld/ZoUx66QLql0qdm5PqZEcNXc8dBHGLORfzeu3iMw1jLeAiHxtdY5+ypuIhczVg==
  dependencies:
    draft-convert "^2.0.0"
    draft-js "^0.10.3"

braft-editor@^2.3.9:
  version "2.3.9"
  resolved "https://npm.corp.kuaishou.com/braft-editor/-/braft-editor-2.3.9.tgz#fd2b8e23ea71191016579a1ed8231d16ad8f5b4a"
  integrity sha512-mqdPk/zI2dhFK8tW/A4Qj/AkkARLh5L/niNw+iif5wFqb6zh15rMlrShgz1nWO/QXyAKr8XtDgxiBbR0zWwtRg==
  dependencies:
    "@babel/runtime" "^7.0.0"
    braft-convert "^2.3.0"
    braft-finder "^0.0.19"
    braft-utils "^3.0.8"
    draft-convert "^2.0.0"
    draft-js "^0.10.3"
    draft-js-multidecorators "^1.0.0"
    draftjs-utils "^0.9.4"
    immutable "~3.7.4"

braft-finder@^0.0.19:
  version "0.0.19"
  resolved "https://npm.corp.kuaishou.com/braft-finder/-/braft-finder-0.0.19.tgz#c324d82526ed3476a93de86cc9b407f4e188bc8d"
  integrity sha512-0kzI6/KbomJJhYX1hpjn4edCKhblyUyWdUrsgBmOrwy0vrj+pPkm69+Uf8Uj6KGAULM6LF0ooC++p7fqUGgFHw==

braft-utils@^3.0.8:
  version "3.0.13"
  resolved "https://npm.corp.kuaishou.com/braft-utils/-/braft-utils-3.0.13.tgz#d67836ec23837bd283ca94e87a447dbbdbb6097b"
  integrity sha512-92YNlc5RW3mNMo0zbWhnqz8PWr21AAPPhnfn3ZUoXM9+wBIuJQe6UyvOas+MEG9UOGFrvTDPbq60P3fdEhyMQQ==

brorand@^1.0.1, brorand@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/brorand/-/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w==

browser-resolve@^2.0.0:
  version "2.0.0"
  resolved "https://npm.corp.kuaishou.com/browser-resolve/-/browser-resolve-2.0.0.tgz#99b7304cb392f8d73dba741bb2d7da28c6d7842b"
  integrity sha512-7sWsQlYL2rGLy2IWm8WL8DCTJvYLc/qlOnsakDac87SOoCd16WLsaAMdCiAqsTNHIe+SXfaqyxyo6THoWqs8WQ==
  dependencies:
    resolve "^1.17.0"

browserify-aes@^1.0.4, browserify-aes@^1.2.0:
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/browserify-aes/-/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/browserify-cipher/-/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/browserify-des/-/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0, browserify-rsa@^4.1.0:
  version "4.1.1"
  resolved "https://npm.corp.kuaishou.com/browserify-rsa/-/browserify-rsa-4.1.1.tgz#06e530907fe2949dc21fc3c2e2302e10b1437238"
  integrity sha512-YBjSAiTqM04ZVei6sXighu679a3SqWORA3qZTEqZImnlkDIFtKc6pNutpjyZ8RJTjQtuYfeetkxM11GwoYXMIQ==
  dependencies:
    bn.js "^5.2.1"
    randombytes "^2.1.0"
    safe-buffer "^5.2.1"

browserify-sign@^4.2.3:
  version "4.2.3"
  resolved "https://npm.corp.kuaishou.com/browserify-sign/-/browserify-sign-4.2.3.tgz#7afe4c01ec7ee59a89a558a4b75bd85ae62d4208"
  integrity sha512-JWCZW6SKhfhjJxO8Tyiiy+XYB7cqd2S5/+WeYHsKdNKFlCBhKbblba1A/HN/90YwtxKc8tCErjffZl++UNmGiw==
  dependencies:
    bn.js "^5.2.1"
    browserify-rsa "^4.1.0"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    elliptic "^6.5.5"
    hash-base "~3.0"
    inherits "^2.0.4"
    parse-asn1 "^5.1.7"
    readable-stream "^2.3.8"
    safe-buffer "^5.2.1"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "https://npm.corp.kuaishou.com/browserify-zlib/-/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==
  dependencies:
    pako "~1.0.5"

browserslist@^4.21.5, browserslist@^4.24.0:
  version "4.24.4"
  resolved "https://npm.corp.kuaishou.com/browserslist/-/browserslist-4.24.4.tgz#c6b2865a3f08bcb860a0e827389003b9fe686e4b"
  integrity sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==
  dependencies:
    caniuse-lite "^1.0.30001688"
    electron-to-chromium "^1.5.73"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.1"

bubblesets-js@^2.3.4:
  version "2.3.4"
  resolved "https://npm.corp.kuaishou.com/bubblesets-js/-/bubblesets-js-2.3.4.tgz#8e1230b29c309e3327a05630fe02df3d96596ab6"
  integrity sha512-DyMjHmpkS2+xcFNtyN00apJYL3ESdp9fTrkDr5+9Qg/GPqFmcWgGsK1akZnttE1XFxJ/VMy4DNNGMGYtmFp1Sg==

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/buffer-xor/-/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ==

buffer@^5.7.1:
  version "5.7.1"
  resolved "https://npm.corp.kuaishou.com/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "https://npm.corp.kuaishou.com/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ==

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.0, call-bind@^1.0.2, call-bind@^1.0.7, call-bind@^1.0.8, call-bind@~1.0.2:
  version "1.0.8"
  resolved "https://npm.corp.kuaishou.com/call-bind/-/call-bind-1.0.8.tgz#0736a9660f537e3388826f440d5ec45f744eaa4c"
  integrity sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/call-bound/-/call-bound-1.0.3.tgz#41cfd032b593e39176a71533ab4f384aa04fd681"
  integrity sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    get-intrinsic "^1.2.6"

call-bound@^1.0.4:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/call-bound/-/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
  integrity sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"
  integrity sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==

camelize@^1.0.0:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/camelize/-/camelize-1.0.1.tgz#89b7e16884056331a35d6b5ad064332c91daa6c3"
  integrity sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ==

caniuse-lite@1.0.30001690:
  version "1.0.30001690"
  resolved "https://npm.corp.kuaishou.com/caniuse-lite/-/caniuse-lite-1.0.30001690.tgz#f2d15e3aaf8e18f76b2b8c1481abde063b8104c8"
  integrity sha512-5ExiE3qQN6oF8Clf8ifIDcMRCRE/dMGcETG/XGMD8/XiXm6HXQgQTh1yZYLXXpSOsEUlJm1Xr7kGULZTuGtP/w==

caniuse-lite@^1.0.30001688:
  version "1.0.30001699"
  resolved "https://npm.corp.kuaishou.com/caniuse-lite/-/caniuse-lite-1.0.30001699.tgz#a102cf330d153bf8c92bfb5be3cd44c0a89c8c12"
  integrity sha512-b+uH5BakXZ9Do9iK+CkDmctUSEqZl+SP056vc5usa0PL+ev5OHw003rZXcnjNDv3L8P5j6rwT6C0BPKSikW08w==

caniuse-lite@^1.0.30001702:
  version "1.0.30001712"
  resolved "https://npm.corp.kuaishou.com/caniuse-lite/-/caniuse-lite-1.0.30001712.tgz#41ee150f12de11b5f57c5889d4f30deb451deedf"
  integrity sha512-MBqPpGYYdQ7/hfKiet9SCI+nmN5/hp4ZzveOJubl5DTAMa5oggjAuoi0Z4onBpKPFI2ePGnQuQIzF3VxDjDJig==

case-anything@2.1.10:
  version "2.1.10"
  resolved "https://npm.corp.kuaishou.com/case-anything/-/case-anything-2.1.10.tgz#d18a6ca968d54ec3421df71e3e190f3bced23410"
  integrity sha512-JczJwVrCP0jPKh05McyVsuOg6AYosrB9XWZKbQzXeDAm2ClE/PJE/BcrrQrVyGYH7Jg8V/LDupmyL4kFlVsVFQ==

center-align@^0.1.1:
  version "0.1.3"
  resolved "https://npm.corp.kuaishou.com/center-align/-/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  integrity sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ==
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@^1.1.1:
  version "1.1.3"
  resolved "https://npm.corp.kuaishou.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^4.0.0:
  version "4.1.2"
  resolved "https://npm.corp.kuaishou.com/chalk/-/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^5.3.0:
  version "5.4.1"
  resolved "https://npm.corp.kuaishou.com/chalk/-/chalk-5.4.1.tgz#1b48bf0963ec158dce2aacf69c093ae2dd2092d8"
  integrity sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://npm.corp.kuaishou.com/ci-info/-/ci-info-3.9.0.tgz#4279a62028a7b1f262f3473fc9605f5e218c59b4"
  integrity sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.6"
  resolved "https://npm.corp.kuaishou.com/cipher-base/-/cipher-base-1.0.6.tgz#8fe672437d01cd6c4561af5334e0cc50ff1955f7"
  integrity sha512-3Ek9H3X6pj5TgenXYtNWdaBon1tgYCaebd+XPg0keyjEbEfkD4KkmAxkQ/i1vYvxdcT5nscLBfq9VJRmCBcFSw==
  dependencies:
    inherits "^2.0.4"
    safe-buffer "^5.2.1"

classnames@2.x, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.5, classnames@^2.2.6, classnames@^2.3.1, classnames@^2.3.2, classnames@^2.5.1:
  version "2.5.1"
  resolved "https://npm.corp.kuaishou.com/classnames/-/classnames-2.5.1.tgz#ba774c614be0f016da105c858e7159eae8e7687b"
  integrity sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==

cliui@^2.1.0:
  version "2.1.0"
  resolved "https://npm.corp.kuaishou.com/cliui/-/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  integrity sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA==
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

clsx@^1.1.1, clsx@^1.2.1:
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/clsx/-/clsx-1.2.1.tgz#0ddc4a20a549b59c93a4116bb26f5294ca17dc12"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

clsx@^2.0.0:
  version "2.1.1"
  resolved "https://npm.corp.kuaishou.com/clsx/-/clsx-2.1.1.tgz#eed397c9fd8bd882bfb18deab7102049a2f32999"
  integrity sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://npm.corp.kuaishou.com/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://npm.corp.kuaishou.com/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-string@^1.5.5:
  version "1.9.1"
  resolved "https://npm.corp.kuaishou.com/color-string/-/color-string-1.9.1.tgz#4467f9146f036f855b764dfb5bf8582bf342c7a4"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://npm.corp.kuaishou.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

comlink@^4.3.1, comlink@^4.4.1:
  version "4.4.2"
  resolved "https://npm.corp.kuaishou.com/comlink/-/comlink-4.4.2.tgz#cbbcd82742fbebc06489c28a183eedc5c60a2bca"
  integrity sha512-OxGdvBmJuNKSCMO4NTl1L47VRp6xn2wG4F/2hYzB6tiCb709otOxtEYCSvK80PtjODfXXZu8ds+Nw5kVCjqd2g==

commander@7:
  version "7.2.0"
  resolved "https://npm.corp.kuaishou.com/commander/-/commander-7.2.0.tgz#a36cb57d0b501ce108e4d20559a150a391d97ab7"
  integrity sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==

commander@^2.20.3:
  version "2.20.3"
  resolved "https://npm.corp.kuaishou.com/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

compute-scroll-into-view@^1.0.20:
  version "1.0.20"
  resolved "https://npm.corp.kuaishou.com/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz#1768b5522d1172754f5d0c9b02de3af6be506a43"
  integrity sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==

compute-scroll-into-view@^3.0.2:
  version "3.1.1"
  resolved "https://npm.corp.kuaishou.com/compute-scroll-into-view/-/compute-scroll-into-view-3.1.1.tgz#02c3386ec531fb6a9881967388e53e8564f3e9aa"
  integrity sha512-VRhuHOLoKYOy4UbilLbUzbYg93XLjv2PncJC50EuTWPA3gaja1UjBsUP/D/9/juV3vQFr6XBEzn9KCAHdUvOHw==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://npm.corp.kuaishou.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

console-browserify@^1.1.0:
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/console-browserify/-/console-browserify-1.2.0.tgz#67063cef57ceb6cf4993a2ab3a55840ae8c49336"
  integrity sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/constants-browserify/-/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ==

contour_plot@^0.0.1:
  version "0.0.1"
  resolved "https://npm.corp.kuaishou.com/contour_plot/-/contour_plot-0.0.1.tgz#475870f032b8e338412aa5fc507880f0bf495c77"
  integrity sha512-Nil2HI76Xux6sVGORvhSS8v66m+/h5CwFkBJDO+U5vWaMdNC0yXNCsGDPbzPhvqOEU5koebhdEvD372LI+IyLw==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://npm.corp.kuaishou.com/convert-source-map/-/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

copy-to-clipboard@^3.2.0, copy-to-clipboard@^3.3.1, copy-to-clipboard@^3.3.3:
  version "3.3.3"
  resolved "https://npm.corp.kuaishou.com/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz#55ac43a1db8ae639a4bd99511c148cdd1b83a1b0"
  integrity sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==
  dependencies:
    toggle-selection "^1.0.6"

core-js-compat@3.28.0:
  version "3.28.0"
  resolved "https://npm.corp.kuaishou.com/core-js-compat/-/core-js-compat-3.28.0.tgz#c08456d854608a7264530a2afa281fadf20ecee6"
  integrity sha512-myzPgE7QodMg4nnd3K1TDoES/nADRStM8Gpz0D6nhkwbmwEnE0ZGJgoWsvQ722FR8D7xS0n0LV556RcEicjTyg==
  dependencies:
    browserslist "^4.21.5"

core-js-pure@3.23.3:
  version "3.23.3"
  resolved "https://npm.corp.kuaishou.com/core-js-pure/-/core-js-pure-3.23.3.tgz#bcd02d3d8ec68ad871ef50d5ccbb248ddb54f401"
  integrity sha512-XpoouuqIj4P+GWtdyV8ZO3/u4KftkeDVMfvp+308eGMhCrA3lVDSmAxO0c6GGOcmgVlaKDrgWVMo49h2ab/TDA==

core-js@3.28.0:
  version "3.28.0"
  resolved "https://npm.corp.kuaishou.com/core-js/-/core-js-3.28.0.tgz#ed8b9e99c273879fdfff0edfc77ee709a5800e4a"
  integrity sha512-GiZn9D4Z/rSYvTeg1ljAIsEqFm0LaN9gVtwDCrKL80zHtS31p9BAjmTxVqTQDMpwlMolJZOFntUG2uwyj7DAqw==

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/core-js/-/core-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"
  integrity sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA==

core-js@^3.6.5:
  version "3.40.0"
  resolved "https://npm.corp.kuaishou.com/core-js/-/core-js-3.40.0.tgz#2773f6b06877d8eda102fc42f828176437062476"
  integrity sha512-7vsMc/Lty6AGnn7uFpYT56QesI5D2Y/UkgKounk87OP9Z2H9Z8kj6jzcSGAxFmUtDOS0ntK6lbQz+Nsa0Jj6mQ==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/core-util-is/-/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

create-ecdh@^4.0.4:
  version "4.0.4"
  resolved "https://npm.corp.kuaishou.com/create-ecdh/-/create-ecdh-4.0.4.tgz#d6e7f4bffa66736085a0762fd3a632684dabcc4e"
  integrity sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.5.3"

create-hash@^1.1.0, create-hash@^1.1.2, create-hash@^1.2.0:
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/create-hash/-/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.4, create-hmac@^1.1.7:
  version "1.1.7"
  resolved "https://npm.corp.kuaishou.com/create-hmac/-/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

create-require@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/create-require/-/create-require-1.1.1.tgz#c1d7e8f1e5f6cfc9ff65f9cd352d37348756c333"
  integrity sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==

crypto-browserify@^3.11.0:
  version "3.12.1"
  resolved "https://npm.corp.kuaishou.com/crypto-browserify/-/crypto-browserify-3.12.1.tgz#bb8921bec9acc81633379aa8f52d69b0b69e0dac"
  integrity sha512-r4ESw/IlusD17lgQi1O20Fa3qNnsckR126TdUuBgAu7GBYSIPvdNyONd3Zrxh0xCwA4+6w/TDArBPsMvhur+KQ==
  dependencies:
    browserify-cipher "^1.0.1"
    browserify-sign "^4.2.3"
    create-ecdh "^4.0.4"
    create-hash "^1.2.0"
    create-hmac "^1.1.7"
    diffie-hellman "^5.0.3"
    hash-base "~3.0.4"
    inherits "^2.0.4"
    pbkdf2 "^3.1.2"
    public-encrypt "^4.0.3"
    randombytes "^2.1.0"
    randomfill "^1.0.4"

crypto-es@^1.2.7:
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/crypto-es/-/crypto-es-1.2.7.tgz#754a6d52319a94fb4eb1f119297f17196b360f88"
  integrity sha512-UUqiVJ2gUuZFmbFsKmud3uuLcNP2+Opt+5ysmljycFCyhA0+T16XJmo1ev/t5kMChMqWh7IEvURNCqsg+SjZGQ==

css-color-keywords@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/css-color-keywords/-/css-color-keywords-1.0.0.tgz#fea2616dc676b2962686b3af8dbdbe180b244e05"
  integrity sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg==

css-in-js-utils@^3.1.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/css-in-js-utils/-/css-in-js-utils-3.1.0.tgz#640ae6a33646d401fc720c54fc61c42cd76ae2bb"
  integrity sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==
  dependencies:
    hyphenate-style-name "^1.0.3"

css-to-react-native@3.2.0:
  version "3.2.0"
  resolved "https://npm.corp.kuaishou.com/css-to-react-native/-/css-to-react-native-3.2.0.tgz#cdd8099f71024e149e4f6fe17a7d46ecd55f1e32"
  integrity sha512-e8RKaLXMOFii+02mOlqwjbD00KSEKqblnpO9e++1aXS1fPQOpS1YoqdVHBqPjHNoxeF2mimzVqawm2KCbEdtHQ==
  dependencies:
    camelize "^1.0.0"
    css-color-keywords "^1.0.0"
    postcss-value-parser "^4.0.2"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "https://npm.corp.kuaishou.com/css-tree/-/css-tree-1.1.3.tgz#eb4870fb6fd7707327ec95c2ff2ab09b5e8db91d"
  integrity sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

cssfilter@0.0.10:
  version "0.0.10"
  resolved "https://npm.corp.kuaishou.com/cssfilter/-/cssfilter-0.0.10.tgz#c6d2672632a2e5c83e013e6864a42ce8defd20ae"
  integrity sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw==

csstype@3.1.3, csstype@^3.0.2, csstype@^3.0.8, csstype@^3.1.2, csstype@^3.1.3:
  version "3.1.3"
  resolved "https://npm.corp.kuaishou.com/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

"d3-array@1 - 3", "d3-array@2 - 3", "d3-array@2.10.0 - 3", "d3-array@2.5.0 - 3", d3-array@^3.2.4:
  version "3.2.4"
  resolved "https://npm.corp.kuaishou.com/d3-array/-/d3-array-3.2.4.tgz#15fec33b237f97ac5d7c986dc77da273a8ed0bb5"
  integrity sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==
  dependencies:
    internmap "1 - 2"

d3-binarytree@1:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/d3-binarytree/-/d3-binarytree-1.0.2.tgz#ed43ebc13c70fbabfdd62df17480bc5a425753cc"
  integrity sha512-cElUNH+sHu95L04m92pG73t2MEJXKu+GeKUN1TJkFsu93E5W8E9Sc3kHEGJKgenGvj19m6upSn2EunvMgMD2Yw==

"d3-color@1 - 3", d3-color@^3.1.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/d3-color/-/d3-color-3.1.0.tgz#395b2833dfac71507f12ac2f7af23bf819de24e2"
  integrity sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==

"d3-dispatch@1 - 3", d3-dispatch@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-dispatch/-/d3-dispatch-3.0.1.tgz#5fc75284e9c2375c36c839411a0cf550cbfc4d5e"
  integrity sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==

"d3-dsv@1 - 3", d3-dsv@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-dsv/-/d3-dsv-3.0.1.tgz#c63af978f4d6a0d084a52a673922be2160789b73"
  integrity sha512-UG6OvdI5afDIFP9w4G0mNq50dSOsXHJaRE8arAS5o9ApWnIElp8GZw1Dun8vP8OyHOZ/QJUKUJwxiiCCnUwm+Q==
  dependencies:
    commander "7"
    iconv-lite "0.6"
    rw "1"

d3-ease@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-ease/-/d3-ease-3.0.1.tgz#9658ac38a2140d59d346160f1f6c30fda0bd12f4"
  integrity sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==

d3-fetch@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-fetch/-/d3-fetch-3.0.1.tgz#83141bff9856a0edb5e38de89cdcfe63d0a60a22"
  integrity sha512-kpkQIM20n3oLVBKGg6oHrUchHM3xODkTzjMoj7aWQFq5QEM+R6E4WkzT5+tojDY7yjez8KgCBRoj4aEr99Fdqw==
  dependencies:
    d3-dsv "1 - 3"

d3-force-3d@^3.0.5:
  version "3.0.6"
  resolved "https://npm.corp.kuaishou.com/d3-force-3d/-/d3-force-3d-3.0.6.tgz#7ea4c26d7937b82993bd9444f570ed52f661d4aa"
  integrity sha512-4tsKHUPLOVkyfEffZo1v6sFHvGFwAIIjt/W8IThbp08DYAsXZck+2pSHEG5W1+gQgEvFLdZkYvmJAbRM2EzMnA==
  dependencies:
    d3-binarytree "1"
    d3-dispatch "1 - 3"
    d3-octree "1"
    d3-quadtree "1 - 3"
    d3-timer "1 - 3"

d3-force@^3.0.0:
  version "3.0.0"
  resolved "https://npm.corp.kuaishou.com/d3-force/-/d3-force-3.0.0.tgz#3e2ba1a61e70888fe3d9194e30d6d14eece155c4"
  integrity sha512-zxV/SsA+U4yte8051P4ECydjD/S+qeYtnaIyAs9tgHCqfguma/aAQDjo85A9Z6EKhBirHRJHXIgJUlffT4wdLg==
  dependencies:
    d3-dispatch "1 - 3"
    d3-quadtree "1 - 3"
    d3-timer "1 - 3"

"d3-format@1 - 3", d3-format@^3.1.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/d3-format/-/d3-format-3.1.0.tgz#9260e23a28ea5cb109e93b21a06e24e2ebd55641"
  integrity sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==

d3-geo-projection@^4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/d3-geo-projection/-/d3-geo-projection-4.0.0.tgz#dc229e5ead78d31869a4e87cf1f45bd2716c48ca"
  integrity sha512-p0bK60CEzph1iqmnxut7d/1kyTmm3UWtPlwdkM31AU+LW+BXazd5zJdoCn7VFxNCHXRngPHRnsNn5uGjLRGndg==
  dependencies:
    commander "7"
    d3-array "1 - 3"
    d3-geo "1.12.0 - 3"

"d3-geo@1.12.0 - 3", d3-geo@^3.1.1:
  version "3.1.1"
  resolved "https://npm.corp.kuaishou.com/d3-geo/-/d3-geo-3.1.1.tgz#6027cf51246f9b2ebd64f99e01dc7c3364033a4d"
  integrity sha512-637ln3gXKXOwhalDzinUgY83KzNWZRKbYubaG+fGVuc/dxO64RRljtCTnf5ecMyE1RIdtqpkVcq0IbtU2S8j2Q==
  dependencies:
    d3-array "2.5.0 - 3"

d3-hierarchy@^3.1.2:
  version "3.1.2"
  resolved "https://npm.corp.kuaishou.com/d3-hierarchy/-/d3-hierarchy-3.1.2.tgz#b01cd42c1eed3d46db77a5966cf726f8c09160c6"
  integrity sha512-FX/9frcub54beBdugHjDCdikxThEqjnR93Qt7PvQTOHxyiNCAlvMrHhclk3cD5VeAaq9fxmfRp+CnWw9rEMBuA==

"d3-interpolate@1 - 3", "d3-interpolate@1.2.0 - 3", d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-interpolate/-/d3-interpolate-3.0.1.tgz#3c47aa5b32c5b3dfb56ef3fd4342078a632b400d"
  integrity sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==
  dependencies:
    d3-color "1 - 3"

d3-octree@1, d3-octree@^1.0.2:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/d3-octree/-/d3-octree-1.1.0.tgz#f07e353b76df872644e7130ab1a74c5ef2f4287e"
  integrity sha512-F8gPlqpP+HwRPMO/8uOu5wjH110+6q4cgJvgJT6vlpy3BEaDIKlTZrgHKZSp/i1InRpVfh4puY/kvL6MxK930A==

d3-path@^3.1.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/d3-path/-/d3-path-3.1.0.tgz#22df939032fb5a71ae8b1800d61ddb7851c42526"
  integrity sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==

"d3-quadtree@1 - 3", d3-quadtree@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-quadtree/-/d3-quadtree-3.0.1.tgz#6dca3e8be2b393c9a9d514dabbd80a92deef1a4f"
  integrity sha512-04xDrxQTDTCFwP5H6hRhsRcb9xxv2RzkcsygFzmkSIOJy3PeRJP7sNk3VRIbKXcog561P9oU0/rVH6vDROAgUw==

d3-random@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-random/-/d3-random-3.0.1.tgz#d4926378d333d9c0bfd1e6fa0194d30aebaa20f4"
  integrity sha512-FXMe9GfxTxqd5D6jFsQ+DJ8BJS4E/fT5mqqdjovykEB2oFbTMDVdg1MGFxfQW+FBOGoB++k8swBrgwSHT1cUXQ==

d3-regression@^1.3.10:
  version "1.3.10"
  resolved "https://npm.corp.kuaishou.com/d3-regression/-/d3-regression-1.3.10.tgz#d1a411ab45044d9e8d5b8aec05f2e598e1a621c9"
  integrity sha512-PF8GWEL70cHHWpx2jUQXc68r1pyPHIA+St16muk/XRokETzlegj5LriNKg7o4LR0TySug4nHYPJNNRz/W+/Niw==

d3-scale-chromatic@^3.1.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/d3-scale-chromatic/-/d3-scale-chromatic-3.1.0.tgz#34c39da298b23c20e02f1a4b239bd0f22e7f1314"
  integrity sha512-A3s5PWiZ9YCXFye1o246KoscMWqf8BsD9eRiJ3He7C9OBaxKhAd5TFCdEx/7VbKtxxTsu//1mMJFrEt572cEyQ==
  dependencies:
    d3-color "1 - 3"
    d3-interpolate "1 - 3"

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "https://npm.corp.kuaishou.com/d3-scale/-/d3-scale-4.0.2.tgz#82b38e8e8ff7080764f8dcec77bd4be393689396"
  integrity sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

d3-shape@^3.2.0:
  version "3.2.0"
  resolved "https://npm.corp.kuaishou.com/d3-shape/-/d3-shape-3.2.0.tgz#a1a839cbd9ba45f28674c69d7f855bcf91dfc6a5"
  integrity sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "https://npm.corp.kuaishou.com/d3-time-format/-/d3-time-format-4.1.0.tgz#7ab5257a5041d11ecb4fe70a5c7d16a195bb408a"
  integrity sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==
  dependencies:
    d3-time "1 - 3"

"d3-time@1 - 3", "d3-time@2.1.1 - 3", d3-time@^3.1.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/d3-time/-/d3-time-3.1.0.tgz#9310db56e992e3c0175e1ef385e545e48a9bb5c7"
  integrity sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==
  dependencies:
    d3-array "2 - 3"

"d3-timer@1 - 3", d3-timer@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/d3-timer/-/d3-timer-3.0.1.tgz#6284d2a2708285b1abb7e201eda4380af35e63b0"
  integrity sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==

dagre@^0.8.5:
  version "0.8.5"
  resolved "https://npm.corp.kuaishou.com/dagre/-/dagre-0.8.5.tgz#ba30b0055dac12b6c1fcc247817442777d06afee"
  integrity sha512-/aTqmnRta7x7MCCpExk7HQL2O4owCT2h8NT//9I1OQ9vt29Pa0BzSAkR5lwFUcQ7491yVi/3CXU9jQ5o0Mn2Sw==
  dependencies:
    graphlib "^2.1.8"
    lodash "^4.17.15"

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/data-view-buffer/-/data-view-buffer-1.0.2.tgz#211a03ba95ecaf7798a8c7198d79536211f88570"
  integrity sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz#9e80f7ca52453ce3e93d25a35318767ea7704735"
  integrity sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz#068307f9b71ab76dbbe10291389e020856606191"
  integrity sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@2.x:
  version "2.30.0"
  resolved "https://npm.corp.kuaishou.com/date-fns/-/date-fns-2.30.0.tgz#f367e644839ff57894ec6ac480de40cae4b0f4d0"
  integrity sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==
  dependencies:
    "@babel/runtime" "^7.21.0"

dayjs@1.11.13, dayjs@1.x, dayjs@^1.11.11, dayjs@^1.11.13, dayjs@^1.11.6, dayjs@^1.9.1:
  version "1.11.13"
  resolved "https://npm.corp.kuaishou.com/dayjs/-/dayjs-1.11.13.tgz#92430b0139055c3ebb60150aa13e860a4b5a366c"
  integrity sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==

debug@^4.1.0, debug@^4.3.1:
  version "4.4.0"
  resolved "https://npm.corp.kuaishou.com/debug/-/debug-4.4.0.tgz#2b3f2aea2ffeb776477460267377dc8710faba8a"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

debug@~4.3.1, debug@~4.3.2:
  version "4.3.7"
  resolved "https://npm.corp.kuaishou.com/debug/-/debug-4.3.7.tgz#87945b4151a011d76d95a198d7111c865c360a52"
  integrity sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==
  dependencies:
    ms "^2.1.3"

decamelize@^1.0.0:
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==

decimal.js-light@^2.5.1:
  version "2.5.1"
  resolved "https://npm.corp.kuaishou.com/decimal.js-light/-/decimal.js-light-2.5.1.tgz#134fd32508f19e208f4fb2f8dac0d2626a867934"
  integrity sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==

deep-equal@~1.1.1:
  version "1.1.2"
  resolved "https://npm.corp.kuaishou.com/deep-equal/-/deep-equal-1.1.2.tgz#78a561b7830eef3134c7f6f3a3d6af272a678761"
  integrity sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deepmerge@^4.3.1:
  version "4.3.1"
  resolved "https://npm.corp.kuaishou.com/deepmerge/-/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

define-data-property@^1.0.1, define-data-property@^1.1.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://npm.corp.kuaishou.com/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/define-properties/-/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

defined@~1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/defined/-/defined-1.0.1.tgz#c0b9db27bfaffd95d6f61399419b893df0f91ebf"
  integrity sha512-hsBd2qSVCRE+5PmNdHt1uzyrFu5d3RwmFDKzyNZMFq/EwDNJF7Ee5+D5oEKF0hU6LhtoUF1macFvOe4AskQC1Q==

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

des.js@^1.0.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/des.js/-/des.js-1.1.0.tgz#1d37f5766f3bbff4ee9638e871a8768c173b81da"
  integrity sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg==
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

diffie-hellman@^5.0.3:
  version "5.0.3"
  resolved "https://npm.corp.kuaishou.com/diffie-hellman/-/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dnd-core@15.1.2:
  version "15.1.2"
  resolved "https://npm.corp.kuaishou.com/dnd-core/-/dnd-core-15.1.2.tgz#0983bce555c4985f58b731ffe1faed31e1ea7f6f"
  integrity sha512-EOec1LyJUuGRFg0LDa55rSRAUe97uNVKVkUo8iyvzQlcECYTuPblVQfRWXWj1OyPseFIeebWpNmKFy0h6BcF1A==
  dependencies:
    "@react-dnd/asap" "4.0.1"
    "@react-dnd/invariant" "3.0.1"
    redux "^4.1.2"

dom-align@^1.7.0:
  version "1.12.4"
  resolved "https://npm.corp.kuaishou.com/dom-align/-/dom-align-1.12.4.tgz#3503992eb2a7cfcb2ed3b2a6d21e0b9c00d54511"
  integrity sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==

domain-browser@4.22.0:
  version "4.22.0"
  resolved "https://npm.corp.kuaishou.com/domain-browser/-/domain-browser-4.22.0.tgz#6ddd34220ec281f9a65d3386d267ddd35c491f9f"
  integrity sha512-IGBwjF7tNk3cwypFNH/7bfzBcgSCbaMOD3GsaY1AU/JRrnHnYgEM0+9kQt52iZxjNsjBtJYtao146V+f8jFZNw==

dotignore@~0.1.2:
  version "0.1.2"
  resolved "https://npm.corp.kuaishou.com/dotignore/-/dotignore-0.1.2.tgz#f942f2200d28c3a76fbdd6f0ee9f3257c8a2e905"
  integrity sha512-UGGGWfSauusaVJC+8fgV+NVvBXkCTmVv7sk6nojDZZvuOUNGUy0Zk4UpHQD6EDjS0jpBwcACvH4eofvyzBcRDw==
  dependencies:
    minimatch "^3.0.4"

draft-convert@^2.0.0:
  version "2.1.13"
  resolved "https://npm.corp.kuaishou.com/draft-convert/-/draft-convert-2.1.13.tgz#ffa1878feee88e0911c5728cd1445f6e5d861a41"
  integrity sha512-/h/n4JCfyO8aWby7wKBkccHdsuVbbDyHWXi/B3Zf2pN++lN1lDOIVt5ulXCcbH2Y5YJEFzMJw/YGfN+R0axxxg==
  dependencies:
    "@babel/runtime" "^7.5.5"
    immutable "~3.7.4"
    invariant "^2.2.1"

draft-js-multidecorators@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/draft-js-multidecorators/-/draft-js-multidecorators-1.0.0.tgz#6c4be8d7b78dd2b966ee51ee6cc179b9b535e612"
  integrity sha512-7qdy+YQol5iq38AoEerhgSJWhCzxvZLn1x5ODfUlGfWlg0SrZ9AXJbaxHVIjdSIZNrbVIm+WANujNxMqCmDSZQ==
  dependencies:
    immutable "*"

draft-js@^0.10.3:
  version "0.10.5"
  resolved "https://npm.corp.kuaishou.com/draft-js/-/draft-js-0.10.5.tgz#bfa9beb018fe0533dbb08d6675c371a6b08fa742"
  integrity sha512-LE6jSCV9nkPhfVX2ggcRLA4FKs6zWq9ceuO/88BpXdNCS7mjRTgs0NsV6piUCJX9YxMsB9An33wnkMmU2sD2Zg==
  dependencies:
    fbjs "^0.8.15"
    immutable "~3.7.4"
    object-assign "^4.1.0"

draftjs-utils@^0.9.4:
  version "0.9.4"
  resolved "https://npm.corp.kuaishou.com/draftjs-utils/-/draftjs-utils-0.9.4.tgz#976c61aa133dbbbfedd65ae1dd6627d7b98c6f08"
  integrity sha512-KYjABSbGpJrwrwmxVj5UhfV37MF/p0QRxKIyL+/+QOaJ8J9z1FBKxkblThbpR0nJi9lxPQWGg+gh+v0dAsSCCg==

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/dunder-proto/-/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

duplexify@^4.1.2:
  version "4.1.3"
  resolved "https://npm.corp.kuaishou.com/duplexify/-/duplexify-4.1.3.tgz#a07e1c0d0a2c001158563d32592ba58bddb0236f"
  integrity sha512-M3BmBhwJRZsSx38lZyhE53Csddgzl5R7xGJNk7CVddZD6CcmwMCH8J+7AprIrQKH7TonKxaCjcv27Qmf+sQ+oA==
  dependencies:
    end-of-stream "^1.4.1"
    inherits "^2.0.3"
    readable-stream "^3.1.1"
    stream-shift "^1.0.2"

echarts-for-react@^3.0.2:
  version "3.0.2"
  resolved "https://npm.corp.kuaishou.com/echarts-for-react/-/echarts-for-react-3.0.2.tgz#ac5859157048a1066d4553e34b328abb24f2b7c1"
  integrity sha512-DRwIiTzx8JfwPOVgGttDytBqdp5VzCSyMRIxubgU/g2n9y3VLUmF2FK7Icmg/sNVkv4+rktmrLN9w22U2yy3fA==
  dependencies:
    fast-deep-equal "^3.1.3"
    size-sensor "^1.0.1"

echarts@^5.6.0:
  version "5.6.0"
  resolved "https://npm.corp.kuaishou.com/echarts/-/echarts-5.6.0.tgz#2377874dca9fb50f104051c3553544752da3c9d6"
  integrity sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==
  dependencies:
    tslib "2.3.0"
    zrender "5.6.1"

electron-to-chromium@^1.5.73:
  version "1.5.97"
  resolved "https://npm.corp.kuaishou.com/electron-to-chromium/-/electron-to-chromium-1.5.97.tgz#5c4a4744c79e7c85b187adf5160264ac130c776f"
  integrity sha512-HKLtaH02augM7ZOdYRuO19rWDeY+QSJ1VxnXFa/XDFLf07HvM90pALIJFgrO+UVaajI3+aJMMpojoUTLZyQ7JQ==

elliptic@^6.5.3, elliptic@^6.5.5:
  version "6.6.1"
  resolved "https://npm.corp.kuaishou.com/elliptic/-/elliptic-6.6.1.tgz#3b8ffb02670bf69e382c7f65bf524c97c5405c06"
  integrity sha512-RaddvvMatK2LJHqFJ+YA4WysVN5Ita9E35botqIYspQ4TkRAlCicdzKOjlyv/1Za5RyTNn7di//eEV0uTAfe3g==
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://npm.corp.kuaishou.com/emojis-list/-/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

encoding@^0.1.11:
  version "0.1.13"
  resolved "https://npm.corp.kuaishou.com/encoding/-/encoding-0.1.13.tgz#56574afdd791f54a8e9b2785c0582a2d26210fa9"
  integrity sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://npm.corp.kuaishou.com/end-of-stream/-/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

engine.io-client@~6.6.1:
  version "6.6.3"
  resolved "https://npm.corp.kuaishou.com/engine.io-client/-/engine.io-client-6.6.3.tgz#815393fa24f30b8e6afa8f77ccca2f28146be6de"
  integrity sha512-T0iLjnyNWahNyv/lcjS2y4oE358tVS/SYQNxYXGAJ9/GLgH4VCvOQ/mhTjqU88mLZCQgiG8RIegFHYCdVC+j5w==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"
    engine.io-parser "~5.2.1"
    ws "~8.17.1"
    xmlhttprequest-ssl "~2.1.1"

engine.io-parser@~5.2.1:
  version "5.2.3"
  resolved "https://npm.corp.kuaishou.com/engine.io-parser/-/engine.io-parser-5.2.3.tgz#00dc5b97b1f233a23c9398d0209504cf5f94d92f"
  integrity sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==

error-stack-parser@^2.0.6, error-stack-parser@^2.1.4:
  version "2.1.4"
  resolved "https://npm.corp.kuaishou.com/error-stack-parser/-/error-stack-parser-2.1.4.tgz#229cb01cdbfa84440bfa91876285b94680188286"
  integrity sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==
  dependencies:
    stackframe "^1.3.4"

es-abstract@^1.22.1, es-abstract@^1.23.2, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9:
  version "1.23.9"
  resolved "https://npm.corp.kuaishou.com/es-abstract/-/es-abstract-1.23.9.tgz#5b45994b7de78dada5c1bebf1379646b32b9d606"
  integrity sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.0"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-regex "^1.2.1"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.0"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.3"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.3"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.18"

es-aggregate-error@^1.0.10:
  version "1.0.13"
  resolved "https://npm.corp.kuaishou.com/es-aggregate-error/-/es-aggregate-error-1.0.13.tgz#7f28b77c9d8d09bbcd3a466e4be9fe02fa985201"
  integrity sha512-KkzhUUuD2CUMqEc8JEqsXEMDHzDPE8RCjZeUBitsnB1eNcAJWQPiciKsMXe3Yytj4Flw1XLl46Qcf9OxvZha7A==
  dependencies:
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    globalthis "^1.0.3"
    has-property-descriptors "^1.0.2"
    set-function-name "^2.0.2"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz#873f3e84418de4ee19c5be752990b2e44718d09e"
  integrity sha512-wd6JXUmyHmt8T5a2xreUwKcGPq6f1f+WwIJkijUqiGcJz1qqnZgP6XIK+QyIWU5lT7imeNxUll48bziG+TSYcA==

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/es-define-property/-/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://npm.corp.kuaishou.com/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-get-iterator@^1.0.2:
  version "1.1.3"
  resolved "https://npm.corp.kuaishou.com/es-get-iterator/-/es-get-iterator-1.1.3.tgz#3ef87523c5d464d41084b2c3c9c214f1199763d6"
  integrity sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    has-symbols "^1.0.3"
    is-arguments "^1.1.1"
    is-map "^2.0.2"
    is-set "^2.0.2"
    is-string "^1.0.7"
    isarray "^2.0.5"
    stop-iteration-iterator "^1.0.0"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://npm.corp.kuaishou.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz#f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d"
  integrity sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "https://npm.corp.kuaishou.com/es-to-primitive/-/es-to-primitive-1.3.0.tgz#96c89c82cc49fd8794a24835ba3e1ff87f214e18"
  integrity sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

es5-imcompatible-versions@^0.1.78:
  version "0.1.90"
  resolved "https://npm.corp.kuaishou.com/es5-imcompatible-versions/-/es5-imcompatible-versions-0.1.90.tgz#7642260198b7197f9b5f6b82f5a8f8a1de8f1b32"
  integrity sha512-2MPI0t/VV4j/oz1qbMekb4gCW81dewTpM2XJHKnPpZiPGu+1rVWmhTnwcq1vt8AFwWrkNF4RE7OZ9ibnKFYKwg==

esbuild@0.17.19:
  version "0.17.19"
  resolved "https://npm.corp.kuaishou.com/esbuild/-/esbuild-0.17.19.tgz#087a727e98299f0462a3d0bcdd9cd7ff100bd955"
  integrity sha512-XQ0jAPFkK/u3LcVRcvVHQcTIqD6E2H1fvZMA5dQPSOWb3suUbWbfbRf94pjc0bNzRYLfIrDRQXr7X+LHIm5oHw==
  optionalDependencies:
    "@esbuild/android-arm" "0.17.19"
    "@esbuild/android-arm64" "0.17.19"
    "@esbuild/android-x64" "0.17.19"
    "@esbuild/darwin-arm64" "0.17.19"
    "@esbuild/darwin-x64" "0.17.19"
    "@esbuild/freebsd-arm64" "0.17.19"
    "@esbuild/freebsd-x64" "0.17.19"
    "@esbuild/linux-arm" "0.17.19"
    "@esbuild/linux-arm64" "0.17.19"
    "@esbuild/linux-ia32" "0.17.19"
    "@esbuild/linux-loong64" "0.17.19"
    "@esbuild/linux-mips64el" "0.17.19"
    "@esbuild/linux-ppc64" "0.17.19"
    "@esbuild/linux-riscv64" "0.17.19"
    "@esbuild/linux-s390x" "0.17.19"
    "@esbuild/linux-x64" "0.17.19"
    "@esbuild/netbsd-x64" "0.17.19"
    "@esbuild/openbsd-x64" "0.17.19"
    "@esbuild/sunos-x64" "0.17.19"
    "@esbuild/win32-arm64" "0.17.19"
    "@esbuild/win32-ia32" "0.17.19"
    "@esbuild/win32-x64" "0.17.19"

escalade@^3.2.0:
  version "3.2.0"
  resolved "https://npm.corp.kuaishou.com/escalade/-/escalade-3.2.0.tgz#011a3f69856ba189dffa7dc8fcce99d2a87903e5"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-string-regexp@^1.0.2:
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://npm.corp.kuaishou.com/eventemitter3/-/eventemitter3-5.0.1.tgz#53f5ffd0a492ac800721bb42c66b841de96423c4"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

events@^3.0.0:
  version "3.3.0"
  resolved "https://npm.corp.kuaishou.com/events/-/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://npm.corp.kuaishou.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-equals@^4.0.3:
  version "4.0.3"
  resolved "https://npm.corp.kuaishou.com/fast-equals/-/fast-equals-4.0.3.tgz#72884cc805ec3c6679b99875f6b7654f39f0e8c7"
  integrity sha512-G3BSX9cfKttjr+2o1O22tYMLq0DPluZnYtq1rXumE1SpL/F/SLIfHx08WYQoWSIpeMYf8sRbJ8++71+v6Pnxfg==

fast-redact@^3.0.0:
  version "3.5.0"
  resolved "https://npm.corp.kuaishou.com/fast-redact/-/fast-redact-3.5.0.tgz#e9ea02f7e57d0cd8438180083e93077e496285e4"
  integrity sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==

fast-shallow-equal@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/fast-shallow-equal/-/fast-shallow-equal-1.0.0.tgz#d4dcaf6472440dcefa6f88b98e3251e27f25628b"
  integrity sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw==

fastest-stable-stringify@^2.0.2:
  version "2.0.2"
  resolved "https://npm.corp.kuaishou.com/fastest-stable-stringify/-/fastest-stable-stringify-2.0.2.tgz#3757a6774f6ec8de40c4e86ec28ea02417214c76"
  integrity sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q==

fbjs@^0.8.15:
  version "0.8.18"
  resolved "https://npm.corp.kuaishou.com/fbjs/-/fbjs-0.8.18.tgz#9835e0addb9aca2eff53295cd79ca1cfc7c9662a"
  integrity sha512-EQaWFK+fEPSoibjNy8IxUtaFOMXcWsY0JaVrQoZR9zC8N2Ygf9iDITPWjUTVIax95b6I742JFLqASHfsag/vKA==
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.30"

fecha@^4.2.1:
  version "4.2.3"
  resolved "https://npm.corp.kuaishou.com/fecha/-/fecha-4.2.3.tgz#4d9ccdbc61e8629b259fdca67e65891448d569fd"
  integrity sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw==

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://npm.corp.kuaishou.com/find-up/-/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flru@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/flru/-/flru-1.0.2.tgz#1ae514c62b8b035ffff9ca9e4563ddcc817f4845"
  integrity sha512-kWyh8ADvHBFz6ua5xYOPnUroZTT/bwWfrCeL0Wj1dzG4/YOmOcfJ99W8dOVyyynJN35rZ9aCOtHChqQovV7yog==

fmin@0.0.2:
  version "0.0.2"
  resolved "https://npm.corp.kuaishou.com/fmin/-/fmin-0.0.2.tgz#59bbb40d43ffdc1c94cd00a568c41f95f1973017"
  integrity sha512-sSi6DzInhl9d8yqssDfGZejChO8d2bAGIpysPsvYsxFe898z89XhCZg6CPNV3nhUhFefeC/AXZK2bAJxlBjN6A==
  dependencies:
    contour_plot "^0.0.1"
    json2module "^0.0.3"
    rollup "^0.25.8"
    tape "^4.5.1"
    uglify-js "^2.6.2"

follow-redirects@^1.14.9:
  version "1.15.9"
  resolved "https://npm.corp.kuaishou.com/follow-redirects/-/follow-redirects-1.15.9.tgz#a604fa10e443bf98ca94228d9eebcc2e8a2c8ee1"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

for-each@^0.3.3, for-each@^0.3.5, for-each@~0.3.3:
  version "0.3.5"
  resolved "https://npm.corp.kuaishou.com/for-each/-/for-each-0.3.5.tgz#d650688027826920feeb0af747ee7b9421a41d47"
  integrity sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==
  dependencies:
    is-callable "^1.2.7"

form-data@^4.0.0:
  version "4.0.1"
  resolved "https://npm.corp.kuaishou.com/form-data/-/form-data-4.0.1.tgz#ba1076daaaa5bfd7e99c1a6cb02aa0a5cff90d48"
  integrity sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://npm.corp.kuaishou.com/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "https://npm.corp.kuaishou.com/function.prototype.name/-/function.prototype.name-1.1.8.tgz#e68e1df7b259a5c949eeef95cdbde53edffabb78"
  integrity sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://npm.corp.kuaishou.com/functions-have-names/-/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

fuse.js@^6.6.2:
  version "6.6.2"
  resolved "https://npm.corp.kuaishou.com/fuse.js/-/fuse.js-6.6.2.tgz#fe463fed4b98c0226ac3da2856a415576dc9a111"
  integrity sha512-cJaJkxCCxC8qIIcPBF9yGxY0W/tVZS3uEISDxhYIdtk8OL93pe+6Zj7LjCqVV4dzbqcriOZ+kQ/NE4RXZHsIGA==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://npm.corp.kuaishou.com/gensync/-/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.1, get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7:
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/get-intrinsic/-/get-intrinsic-1.2.7.tgz#dcfcb33d3272e15f445d15124bc0a216189b9044"
  integrity sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    function-bind "^1.1.2"
    get-proto "^1.0.0"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://npm.corp.kuaishou.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/get-proto/-/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/get-symbol-description/-/get-symbol-description-1.1.0.tgz#7bdd54e0befe8ffc9f3b4e203220d9f1e881b6ee"
  integrity sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

get-them-args@1.3.2:
  version "1.3.2"
  resolved "https://npm.corp.kuaishou.com/get-them-args/-/get-them-args-1.3.2.tgz#74a20ba8a4abece5ae199ad03f2bcc68fdfc9ba5"
  integrity sha512-LRn8Jlk+DwZE4GTlDbT3Hikd1wSHgLMme/+7ddlqKd7ldwR6LjJgTVWzBnR01wnYGe4KgrXjg287RaI22UHmAw==

gl-matrix@^3.3.0, gl-matrix@^3.4.3:
  version "3.4.3"
  resolved "https://npm.corp.kuaishou.com/gl-matrix/-/gl-matrix-3.4.3.tgz#fc1191e8320009fd4d20e9339595c6041ddc22c9"
  integrity sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==

glob@~7.2.3:
  version "7.2.3"
  resolved "https://npm.corp.kuaishou.com/glob/-/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://npm.corp.kuaishou.com/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globalthis@^1.0.3, globalthis@^1.0.4:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/globalthis/-/globalthis-1.0.4.tgz#7430ed3a975d97bfb59bcce41f5cabbafa651236"
  integrity sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

goober@^2.0.41:
  version "2.1.16"
  resolved "https://npm.corp.kuaishou.com/goober/-/goober-2.1.16.tgz#7d548eb9b83ff0988d102be71f271ca8f9c82a95"
  integrity sha512-erjk19y1U33+XAMe1VTvIONHYoSqE4iS7BYUZfHaqeohLmnC0FdxEh7rQU+6MZ4OajItzjZFSRtVANrQwNq6/g==

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/gopd/-/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://npm.corp.kuaishou.com/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

graphlib@^2.1.8:
  version "2.1.8"
  resolved "https://npm.corp.kuaishou.com/graphlib/-/graphlib-2.1.8.tgz#5761d414737870084c92ec7b5dbcb0592c9d35da"
  integrity sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==
  dependencies:
    lodash "^4.17.15"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://npm.corp.kuaishou.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==
  dependencies:
    ansi-regex "^2.0.0"

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/has-bigints/-/has-bigints-1.1.0.tgz#28607e965ac967e03cd2a2c70a2636a1edad49fe"
  integrity sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/has-proto/-/has-proto-1.2.0.tgz#5de5a6eabd95fdffd9818b43055e8065e39fe9d5"
  integrity sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/has-symbols/-/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

has@~1.0.3:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/has/-/has-1.0.4.tgz#2eb2860e000011dae4f1406a86fe80e530fb2ec6"
  integrity sha512-qdSAmqLF6209RFj4VVItywPMbm3vWylknmB3nvNiUIs72xAimcM8nVYxYr7ncvZq5qzk9MKIZR8ijqD/1QuYjQ==

hash-base@^3.0.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/hash-base/-/hash-base-3.1.0.tgz#55c381d9e06e1d2997a883b4a3fddfe7f0d3af33"
  integrity sha512-1nmYp/rhMDiE7AYkDw+lLwlAzz0AntGIe51F3RfFfEqyQ3feY2eI/NcwC6umIQVOASPMsWJLJScWKSSvzL9IVA==
  dependencies:
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    safe-buffer "^5.2.0"

hash-base@~3.0, hash-base@~3.0.4:
  version "3.0.5"
  resolved "https://npm.corp.kuaishou.com/hash-base/-/hash-base-3.0.5.tgz#52480e285395cf7fba17dc4c9e47acdc7f248a8a"
  integrity sha512-vXm0l45VbcHEVlTCzs8M+s0VeYsB2lnlAaThoLKGXr3bE/VWDOelNUnycUPEhKEaXARL2TEFjBOyUiM6+55KBg==
  dependencies:
    inherits "^2.0.4"
    safe-buffer "^5.2.1"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "https://npm.corp.kuaishou.com/hash.js/-/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  resolved "https://npm.corp.kuaishou.com/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hmac-drbg@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/hmac-drbg/-/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://npm.corp.kuaishou.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

html-entities@^2.6.0:
  version "2.6.0"
  resolved "https://npm.corp.kuaishou.com/html-entities/-/html-entities-2.6.0.tgz#7c64f1ea3b36818ccae3d3fb48b6974208e984f8"
  integrity sha512-kig+rMn/QOVRvr7c86gQ8lWXq+Hkv6CbAH1hLu+RG338StTpE8Z0b44SDVaqVu7HGKf27frdmUYEs9hTUX/cLQ==

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/https-browserify/-/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg==

hyphenate-style-name@^1.0.3:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz#1797bf50369588b47b72ca6d5e65374607cf4436"
  integrity sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==

iconv-lite@0.6, iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "https://npm.corp.kuaishou.com/iconv-lite/-/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

idb@^8.0.0:
  version "8.0.2"
  resolved "https://npm.corp.kuaishou.com/idb/-/idb-8.0.2.tgz#349af3974281879889e0572bbb231f978b9f3cf0"
  integrity sha512-CX70rYhx7GDDQzwwQMDwF6kDRQi5vVs6khHUumDrMecBylKkwvZ8HWvKV08AGb7VbpoGCWUQ4aHzNDgoUiOIUg==

ieee754@^1.1.13:
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

immutable@*:
  version "5.0.3"
  resolved "https://npm.corp.kuaishou.com/immutable/-/immutable-5.0.3.tgz#aa037e2313ea7b5d400cd9298fa14e404c933db1"
  integrity sha512-P8IdPQHq3lA1xVeBRi5VPqUm5HDgKnx0Ru51wZz5mjxHr5n3RWhjIpOFU7ybkUxfB+5IToy+OLaHYDBIWsv+uw==

immutable@~3.7.4:
  version "3.7.6"
  resolved "https://npm.corp.kuaishou.com/immutable/-/immutable-3.7.6.tgz#13b4d3cb12befa15482a26fe1b2ebae640071e4b"
  integrity sha512-AizQPcaofEtO11RZhPPHBOJRdo/20MKQF9mBLnVkBoyHi1/zXK8fzVdnEpSV9gxqtnh6Qomfp3F0xT5qP/vThw==

import-html-entry@^1.9.0:
  version "1.17.0"
  resolved "https://npm.corp.kuaishou.com/import-html-entry/-/import-html-entry-1.17.0.tgz#65211779b4ebf5a25200308bf0cbc6f831d5e4e1"
  integrity sha512-2SDsRlGlE8bqdnGqsOyiDPEWlzJR0jNW4LWopnZl5QE1Yd0nJ7fykWo2GaKUF7Jq7pR0g3dElhuJHyamTt1gPQ==
  dependencies:
    "@babel/runtime" "^7.7.2"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://npm.corp.kuaishou.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@~2.0.4:
  version "2.0.4"
  resolved "https://npm.corp.kuaishou.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inline-style-prefixer@^7.0.1:
  version "7.0.1"
  resolved "https://npm.corp.kuaishou.com/inline-style-prefixer/-/inline-style-prefixer-7.0.1.tgz#9310f3cfa2c6f3901d1480f373981c02691781e8"
  integrity sha512-lhYo5qNTQp3EvSSp3sRvXMbVQTLrvGV6DycRMJ5dm2BLMiJ30wpXKdDdgX+GmJZ5uQMucwRKHamXSst3Sj/Giw==
  dependencies:
    css-in-js-utils "^3.1.0"

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/internal-slot/-/internal-slot-1.1.0.tgz#1eac91762947d2f7056bc838d93e13b2e9604961"
  integrity sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

"internmap@1 - 2":
  version "2.0.3"
  resolved "https://npm.corp.kuaishou.com/internmap/-/internmap-2.0.3.tgz#6685f23755e43c524e251d29cbc97248e3061009"
  integrity sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==

intersection-observer@^0.12.0:
  version "0.12.2"
  resolved "https://npm.corp.kuaishou.com/intersection-observer/-/intersection-observer-0.12.2.tgz#4a45349cc0cd91916682b1f44c28d7ec737dc375"
  integrity sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==

invariant@^2.2.1:
  version "2.2.4"
  resolved "https://npm.corp.kuaishou.com/invariant/-/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
  dependencies:
    loose-envify "^1.0.0"

is-any-array@^2.0.0, is-any-array@^2.0.1:
  version "2.0.1"
  resolved "https://npm.corp.kuaishou.com/is-any-array/-/is-any-array-2.0.1.tgz#9233242a9c098220290aa2ec28f82ca7fa79899e"
  integrity sha512-UtilS7hLRu++wb/WBAw9bNuP1Eg04Ivn1vERJck8zJthEvXCBEBpGR/33u/xLKWEQf95803oalHrVDptcAvFdQ==

is-arguments@^1.0.4, is-arguments@^1.1.1:
  version "1.2.0"
  resolved "https://npm.corp.kuaishou.com/is-arguments/-/is-arguments-1.2.0.tgz#ad58c6aecf563b78ef2bf04df540da8f5d7d8e1b"
  integrity sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "https://npm.corp.kuaishou.com/is-array-buffer/-/is-array-buffer-3.0.5.tgz#65742e1e687bd2cc666253068fd8707fe4d44280"
  integrity sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://npm.corp.kuaishou.com/is-arrayish/-/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-async-function@^2.0.0:
  version "2.1.1"
  resolved "https://npm.corp.kuaishou.com/is-async-function/-/is-async-function-2.1.1.tgz#3e69018c8e04e73b738793d020bfe884b9fd3523"
  integrity sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/is-bigint/-/is-bigint-1.1.0.tgz#dda7a3445df57a42583db4228682eba7c4170672"
  integrity sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==
  dependencies:
    has-bigints "^1.0.2"

is-boolean-object@^1.2.1:
  version "1.2.2"
  resolved "https://npm.corp.kuaishou.com/is-boolean-object/-/is-boolean-object-1.2.2.tgz#7067f47709809a393c71ff5bb3e135d8a9215d9e"
  integrity sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://npm.corp.kuaishou.com/is-buffer/-/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==

is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://npm.corp.kuaishou.com/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://npm.corp.kuaishou.com/is-core-module/-/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/is-data-view/-/is-data-view-1.0.2.tgz#bae0a41b9688986c2188dda6657e56b8f9e63b8e"
  integrity sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/is-date-object/-/is-date-object-1.1.0.tgz#ad85541996fc7aa8b2729701d27b7319f95d82f7"
  integrity sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz#eefdcdc6c94ddd0674d9c85887bf93f944a97c90"
  integrity sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==
  dependencies:
    call-bound "^1.0.3"

is-generator-function@^1.0.10, is-generator-function@^1.0.7:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/is-generator-function/-/is-generator-function-1.1.0.tgz#bf3eeda931201394f57b5dba2800f91a238309ca"
  integrity sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-map@^2.0.2, is-map@^2.0.3:
  version "2.0.3"
  resolved "https://npm.corp.kuaishou.com/is-map/-/is-map-2.0.3.tgz#ede96b7fe1e270b3c4465e3a465658764926d62e"
  integrity sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==

is-nan@^1.3.2:
  version "1.3.2"
  resolved "https://npm.corp.kuaishou.com/is-nan/-/is-nan-1.3.2.tgz#043a54adea31748b55b6cd4e09aadafa69bd9e1d"
  integrity sha512-E+zBKpQ2t6MEo1VsonYmluk9NxGrbzpeeLC2xIViuO2EjU2xsXsBPwTr3Ykv9l08UYEVEdWeRZNouaZqF6RN0w==
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/is-number-object/-/is-number-object-1.1.1.tgz#144b21e95a1bc148205dcc2814a9134ec41b2541"
  integrity sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-regex@^1.1.4, is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/is-regex/-/is-regex-1.2.1.tgz#76d70a3ed10ef9be48eb577887d74205bf0cad22"
  integrity sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-regex@~1.1.4:
  version "1.1.4"
  resolved "https://npm.corp.kuaishou.com/is-regex/-/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-set@^2.0.2, is-set@^2.0.3:
  version "2.0.3"
  resolved "https://npm.corp.kuaishou.com/is-set/-/is-set-2.0.3.tgz#8ab209ea424608141372ded6e0cb200ef1d9d01d"
  integrity sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz#9b67844bd9b7f246ba0708c3a93e34269c774f6f"
  integrity sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==
  dependencies:
    call-bound "^1.0.3"

is-stream@^1.0.1:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/is-stream/-/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==

is-string@^1.0.7, is-string@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/is-string/-/is-string-1.1.1.tgz#92ea3f3d5c5b6e039ca8677e5ac8d07ea773cbb9"
  integrity sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/is-symbol/-/is-symbol-1.1.1.tgz#f47761279f532e2b05a7024a7506dbbedacd0634"
  integrity sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15, is-typed-array@^1.1.3:
  version "1.1.15"
  resolved "https://npm.corp.kuaishou.com/is-typed-array/-/is-typed-array-1.1.15.tgz#4bfb4a45b61cee83a5a46fba778e4e8d59c0ce0b"
  integrity sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==
  dependencies:
    which-typed-array "^1.1.16"

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "https://npm.corp.kuaishou.com/is-weakmap/-/is-weakmap-2.0.2.tgz#bf72615d649dfe5f699079c54b83e47d1ae19cfd"
  integrity sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==

is-weakref@^1.0.2, is-weakref@^1.1.0:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/is-weakref/-/is-weakref-1.1.1.tgz#eea430182be8d64174bd96bffbc46f21bf3f9293"
  integrity sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "https://npm.corp.kuaishou.com/is-weakset/-/is-weakset-2.0.4.tgz#c9f5deb0bc1906c6d6f1027f284ddf459249daca"
  integrity sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://npm.corp.kuaishou.com/isarray/-/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://npm.corp.kuaishou.com/isomorphic-fetch/-/isomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  integrity sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA==
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

isomorphic-rslog@0.0.6:
  version "0.0.6"
  resolved "https://npm.corp.kuaishou.com/isomorphic-rslog/-/isomorphic-rslog-0.0.6.tgz#abf13c77b545b03e5ab3bc376e6de720e07eb190"
  integrity sha512-HM0q6XqQ93psDlqvuViNs/Ea3hAyGDkIdVAHlrEocjjAwGrs1fZ+EdQjS9eUPacnYB7Y8SoDdSY3H8p3ce205A==

isomorphic-timers-promises@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/isomorphic-timers-promises/-/isomorphic-timers-promises-1.0.1.tgz#e4137c24dbc54892de8abae3a4b5c1ffff381598"
  integrity sha512-u4sej9B1LPSxTGKB/HiuzvEQnXH0ECYkSVQU39koSwmFAxhlEAFl9RdTvLv4TOTQUgBS5O3O5fwUxk6byBZ+IQ==

iterate-iterator@^1.0.1:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/iterate-iterator/-/iterate-iterator-1.0.2.tgz#551b804c9eaa15b847ea6a7cdc2f5bf1ec150f91"
  integrity sha512-t91HubM4ZDQ70M9wqp+pcNpu8OyJ9UAtXntT/Bcsvp5tZMnz9vRa+IunKXeI8AnfZMTv0jNuVEmGeLSMjVvfPw==

iterate-value@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/iterate-value/-/iterate-value-1.0.2.tgz#935115bd37d006a52046535ebc8d07e9c9337f57"
  integrity sha512-A6fMAio4D2ot2r/TYzr4yUWrmwNdsN5xL7+HUiyACE4DXm+q8HtPcnFTp+NnW3k4N05tZ7FVYFFb2CR13NxyHQ==
  dependencies:
    es-get-iterator "^1.0.2"
    iterate-iterator "^1.0.1"

jest-util@^29.4.3:
  version "29.7.0"
  resolved "https://npm.corp.kuaishou.com/jest-util/-/jest-util-29.7.0.tgz#23c2b62bfb22be82b44de98055802ff3710fc0bc"
  integrity sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-worker@29.4.3:
  version "29.4.3"
  resolved "https://npm.corp.kuaishou.com/jest-worker/-/jest-worker-29.4.3.tgz#9a4023e1ea1d306034237c7133d7da4240e8934e"
  integrity sha512-GLHN/GTAAMEy5BFdvpUfzr9Dr80zQqBrh0fz1mtRMe05hqP45+HfQltu7oTBfduD0UeZs09d+maFtFYAXFWvAA==
  dependencies:
    "@types/node" "*"
    jest-util "^29.4.3"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

js-cookie@^2.2.1, js-cookie@^2.x.x:
  version "2.2.1"
  resolved "https://npm.corp.kuaishou.com/js-cookie/-/js-cookie-2.2.1.tgz#69e106dc5d5806894562902aa5baec3744e9b2b8"
  integrity sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==

js-cookie@^3.0.1, js-cookie@^3.0.5:
  version "3.0.5"
  resolved "https://npm.corp.kuaishou.com/js-cookie/-/js-cookie-3.0.5.tgz#0b7e2fd0c01552c58ba86e0841f94dc2557dcdbc"
  integrity sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/jsesc/-/jsesc-3.1.0.tgz#74d335a234f67ed19907fdadfac7ccf9d409825d"
  integrity sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==

json-stringify-safe@^5.0.1:
  version "5.0.1"
  resolved "https://npm.corp.kuaishou.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==

json2module@^0.0.3:
  version "0.0.3"
  resolved "https://npm.corp.kuaishou.com/json2module/-/json2module-0.0.3.tgz#00fb5f4a9b7adfc3f0647c29cb17bcd1979be9b2"
  integrity sha512-qYGxqrRrt4GbB8IEOy1jJGypkNsjWoIMlZt4bAsmUScCA507Hbc2p1JOhBzqn45u3PWafUgH2OnzyNU7udO/GA==
  dependencies:
    rw "^1.3.2"

json2mq@^0.2.0:
  version "0.2.0"
  resolved "https://npm.corp.kuaishou.com/json2mq/-/json2mq-0.2.0.tgz#b637bd3ba9eabe122c83e9720483aeb10d2c904a"
  integrity sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==
  dependencies:
    string-convert "^0.2.0"

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://npm.corp.kuaishou.com/json5/-/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

kill-port@^2.0.1:
  version "2.0.1"
  resolved "https://npm.corp.kuaishou.com/kill-port/-/kill-port-2.0.1.tgz#e5e18e2706b13d54320938be42cb7d40609b15cf"
  integrity sha512-e0SVOV5jFo0mx8r7bS29maVWp17qGqLBZ5ricNSajON6//kmb7qqqNnml4twNE8Dtj97UQD+gNFOaipS/q1zzQ==
  dependencies:
    get-them-args "1.3.2"
    shell-exec "1.0.2"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "https://npm.corp.kuaishou.com/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==
  dependencies:
    is-buffer "^1.1.5"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
  integrity sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ==

loader-utils@2.0.4:
  version "2.0.4"
  resolved "https://npm.corp.kuaishou.com/loader-utils/-/loader-utils-2.0.4.tgz#8b5cb38b5c34a9a018ee1fc0e6a066d1dfcc528c"
  integrity sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://npm.corp.kuaishou.com/locate-path/-/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://npm.corp.kuaishou.com/lodash-es/-/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://npm.corp.kuaishou.com/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"
  integrity sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://npm.corp.kuaishou.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.isarray@^4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/lodash.isarray/-/lodash.isarray-4.0.0.tgz#2aca496b28c4ca6d726715313590c02e6ea34403"
  integrity sha512-V8ViWvoNlXpCrB6Ewaj3ScRXUpmCvqp4tJUxa3dlovuJj/8lp3SND5Kw4v5OeuHgoyw4qJN+gl36qZqp6WYQ6g==

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://npm.corp.kuaishou.com/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz#6c2e171db2a257cd96802fd43b01b20d5f5870f6"
  integrity sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "https://npm.corp.kuaishou.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"
  integrity sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==

lodash.isnil@^4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/lodash.isnil/-/lodash.isnil-4.0.0.tgz#49e28cd559013458c814c5479d3c663a21bfaa6c"
  integrity sha512-up2Mzq3545mwVnMhTDMdfoG1OurpA/s5t88JmQX809eH3C8491iu2sfKhTfhQtKY78oPNhiaHJUpT/dUDAAtng==

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://npm.corp.kuaishou.com/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz#3ce76810c5928d03352301ac287317f11c0b1ffc"
  integrity sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==

lodash.isobject@^3.0.2:
  version "3.0.2"
  resolved "https://npm.corp.kuaishou.com/lodash.isobject/-/lodash.isobject-3.0.2.tgz#3c8fb8d5b5bf4bf90ae06e14f2a530a4ed935e1d"
  integrity sha512-3/Qptq2vr7WeJbB4KHUSKlq8Pl7ASXi3UG6CMbBm8WRtXi8+GHm7mKaU3urfpSEzWe2wCIChs6/sdocUsTKJiA==

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://npm.corp.kuaishou.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz#7c526a52d89b45c45cc690b88163be0497f550cb"
  integrity sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://npm.corp.kuaishou.com/lodash.isstring/-/lodash.isstring-4.0.1.tgz#d527dfb5456eca7cc9bb95d5daeaf88ba54a5451"
  integrity sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://npm.corp.kuaishou.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==

lodash.tonumber@^4.0.3:
  version "4.0.3"
  resolved "https://npm.corp.kuaishou.com/lodash.tonumber/-/lodash.tonumber-4.0.3.tgz#0b96b31b35672793eb7f5a63ee791f1b9e9025d9"
  integrity sha512-SY0SwuPOHRwKcCNTdsntPYb+Zddz5mDUIVFABzRMqmAiL41pMeyoQFGxYAw5zdc9NnH4pbJqiqqp5ckfxa+zSA==

lodash@^4.17.11, lodash@^4.17.15, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://npm.corp.kuaishou.com/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

longest@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/longest/-/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"
  integrity sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg==

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://npm.corp.kuaishou.com/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://npm.corp.kuaishou.com/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

md5.js@^1.3.4:
  version "1.3.5"
  resolved "https://npm.corp.kuaishou.com/md5.js/-/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://npm.corp.kuaishou.com/mdn-data/-/mdn-data-2.0.14.tgz#7113fc4281917d63ce29b43446f701e68c25ba50"
  integrity sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==

"memoize-one@>=3.1.1 <6":
  version "5.2.1"
  resolved "https://npm.corp.kuaishou.com/memoize-one/-/memoize-one-5.2.1.tgz#8337aa3c4335581839ec01c3d594090cebe8f00e"
  integrity sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://npm.corp.kuaishou.com/memoize-one/-/memoize-one-6.0.0.tgz#b2591b871ed82948aee4727dc6abceeeac8c1045"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://npm.corp.kuaishou.com/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "https://npm.corp.kuaishou.com/miller-rabin/-/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://npm.corp.kuaishou.com/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://npm.corp.kuaishou.com/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://npm.corp.kuaishou.com/minimatch/-/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.0, minimist@~1.2.8:
  version "1.2.8"
  resolved "https://npm.corp.kuaishou.com/minimist/-/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

ml-array-max@^1.2.4:
  version "1.2.4"
  resolved "https://npm.corp.kuaishou.com/ml-array-max/-/ml-array-max-1.2.4.tgz#2373e2b7e51c8807e456cc0ef364c5863713623b"
  integrity sha512-BlEeg80jI0tW6WaPyGxf5Sa4sqvcyY6lbSn5Vcv44lp1I2GR6AWojfUvLnGTNsIXrZ8uqWmo8VcG1WpkI2ONMQ==
  dependencies:
    is-any-array "^2.0.0"

ml-array-min@^1.2.3:
  version "1.2.3"
  resolved "https://npm.corp.kuaishou.com/ml-array-min/-/ml-array-min-1.2.3.tgz#662f027c400105816b849cc3cd786915d0801495"
  integrity sha512-VcZ5f3VZ1iihtrGvgfh/q0XlMobG6GQ8FsNyQXD3T+IlstDv85g8kfV0xUG1QPRO/t21aukaJowDzMTc7j5V6Q==
  dependencies:
    is-any-array "^2.0.0"

ml-array-rescale@^1.3.7:
  version "1.3.7"
  resolved "https://npm.corp.kuaishou.com/ml-array-rescale/-/ml-array-rescale-1.3.7.tgz#c4d129320d113a732e62dd963dc1695bba9a5340"
  integrity sha512-48NGChTouvEo9KBctDfHC3udWnQKNKEWN0ziELvY3KG25GR5cA8K8wNVzracsqSW1QEkAXjTNx+ycgAv06/1mQ==
  dependencies:
    is-any-array "^2.0.0"
    ml-array-max "^1.2.4"
    ml-array-min "^1.2.3"

ml-matrix@^6.10.4:
  version "6.12.1"
  resolved "https://npm.corp.kuaishou.com/ml-matrix/-/ml-matrix-6.12.1.tgz#8fc99365f7294d27076f0ce405b589e1c04ca1b0"
  integrity sha512-TJ+8eOFdp+INvzR4zAuwBQJznDUfktMtOB6g/hUcGh3rcyjxbz4Te57Pgri8Q9bhSQ7Zys4IYOGhFdnlgeB6Lw==
  dependencies:
    is-any-array "^2.0.1"
    ml-array-rescale "^1.3.7"

mobx-react-lite@^4.0.5:
  version "4.1.0"
  resolved "https://npm.corp.kuaishou.com/mobx-react-lite/-/mobx-react-lite-4.1.0.tgz#6a03ed2d94150848213cfebd7d172e123528a972"
  integrity sha512-QEP10dpHHBeQNv1pks3WnHRCem2Zp636lq54M2nKO2Sarr13pL4u6diQXf65yzXUn0mkk18SyIDCm9UOJYTi1w==
  dependencies:
    use-sync-external-store "^1.4.0"

mobx@^6.10.1, mobx@^6.10.2:
  version "6.13.6"
  resolved "https://npm.corp.kuaishou.com/mobx/-/mobx-6.13.6.tgz#3b80895c7c9df456efc86ae0b6983ccea1da6cc6"
  integrity sha512-r19KNV0uBN4b+ER8Z0gA4y+MzDYIQ2SvOmn3fUrqPnWXdQfakd9yfbPBDBF/p5I+bd3N5Rk1fHONIvMay+bJGA==

mock-json-schema@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/mock-json-schema/-/mock-json-schema-1.1.1.tgz#35cf35ae16e519986ff83c22b4a886a8fe5b9ba5"
  integrity sha512-YV23vlsLP1EEOy0EviUvZTluXjLR+rhMzeayP2rcDiezj3RW01MhOSQkbQskdtg0K2fnGas5LKbSXgNjAOSX4A==
  dependencies:
    lodash "^4.17.21"

mock-property@~1.0.0:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/mock-property/-/mock-property-1.0.3.tgz#3e37c50a56609d548cabd56559fde3dd8767b10c"
  integrity sha512-2emPTb1reeLLYwHxyVx993iYyCHEiRRO+y8NFXFPL5kl5q14sgTK76cXyEKkeKCHeRw35SfdkUJ10Q1KfHuiIQ==
  dependencies:
    define-data-property "^1.1.1"
    functions-have-names "^1.2.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"
    hasown "^2.0.0"
    isarray "^2.0.5"

moment@^2.24.0, moment@^2.25.3, moment@^2.30.1:
  version "2.30.1"
  resolved "https://npm.corp.kuaishou.com/moment/-/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

monaco-editor@^0.52.2:
  version "0.52.2"
  resolved "https://npm.corp.kuaishou.com/monaco-editor/-/monaco-editor-0.52.2.tgz#53c75a6fcc6802684e99fd1b2700299857002205"
  integrity sha512-GEQWEZmfkOGLdd3XK8ryrfWz3AIP8YymVXiPHEdewrUq7mh0qrKrfHLNCXcbB6sTnMLnOZ3ztSiKcciFUkIJwQ==

ms@^2.1.3:
  version "2.1.3"
  resolved "https://npm.corp.kuaishou.com/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

nano-css@^5.6.2:
  version "5.6.2"
  resolved "https://npm.corp.kuaishou.com/nano-css/-/nano-css-5.6.2.tgz#584884ddd7547278f6d6915b6805069742679a32"
  integrity sha512-+6bHaC8dSDGALM1HJjOHVXpuastdu2xFoZlC77Jh4cg+33Zcgm+Gxd+1xsnpZK14eyHObSp82+ll5y3SX75liw==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"
    css-tree "^1.1.2"
    csstype "^3.1.2"
    fastest-stable-stringify "^2.0.2"
    inline-style-prefixer "^7.0.1"
    rtl-css-js "^1.16.1"
    stacktrace-js "^2.0.2"
    stylis "^4.3.0"

nanoid@^3.3.1, nanoid@^3.3.6:
  version "3.3.8"
  resolved "https://npm.corp.kuaishou.com/nanoid/-/nanoid-3.3.8.tgz#b1be3030bee36aaff18bacb375e5cce521684baf"
  integrity sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==

nanoid@^3.3.4, nanoid@^3.3.7:
  version "3.3.11"
  resolved "https://npm.corp.kuaishou.com/nanoid/-/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "https://npm.corp.kuaishou.com/node-fetch/-/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
  integrity sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://npm.corp.kuaishou.com/node-releases/-/node-releases-2.0.19.tgz#9e445a52950951ec4d177d843af370b411caf314"
  integrity sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==

node-stdlib-browser@1.3.0:
  version "1.3.0"
  resolved "https://npm.corp.kuaishou.com/node-stdlib-browser/-/node-stdlib-browser-1.3.0.tgz#6e04d149f9abfc345a2271b2820a75df0f9cd7de"
  integrity sha512-g/koYzOr9Fb1Jc+tHUHlFd5gODjGn48tHexUK8q6iqOVriEgSnd3/1T7myBYc+0KBVze/7F7n65ec9rW6OD7xw==
  dependencies:
    assert "^2.0.0"
    browser-resolve "^2.0.0"
    browserify-zlib "^0.2.0"
    buffer "^5.7.1"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    create-require "^1.1.1"
    crypto-browserify "^3.11.0"
    domain-browser "4.22.0"
    events "^3.0.0"
    https-browserify "^1.0.0"
    isomorphic-timers-promises "^1.0.1"
    os-browserify "^0.3.0"
    path-browserify "^1.0.1"
    pkg-dir "^5.0.0"
    process "^0.11.10"
    punycode "^1.4.1"
    querystring-es3 "^0.2.1"
    readable-stream "^3.6.0"
    stream-browserify "^3.0.0"
    stream-http "^3.2.0"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.1"
    url "^0.11.4"
    util "^0.12.4"
    vm-browserify "^1.0.1"

object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://npm.corp.kuaishou.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-inspect@^1.13.3:
  version "1.13.4"
  resolved "https://npm.corp.kuaishou.com/object-inspect/-/object-inspect-1.13.4.tgz#8375265e21bc20d0fa582c22e1b13485d6e00213"
  integrity sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==

object-inspect@~1.12.3:
  version "1.12.3"
  resolved "https://npm.corp.kuaishou.com/object-inspect/-/object-inspect-1.12.3.tgz#ba62dffd67ee256c8c086dfae69e016cd1f198b9"
  integrity sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==

object-is@^1.1.5:
  version "1.1.6"
  resolved "https://npm.corp.kuaishou.com/object-is/-/object-is-1.1.6.tgz#1a6a53aed2dd8f7e6775ff870bea58545956ab07"
  integrity sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "https://npm.corp.kuaishou.com/object.assign/-/object.assign-4.1.7.tgz#8c14ca1a424c6a561b0bb2a22f66f5049a945d3d"
  integrity sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

on-exit-leak-free@^0.2.0:
  version "0.2.0"
  resolved "https://npm.corp.kuaishou.com/on-exit-leak-free/-/on-exit-leak-free-0.2.0.tgz#b39c9e3bf7690d890f4861558b0d7b90a442d209"
  integrity sha512-dqaz3u44QbRXQooZLTUKU41ZrzYrcvLISVgbrzbyCMxpmSLJvZ3ZamIJIZ29P6OhZIkNIQKosdeM6t1LYbA9hg==

once@^1.3.0, once@^1.4.0:
  version "1.4.0"
  resolved "https://npm.corp.kuaishou.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "https://npm.corp.kuaishou.com/os-browserify/-/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A==

own-keys@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/own-keys/-/own-keys-1.0.1.tgz#e4006910a2bf913585289676eebd6f390cf51358"
  integrity sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

oxc-resolver@1.12.0:
  version "1.12.0"
  resolved "https://npm.corp.kuaishou.com/oxc-resolver/-/oxc-resolver-1.12.0.tgz#192aee832fd9a9a2a6e268b4e2e48b56ffeb2f80"
  integrity sha512-YlaCIArvWNKCWZFRrMjhh2l5jK80eXnpYP+bhRc1J/7cW3TiyEY0ngJo73o/5n8hA3+4yLdTmXLNTQ3Ncz50LQ==
  optionalDependencies:
    "@oxc-resolver/binding-darwin-arm64" "1.12.0"
    "@oxc-resolver/binding-darwin-x64" "1.12.0"
    "@oxc-resolver/binding-freebsd-x64" "1.12.0"
    "@oxc-resolver/binding-linux-arm-gnueabihf" "1.12.0"
    "@oxc-resolver/binding-linux-arm64-gnu" "1.12.0"
    "@oxc-resolver/binding-linux-arm64-musl" "1.12.0"
    "@oxc-resolver/binding-linux-x64-gnu" "1.12.0"
    "@oxc-resolver/binding-linux-x64-musl" "1.12.0"
    "@oxc-resolver/binding-wasm32-wasi" "1.12.0"
    "@oxc-resolver/binding-win32-arm64-msvc" "1.12.0"
    "@oxc-resolver/binding-win32-x64-msvc" "1.12.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/p-limit/-/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://npm.corp.kuaishou.com/p-locate/-/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

pako@~1.0.5:
  version "1.0.11"
  resolved "https://npm.corp.kuaishou.com/pako/-/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==

parse-asn1@^5.0.0, parse-asn1@^5.1.7:
  version "5.1.7"
  resolved "https://npm.corp.kuaishou.com/parse-asn1/-/parse-asn1-5.1.7.tgz#73cdaaa822125f9647165625eb45f8a051d2df06"
  integrity sha512-CTM5kuWR3sx9IFamcl5ErfPl6ea/N8IYwiJ+vpeB2g+1iknv7zBl5uPwbMbRVznRVbrNY6lGuDoE5b30grmbqg==
  dependencies:
    asn1.js "^4.10.1"
    browserify-aes "^1.2.0"
    evp_bytestokey "^1.0.3"
    hash-base "~3.0"
    pbkdf2 "^3.1.2"
    safe-buffer "^5.2.1"

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/path-browserify/-/path-browserify-1.0.1.tgz#d98454a9c3753d5790860f16f68867b9e46be1fd"
  integrity sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://npm.corp.kuaishou.com/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

pbkdf2@^3.1.2:
  version "3.1.2"
  resolved "https://npm.corp.kuaishou.com/pbkdf2/-/pbkdf2-3.1.2.tgz#dd822aa0887580e52f1a039dc3eda108efae3075"
  integrity sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA==
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

pdfast@^0.2.0:
  version "0.2.0"
  resolved "https://npm.corp.kuaishou.com/pdfast/-/pdfast-0.2.0.tgz#8cbc556e1bf2522177787c0de2e0d4373ba885c9"
  integrity sha512-cq6TTu6qKSFUHwEahi68k/kqN2mfepjkGrG9Un70cgdRRKLKY6Rf8P8uvP2NvZktaQZNF3YE7agEkLj0vGK9bA==

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/picocolors/-/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.2.3:
  version "2.3.1"
  resolved "https://npm.corp.kuaishou.com/picomatch/-/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pino-abstract-transport@v0.5.0:
  version "0.5.0"
  resolved "https://npm.corp.kuaishou.com/pino-abstract-transport/-/pino-abstract-transport-0.5.0.tgz#4b54348d8f73713bfd14e3dc44228739aa13d9c0"
  integrity sha512-+KAgmVeqXYbTtU2FScx1XS3kNyfZ5TrXY07V96QnUSFqo2gAqlvmaxH67Lj7SWazqsMabf+58ctdTcBgnOLUOQ==
  dependencies:
    duplexify "^4.1.2"
    split2 "^4.0.0"

pino-std-serializers@^4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/pino-std-serializers/-/pino-std-serializers-4.0.0.tgz#1791ccd2539c091ae49ce9993205e2cd5dbba1e2"
  integrity sha512-cK0pekc1Kjy5w9V2/n+8MkZwusa6EyyxfeQCB799CQRhRt/CqYKiWs5adeu8Shve2ZNffvfC/7J64A2PJo1W/Q==

pino@7.11.0:
  version "7.11.0"
  resolved "https://npm.corp.kuaishou.com/pino/-/pino-7.11.0.tgz#0f0ea5c4683dc91388081d44bff10c83125066f6"
  integrity sha512-dMACeu63HtRLmCG8VKdy4cShCPKaYDR4youZqoSWLxl5Gu99HUw8bw75thbPv9Nip+H+QYX8o3ZJbTdVZZ2TVg==
  dependencies:
    atomic-sleep "^1.0.0"
    fast-redact "^3.0.0"
    on-exit-leak-free "^0.2.0"
    pino-abstract-transport v0.5.0
    pino-std-serializers "^4.0.0"
    process-warning "^1.0.0"
    quick-format-unescaped "^4.0.3"
    real-require "^0.1.0"
    safe-stable-stringify "^2.1.0"
    sonic-boom "^2.2.1"
    thread-stream "^0.15.1"

piscina@4.7.0:
  version "4.7.0"
  resolved "https://npm.corp.kuaishou.com/piscina/-/piscina-4.7.0.tgz#68936fc77128db00541366531330138e366dc851"
  integrity sha512-b8hvkpp9zS0zsfa939b/jXbe64Z2gZv0Ha7FYPNUiDIB1y2AtxcOZdfP8xN8HFjUaqQiT9gRlfjAsoL8vdJ1Iw==
  optionalDependencies:
    "@napi-rs/nice" "^1.0.1"

pkg-dir@^5.0.0:
  version "5.0.0"
  resolved "https://npm.corp.kuaishou.com/pkg-dir/-/pkg-dir-5.0.0.tgz#a02d6aebe6ba133a928f74aec20bafdfe6b8e760"
  integrity sha512-NPE8TDbzl/3YQYY7CSS228s3g2ollTFnc+Qi3tqmqJp9Vg2ovUpixcJEo2HJScN2Ez+kEaal6y70c0ehqJBJeA==
  dependencies:
    find-up "^5.0.0"

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz#93e3582bc0e5426586d9d07b79ee40fc841de4ae"
  integrity sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==

postcss-value-parser@^4.0.2:
  version "4.2.0"
  resolved "https://npm.corp.kuaishou.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@8.4.21:
  version "8.4.21"
  resolved "https://npm.corp.kuaishou.com/postcss/-/postcss-8.4.21.tgz#c639b719a57efc3187b13a1d765675485f4134f4"
  integrity sha512-tP7u/Sn/dVxK2NnruI4H9BG+x+Wxz6oeZ1cJ8P6G/PZY0IKk4k/63TDsQf2kQq3+qoJeLm2kIBUNlZe3zgb4Zg==
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

postcss@8.4.49:
  version "8.4.49"
  resolved "https://npm.corp.kuaishou.com/postcss/-/postcss-8.4.49.tgz#4ea479048ab059ab3ae61d082190fabfd994fe19"
  integrity sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://npm.corp.kuaishou.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

process-warning@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/process-warning/-/process-warning-1.0.0.tgz#980a0b25dc38cd6034181be4b7726d89066b4616"
  integrity sha512-du4wfLyj4yCZq1VupnVSZmRsPJsNuxoDQFdCFHLaYiEbFBD7QE0a+I4D7hOxrVnh78QE/YipFAj9lXHiXocV+Q==

process@^0.11.10:
  version "0.11.10"
  resolved "https://npm.corp.kuaishou.com/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==

promise-polyfill@^8.2.1:
  version "8.3.0"
  resolved "https://npm.corp.kuaishou.com/promise-polyfill/-/promise-polyfill-8.3.0.tgz#9284810268138d103807b11f4e23d5e945a4db63"
  integrity sha512-H5oELycFml5yto/atYqmjyigJoAo3+OXwolYiH7OfQuYlAqhxNvTfiNMbV9hsC6Yp83yE5r2KTVmtrG6R9i6Pg==

promise.any@^2.0.3:
  version "2.0.6"
  resolved "https://npm.corp.kuaishou.com/promise.any/-/promise.any-2.0.6.tgz#e234bf0c5250368a580f2ae1fbd482e27d3a89b1"
  integrity sha512-Ew/MrPtTjiHnnki0AA2hS2o65JaZ5n+5pp08JSyWWUdeOGF4F41P+Dn+rdqnaOV/FTxhR6eBDX412luwn3th9g==
  dependencies:
    array.prototype.map "^1.0.5"
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-aggregate-error "^1.0.10"
    get-intrinsic "^1.2.1"
    iterate-value "^1.0.2"

promise@^7.1.1:
  version "7.3.1"
  resolved "https://npm.corp.kuaishou.com/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==
  dependencies:
    asap "~2.0.3"

prop-types@15.x, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://npm.corp.kuaishou.com/prop-types/-/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

public-encrypt@^4.0.3:
  version "4.0.3"
  resolved "https://npm.corp.kuaishou.com/public-encrypt/-/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

punycode@^1.4.1:
  version "1.4.1"
  resolved "https://npm.corp.kuaishou.com/punycode/-/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==

qrcode.react@^3.1.0:
  version "3.2.0"
  resolved "https://npm.corp.kuaishou.com/qrcode.react/-/qrcode.react-3.2.0.tgz#97daabd4ff641a3f3c678f87be106ebc55f9cd07"
  integrity sha512-YietHHltOHA4+l5na1srdaMx4sVSOjV9tamHs+mwiLWAMr6QVACRUw1Neax5CptFILcNoITctJY0Ipyn5enQ8g==

qs@^6.11.2, qs@^6.12.3:
  version "6.14.0"
  resolved "https://npm.corp.kuaishou.com/qs/-/qs-6.14.0.tgz#c63fa40680d2c5c941412a0e899c89af60c0a930"
  integrity sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==
  dependencies:
    side-channel "^1.1.0"

querystring-es3@^0.2.1:
  version "0.2.1"
  resolved "https://npm.corp.kuaishou.com/querystring-es3/-/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA==

quick-format-unescaped@^4.0.3:
  version "4.0.4"
  resolved "https://npm.corp.kuaishou.com/quick-format-unescaped/-/quick-format-unescaped-4.0.4.tgz#93ef6dd8d3453cbc7970dd614fad4c5954d6b5a7"
  integrity sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==

quickselect@^2.0.0:
  version "2.0.0"
  resolved "https://npm.corp.kuaishou.com/quickselect/-/quickselect-2.0.0.tgz#f19680a486a5eefb581303e023e98faaf25dd018"
  integrity sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5, randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://npm.corp.kuaishou.com/randombytes/-/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.4:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/randomfill/-/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

rbush@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/rbush/-/rbush-3.0.1.tgz#5fafa8a79b3b9afdfe5008403a720cc1de882ecf"
  integrity sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==
  dependencies:
    quickselect "^2.0.0"

rc-align@^4.0.0:
  version "4.0.15"
  resolved "https://npm.corp.kuaishou.com/rc-align/-/rc-align-4.0.15.tgz#2bbd665cf85dfd0b0244c5a752b07565e9098577"
  integrity sha512-wqJtVH60pka/nOX7/IspElA8gjPNQKIx/ZqJ6heATCkXpe1Zg4cPVrMD2vC96wjsFFL8WsmhPbx9tdMo1qqlIA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    dom-align "^1.7.0"
    rc-util "^5.26.0"
    resize-observer-polyfill "^1.5.1"

rc-cascader@~3.33.0:
  version "3.33.0"
  resolved "https://npm.corp.kuaishou.com/rc-cascader/-/rc-cascader-3.33.0.tgz#acdeafebbdf7f7296f4d84980d02cf0835f93910"
  integrity sha512-JvZrMbKBXIbEDmpIORxqvedY/bck6hGbs3hxdWT8eS9wSQ1P7//lGxbyKjOSyQiVBbgzNWriSe6HoMcZO/+0rQ==
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "^2.3.1"
    rc-select "~14.16.2"
    rc-tree "~5.13.0"
    rc-util "^5.43.0"

rc-cascader@~3.6.0:
  version "3.6.2"
  resolved "https://npm.corp.kuaishou.com/rc-cascader/-/rc-cascader-3.6.2.tgz#2b5c108807234898cd9a0366d0626f786b7b5622"
  integrity sha512-sf2otpazlROTzkD3nZVfIzXmfBLiEOBTXA5wxozGXBpS902McDpvF0bdcYBu5hN+rviEAm6Mh9cLXNQ1Ty8wKQ==
  dependencies:
    "@babel/runtime" "^7.12.5"
    array-tree-filter "^2.1.0"
    classnames "^2.3.1"
    rc-select "~14.1.0"
    rc-tree "~5.6.3"
    rc-util "^5.6.1"

rc-checkbox@~2.3.0:
  version "2.3.2"
  resolved "https://npm.corp.kuaishou.com/rc-checkbox/-/rc-checkbox-2.3.2.tgz#f91b3678c7edb2baa8121c9483c664fa6f0aefc1"
  integrity sha512-afVi1FYiGv1U0JlpNH/UaEXdh6WUJjcWokj/nUN2TgG80bfG+MDdbfHKlLcNNba94mbjy2/SXJ1HDgrOkXGAjg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"

rc-checkbox@~3.5.0:
  version "3.5.0"
  resolved "https://npm.corp.kuaishou.com/rc-checkbox/-/rc-checkbox-3.5.0.tgz#3ae2441e3a321774d390f76539e706864fcf5ff0"
  integrity sha512-aOAQc3E98HteIIsSqm6Xk2FPKIER6+5vyEFMZfo73TqM+VVAIqOkHoPjgKLqSNtVLWScoaM7vY2ZrGEheI79yg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.25.2"

rc-collapse@~3.1.0:
  version "3.1.4"
  resolved "https://npm.corp.kuaishou.com/rc-collapse/-/rc-collapse-3.1.4.tgz#063e33fcc427a378e63da757898cd1fba6269679"
  integrity sha512-WayrhswKMwuJab9xbqFxXTgV0m6X8uOPEO6zm/GJ5YJiJ/wIh/Dd2VtWeI06HYUEnTFv0HNcYv+zWbB+p6OD2A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.3.4"
    rc-util "^5.2.1"
    shallowequal "^1.1.0"

rc-collapse@~3.9.0:
  version "3.9.0"
  resolved "https://npm.corp.kuaishou.com/rc-collapse/-/rc-collapse-3.9.0.tgz#972404ce7724e1c9d1d2476543e1175404a36806"
  integrity sha512-swDdz4QZ4dFTo4RAUMLL50qP0EY62N2kvmk2We5xYdRwcRn8WcYtuetCJpwpaCbUfUt5+huLpVxhvmnK+PHrkA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.3.4"
    rc-util "^5.27.0"

rc-dialog@~8.6.0:
  version "8.6.0"
  resolved "https://npm.corp.kuaishou.com/rc-dialog/-/rc-dialog-8.6.0.tgz#3b228dac085de5eed8c6237f31162104687442e7"
  integrity sha512-GSbkfqjqxpZC5/zc+8H332+q5l/DKUhpQr0vdX2uDsxo5K0PhvaMEVjyoJUTkZ3+JstEADQji1PVLVb/2bJeOQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-motion "^2.3.0"
    rc-util "^5.6.1"

rc-dialog@~9.6.0:
  version "9.6.0"
  resolved "https://npm.corp.kuaishou.com/rc-dialog/-/rc-dialog-9.6.0.tgz#dc7a255c6ad1cb56021c3a61c7de86ee88c7c371"
  integrity sha512-ApoVi9Z8PaCQg6FsUzS8yvBEQy0ZL2PkuvAgrmohPkN3okps5WZ5WQWPc1RNuiOKaAYv8B97ACdsFU5LizzCqg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-8"
    classnames "^2.2.6"
    rc-motion "^2.3.0"
    rc-util "^5.21.0"

rc-drawer@~4.4.2:
  version "4.4.3"
  resolved "https://npm.corp.kuaishou.com/rc-drawer/-/rc-drawer-4.4.3.tgz#2094937a844e55dc9644236a2d9fba79c344e321"
  integrity sha512-FYztwRs3uXnFOIf1hLvFxIQP9MiZJA+0w+Os8dfDh/90X7z/HqP/Yg+noLCIeHEbKln1Tqelv8ymCAN24zPcfQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.7.0"

rc-drawer@~7.2.0:
  version "7.2.0"
  resolved "https://npm.corp.kuaishou.com/rc-drawer/-/rc-drawer-7.2.0.tgz#8d7de2f1fd52f3ac5a25f54afbb8ac14c62e5663"
  integrity sha512-9lOQ7kBekEJRdEpScHvtmEtXnAsy+NGDXiRWc2ZVC7QXAazNVbeT4EraQKYwCME8BJLa8Bxqxvs5swwyOepRwg==
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@rc-component/portal" "^1.1.1"
    classnames "^2.2.6"
    rc-motion "^2.6.1"
    rc-util "^5.38.1"

rc-dropdown@^3.2.0:
  version "3.6.2"
  resolved "https://npm.corp.kuaishou.com/rc-dropdown/-/rc-dropdown-3.6.2.tgz#d23b8b2762941ac39e665673946f67ca9c39118f"
  integrity sha512-Wsw7GkVbUXADEs8FPL0v8gd+3mWQiydPFXBlr2imMScQaf8hh79pG9KrBc1DwK+nqHmYOpQfK2gn6jG2AQw9Pw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-trigger "^5.0.4"
    rc-util "^5.17.0"

rc-dropdown@~3.2.0:
  version "3.2.5"
  resolved "https://npm.corp.kuaishou.com/rc-dropdown/-/rc-dropdown-3.2.5.tgz#c211e571d29d15e7f725b5a75fc8c7f371fc3348"
  integrity sha512-dVO2eulOSbEf+F4OyhCY5iGiMVhUYY/qeXxL7Ex2jDBt/xc89jU07mNoowV6aWxwVOc70pxEINff0oM2ogjluA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-trigger "^5.0.4"

rc-dropdown@~4.2.0, rc-dropdown@~4.2.1:
  version "4.2.1"
  resolved "https://npm.corp.kuaishou.com/rc-dropdown/-/rc-dropdown-4.2.1.tgz#44729eb2a4272e0353d31ac060da21e606accb1c"
  integrity sha512-YDAlXsPv3I1n42dv1JpdM7wJ+gSUBfeyPK59ZpBD9jQhK9jVuxpjj3NmWQHOBceA1zEPVX84T2wbdb2SD0UjmA==
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-util "^5.44.1"

rc-field-form@~1.27.0:
  version "1.27.4"
  resolved "https://npm.corp.kuaishou.com/rc-field-form/-/rc-field-form-1.27.4.tgz#53600714af5b28c226c70d34867a8c52ccd64d44"
  integrity sha512-PQColQnZimGKArnOh8V2907+VzDCXcqtFvHgevDLtqWc/P7YASb/FqntSmdS8q3VND5SHX3Y1vgMIzY22/f/0Q==
  dependencies:
    "@babel/runtime" "^7.18.0"
    async-validator "^4.1.0"
    rc-util "^5.8.0"

rc-field-form@~2.7.0:
  version "2.7.0"
  resolved "https://npm.corp.kuaishou.com/rc-field-form/-/rc-field-form-2.7.0.tgz#22413e793f35bfc1f35b0ec462774d7277f5a399"
  integrity sha512-hgKsCay2taxzVnBPZl+1n4ZondsV78G++XVsMIJCAoioMjlMQR9YwAp7JZDIECzIu2Z66R+f4SFIRrO2DjDNAA==
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/async-validator" "^5.0.3"
    rc-util "^5.32.2"

rc-image@~5.2.5:
  version "5.2.5"
  resolved "https://npm.corp.kuaishou.com/rc-image/-/rc-image-5.2.5.tgz#44e6ffc842626827960e7ab72e1c0d6f3a8ce440"
  integrity sha512-qUfZjYIODxO0c8a8P5GeuclYXZjzW4hV/5hyo27XqSFo1DmTCs2HkVeQObkcIk5kNsJtgsj1KoPThVsSc/PXOw==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "^2.2.6"
    rc-dialog "~8.6.0"
    rc-util "^5.0.6"

rc-image@~7.11.0:
  version "7.11.0"
  resolved "https://npm.corp.kuaishou.com/rc-image/-/rc-image-7.11.0.tgz#18c77ea557a6fdbe26856c688a9aace1505c0e77"
  integrity sha512-aZkTEZXqeqfPZtnSdNUnKQA0N/3MbgR7nUnZ+/4MfSFWPFHZau4p5r5ShaI0KPEMnNjv4kijSCFq/9wtJpwykw==
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/portal" "^1.0.2"
    classnames "^2.2.6"
    rc-dialog "~9.6.0"
    rc-motion "^2.6.2"
    rc-util "^5.34.1"

rc-input-number@~7.3.0:
  version "7.3.11"
  resolved "https://npm.corp.kuaishou.com/rc-input-number/-/rc-input-number-7.3.11.tgz#c7089705a220e1a59ba974fabf89693e00dd2442"
  integrity sha512-aMWPEjFeles6PQnMqP5eWpxzsvHm9rh1jQOWXExUEIxhX62Fyl/ptifLHOn17+waDG1T/YUb6flfJbvwRhHrbA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.23.0"

rc-input-number@~9.4.0:
  version "9.4.0"
  resolved "https://npm.corp.kuaishou.com/rc-input-number/-/rc-input-number-9.4.0.tgz#65caf04f1b6d05f47e141b1f5f484724c1f7fd5a"
  integrity sha512-Tiy4DcXcFXAf9wDhN8aUAyMeCLHJUHA/VA/t7Hj8ZEx5ETvxG7MArDOSE6psbiSCo+vJPm4E3fGN710ITVn6GA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/mini-decimal" "^1.0.1"
    classnames "^2.2.5"
    rc-input "~1.7.1"
    rc-util "^5.40.1"

rc-input@~1.7.1, rc-input@~1.7.2:
  version "1.7.2"
  resolved "https://npm.corp.kuaishou.com/rc-input/-/rc-input-1.7.2.tgz#a41d5ca14021475d3998deb09630ee46268610af"
  integrity sha512-g3nYONnl4edWj2FfVoxsU3Ec4XTE+Hb39Kfh2MFxMZjp/0gGyPUgy/v7ZhS27ZxUFNkuIDYXm9PJsLyJbtg86A==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.18.1"

rc-mentions@~1.6.1:
  version "1.6.5"
  resolved "https://npm.corp.kuaishou.com/rc-mentions/-/rc-mentions-1.6.5.tgz#d9516abd19a757c674df1c88a3459628fe95a149"
  integrity sha512-CUU4+q+awG2pA0l/tG2kPB2ytWbKQUkFxVeKwacr63w7crE/yjfzrFXxs/1fxhyEbQUWdAZt/L25QBieukYQ5w==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-menu "~9.3.2"
    rc-textarea "^0.3.0"
    rc-trigger "^5.0.4"
    rc-util "^5.0.1"

rc-mentions@~2.19.1:
  version "2.19.1"
  resolved "https://npm.corp.kuaishou.com/rc-mentions/-/rc-mentions-2.19.1.tgz#3fd0dd0bf3dd63afdb6a21750cbae81f3824b9c4"
  integrity sha512-KK3bAc/bPFI993J3necmaMXD2reZTzytZdlTvkeBbp50IGH1BDPDvxLdHDUrpQx2b2TGaVJsn+86BvYa03kGqA==
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-input "~1.7.1"
    rc-menu "~9.16.0"
    rc-textarea "~1.9.0"
    rc-util "^5.34.1"

rc-menu@~9.16.0:
  version "9.16.0"
  resolved "https://npm.corp.kuaishou.com/rc-menu/-/rc-menu-9.16.0.tgz#53647f60f513bfa09bfc1accbd96a8df24900121"
  integrity sha512-vAL0yqPkmXWk3+YKRkmIR8TYj3RVdEt3ptG2jCJXWNAvQbT0VJJdRyHZ7kG/l1JsZlB+VJq/VcYOo69VR4oD+w==
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.0.0"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.3.1"
    rc-util "^5.27.0"

rc-menu@~9.3.2:
  version "9.3.2"
  resolved "https://npm.corp.kuaishou.com/rc-menu/-/rc-menu-9.3.2.tgz#bb842d37ebf71da912bea201cf7ef0a27267ad49"
  integrity sha512-h3m45oY1INZyqphGELkdT0uiPnFzxkML8m0VMhJnk2fowtqfiT7F5tJLT3znEVaPIY80vMy1bClCkgq8U91CzQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.2.0"
    rc-trigger "^5.1.2"
    rc-util "^5.12.0"
    shallowequal "^1.1.0"

rc-menu@~9.8.1:
  version "9.8.4"
  resolved "https://npm.corp.kuaishou.com/rc-menu/-/rc-menu-9.8.4.tgz#58bf19d471e3c74ff4bcfdb0f02a3826ebe2553b"
  integrity sha512-lmw2j8I2fhdIzHmC9ajfImfckt0WDb2KVJJBBRIsxPEw2kGkEfjLMUoB1NgiNT/Q5cC8PdjGOGQjHJIJMwyNMw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.2.8"
    rc-trigger "^5.1.2"
    rc-util "^5.27.0"

rc-motion@^2.0.0, rc-motion@^2.0.1, rc-motion@^2.2.0, rc-motion@^2.3.0, rc-motion@^2.3.4, rc-motion@^2.4.3, rc-motion@^2.4.4, rc-motion@^2.6.1, rc-motion@^2.6.2, rc-motion@^2.9.0, rc-motion@^2.9.5:
  version "2.9.5"
  resolved "https://npm.corp.kuaishou.com/rc-motion/-/rc-motion-2.9.5.tgz#12c6ead4fd355f94f00de9bb4f15df576d677e0c"
  integrity sha512-w+XTUrfh7ArbYEd2582uDrEhmBHwK1ZENJiSJVb7uRxdE7qJSYjbO2eksRXmndqyKqKoYPc9ClpPh5242mV1vA==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.44.0"

rc-notification@~4.5.7:
  version "4.5.7"
  resolved "https://npm.corp.kuaishou.com/rc-notification/-/rc-notification-4.5.7.tgz#265e6e6a0c1a0fac63d6abd4d832eb8ff31522f1"
  integrity sha512-zhTGUjBIItbx96SiRu3KVURcLOydLUHZCPpYEn1zvh+re//Tnq/wSxN4FKgp38n4HOgHSVxcLEeSxBMTeBBDdw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.2.0"
    rc-util "^5.0.1"

rc-notification@~5.6.3:
  version "5.6.3"
  resolved "https://npm.corp.kuaishou.com/rc-notification/-/rc-notification-5.6.3.tgz#5aca122b7e2fb73ae4ba77238a90e6e8c9c12b8d"
  integrity sha512-42szwnn8VYQoT6GnjO00i1iwqV9D1TTMvxObWsuLwgl0TsOokzhkYiufdtQBsJMFjJravS1hfDKVMHLKLcPE4g==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.9.0"
    rc-util "^5.20.1"

rc-overflow@^1.0.0, rc-overflow@^1.2.0, rc-overflow@^1.2.8, rc-overflow@^1.3.1, rc-overflow@^1.3.2:
  version "1.4.1"
  resolved "https://npm.corp.kuaishou.com/rc-overflow/-/rc-overflow-1.4.1.tgz#e1bcf0375979c24cffa2d87bf83a19ded5fcdf45"
  integrity sha512-3MoPQQPV1uKyOMVNd6SZfONi+f3st0r8PksexIdBTeIYbMX0Jr+k7pHEDvsXtR4BpCv90/Pv2MovVNhktKrwvw==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.37.0"

rc-pagination@~3.1.9:
  version "3.1.17"
  resolved "https://npm.corp.kuaishou.com/rc-pagination/-/rc-pagination-3.1.17.tgz#91e690aa894806e344cea88ea4a16d244194a1bd"
  integrity sha512-/BQ5UxcBnW28vFAcP2hfh+Xg15W0QZn8TWYwdCApchMH1H0CxiaUUcULP8uXcFM1TygcdKWdt3JqsL9cTAfdkQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"

rc-pagination@~5.1.0:
  version "5.1.0"
  resolved "https://npm.corp.kuaishou.com/rc-pagination/-/rc-pagination-5.1.0.tgz#a6e63a2c5db29e62f991282eb18a2d3ee725ba8b"
  integrity sha512-8416Yip/+eclTFdHXLKTxZvn70duYVGTvUUWbckCCZoIl3jagqke3GLsFrMs0bsQBikiYpZLD9206Ej4SOdOXQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.38.0"

rc-picker@~2.5.17:
  version "2.5.19"
  resolved "https://npm.corp.kuaishou.com/rc-picker/-/rc-picker-2.5.19.tgz#73d07546fac3992f0bfabf2789654acada39e46f"
  integrity sha512-u6myoCu/qiQ0vLbNzSzNrzTQhs7mldArCpPHrEI6OUiifs+IPXmbesqSm0zilJjfzrZJLgYeyyOMSznSlh0GKA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    date-fns "2.x"
    dayjs "1.x"
    moment "^2.24.0"
    rc-trigger "^5.0.4"
    rc-util "^5.4.0"
    shallowequal "^1.1.0"

rc-picker@~4.11.0:
  version "4.11.1"
  resolved "https://npm.corp.kuaishou.com/rc-picker/-/rc-picker-4.11.1.tgz#fc20848ffc56c5743a10a4d67413738ea536d222"
  integrity sha512-qHaZrHrYjAVwMcKqMXJz9xHifQgQpKSav0E1ejOe3SFTHZggPlmKzLnA5i//Y4DEumR4HZEsePSOdOlmX1JvAw==
  dependencies:
    "@babel/runtime" "^7.24.7"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.1"
    rc-overflow "^1.3.2"
    rc-resize-observer "^1.4.0"
    rc-util "^5.43.0"

rc-progress@~3.1.0:
  version "3.1.4"
  resolved "https://npm.corp.kuaishou.com/rc-progress/-/rc-progress-3.1.4.tgz#66040d0fae7d8ced2b38588378eccb2864bad615"
  integrity sha512-XBAif08eunHssGeIdxMXOmRQRULdHaDdIFENQ578CMb4dyewahmmfJRyab+hw4KH4XssEzzYOkAInTLS7JJG+Q==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"

rc-progress@~4.0.0:
  version "4.0.0"
  resolved "https://npm.corp.kuaishou.com/rc-progress/-/rc-progress-4.0.0.tgz#5382147d9add33d3a5fbd264001373df6440e126"
  integrity sha512-oofVMMafOCokIUIBnZLNcOZFsABaUw8PPrf1/y0ZBvKZNpOiu5h4AO9vv11Sw0p4Hb3D0yGWuEattcQGtNJ/aw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-rate@~2.13.1:
  version "2.13.1"
  resolved "https://npm.corp.kuaishou.com/rc-rate/-/rc-rate-2.13.1.tgz#29af7a3d4768362e9d4388f955a8b6389526b7fd"
  integrity sha512-QUhQ9ivQ8Gy7mtMZPAjLbxBt5y9GRp65VcUyGUMF3N3fhiftivPHdpuDIaWIMOTEprAjZPC08bls1dQB+I1F2Q==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.0.1"

rc-rate@~2.9.0:
  version "2.9.3"
  resolved "https://npm.corp.kuaishou.com/rc-rate/-/rc-rate-2.9.3.tgz#b30a8043ffcb327bab053cd78508e07015d8a483"
  integrity sha512-2THssUSnRhtqIouQIIXqsZGzRczvp4WsH4WvGuhiwm+LG2fVpDUJliP9O1zeDOZvYfBE/Bup4SgHun/eCkbjgQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.0.1"

rc-resize-observer@^1.0.0, rc-resize-observer@^1.1.0, rc-resize-observer@^1.1.2, rc-resize-observer@^1.2.0, rc-resize-observer@^1.3.1, rc-resize-observer@^1.4.0, rc-resize-observer@^1.4.3:
  version "1.4.3"
  resolved "https://npm.corp.kuaishou.com/rc-resize-observer/-/rc-resize-observer-1.4.3.tgz#4fd41fa561ba51362b5155a07c35d7c89a1ea569"
  integrity sha512-YZLjUbyIWox8E9i9C3Tm7ia+W7euPItNWSPX5sCcQTYbnwDb5uNpnLHQCG1f22oZWUhLw4Mv2tFmeWe68CDQRQ==
  dependencies:
    "@babel/runtime" "^7.20.7"
    classnames "^2.2.1"
    rc-util "^5.44.1"
    resize-observer-polyfill "^1.5.1"

rc-segmented@~2.7.0:
  version "2.7.0"
  resolved "https://npm.corp.kuaishou.com/rc-segmented/-/rc-segmented-2.7.0.tgz#f56c2044abf8f03958b3a9a9d32987f10dcc4fc4"
  integrity sha512-liijAjXz+KnTRVnxxXG2sYDGd6iLL7VpGGdR8gwoxAXy2KglviKCxLWZdjKYJzYzGSUwKDSTdYk8brj54Bn5BA==
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-motion "^2.4.4"
    rc-util "^5.17.0"

rc-select@~13.2.1:
  version "13.2.1"
  resolved "https://npm.corp.kuaishou.com/rc-select/-/rc-select-13.2.1.tgz#d69675f8bc72622a8f3bc024fa21bfee8d56257d"
  integrity sha512-L2cJFAjVEeDiNVa/dlOVKE79OUb0J7sUBvWN3Viav3XHcjvv9Ovn4D8J9QhBSlDXeGuczZ81CZI3BbdHD25+Gg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.0.0"
    rc-trigger "^5.0.4"
    rc-util "^5.9.8"
    rc-virtual-list "^3.2.0"

rc-select@~14.1.0:
  version "14.1.18"
  resolved "https://npm.corp.kuaishou.com/rc-select/-/rc-select-14.1.18.tgz#f1d95233132cda9c1485963254255b83e97a37a9"
  integrity sha512-4JgY3oG2Yz68ECMUSCON7mtxuJvCSj+LJpHEg/AONaaVBxIIrmI/ZTuMJkyojall/X50YdBe5oMKqHHPNiPzEg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.0.0"
    rc-trigger "^5.0.4"
    rc-util "^5.16.1"
    rc-virtual-list "^3.2.0"

rc-select@~14.16.2, rc-select@~14.16.6:
  version "14.16.6"
  resolved "https://npm.corp.kuaishou.com/rc-select/-/rc-select-14.16.6.tgz#1c57a9aa97248b3fd9a830d9bf5df6e9d2ad2c69"
  integrity sha512-YPMtRPqfZWOm2XGTbx5/YVr1HT0vn//8QS77At0Gjb3Lv+Lbut0IORJPKLWu1hQ3u4GsA0SrDzs7nI8JG7Zmyg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.1.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.3.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.2"

rc-slider@~11.1.8:
  version "11.1.8"
  resolved "https://npm.corp.kuaishou.com/rc-slider/-/rc-slider-11.1.8.tgz#cf3b30dacac8f98d44f7685f733f6f7da146fc06"
  integrity sha512-2gg/72YFSpKP+Ja5AjC5DPL1YnV8DEITDQrcc1eASrUYjl0esptaBVJBh5nLTXCCp15eD8EuGjwezVGSHhs9tQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.36.0"

rc-slider@~9.7.4:
  version "9.7.5"
  resolved "https://npm.corp.kuaishou.com/rc-slider/-/rc-slider-9.7.5.tgz#193141c68e99b1dc3b746daeb6bf852946f5b7f4"
  integrity sha512-LV/MWcXFjco1epPbdw1JlLXlTgmWpB9/Y/P2yinf8Pg3wElHxA9uajN21lJiWtZjf5SCUekfSP6QMJfDo4t1hg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-tooltip "^5.0.1"
    rc-util "^5.16.1"
    shallowequal "^1.1.0"

rc-steps@~4.1.0:
  version "4.1.4"
  resolved "https://npm.corp.kuaishou.com/rc-steps/-/rc-steps-4.1.4.tgz#0ba82db202d59ca52d0693dc9880dd145b19dc23"
  integrity sha512-qoCqKZWSpkh/b03ASGx1WhpKnuZcRWmvuW+ZUu4mvMdfvFzVxblTwUM+9aBd0mlEUFmt6GW8FXhMpHkK3Uzp3w==
  dependencies:
    "@babel/runtime" "^7.10.2"
    classnames "^2.2.3"
    rc-util "^5.0.1"

rc-steps@~6.0.1:
  version "6.0.1"
  resolved "https://npm.corp.kuaishou.com/rc-steps/-/rc-steps-6.0.1.tgz#c2136cd0087733f6d509209a84a5c80dc29a274d"
  integrity sha512-lKHL+Sny0SeHkQKKDJlAjV5oZ8DwCdS2hFhAkIjuQt1/pB81M0cA0ErVFdHq9+jmPmFw1vJB2F5NBzFXLJxV+g==
  dependencies:
    "@babel/runtime" "^7.16.7"
    classnames "^2.2.3"
    rc-util "^5.16.1"

rc-switch@~3.2.0:
  version "3.2.2"
  resolved "https://npm.corp.kuaishou.com/rc-switch/-/rc-switch-3.2.2.tgz#d001f77f12664d52595b4f6fb425dd9e66fba8e8"
  integrity sha512-+gUJClsZZzvAHGy1vZfnwySxj+MjLlGRyXKXScrtCTcmiYNPzxDFOxdQ/3pK1Kt/0POvwJ/6ALOR8gwdXGhs+A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-util "^5.0.1"

rc-switch@~4.1.0:
  version "4.1.0"
  resolved "https://npm.corp.kuaishou.com/rc-switch/-/rc-switch-4.1.0.tgz#f37d81b4e0c5afd1274fd85367b17306bf25e7d7"
  integrity sha512-TI8ufP2Az9oEbvyCeVE4+90PDSljGyuwix3fV58p7HV2o4wBnVToEyomJRVyTaZeqNPAp+vqeo4Wnj5u0ZZQBg==
  dependencies:
    "@babel/runtime" "^7.21.0"
    classnames "^2.2.1"
    rc-util "^5.30.0"

rc-table@~7.23.0:
  version "7.23.2"
  resolved "https://npm.corp.kuaishou.com/rc-table/-/rc-table-7.23.2.tgz#f6f906e8fafb05ddbfdd69d450feb875ce260a7b"
  integrity sha512-opc2IBJOetsPSdNI+u1Lh9yY4Ks+EMgo1oJzZN+yIV4fRcgP81tHtxdPOVvXPFI4rUMO8CKnmHbGPU7jxMRAeg==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.14.0"
    shallowequal "^1.1.0"

rc-table@~7.50.2:
  version "7.50.2"
  resolved "https://npm.corp.kuaishou.com/rc-table/-/rc-table-7.50.2.tgz#d66ba71dd5c34ff8981255afc0b98e48d38a8a97"
  integrity sha512-+nJbzxzstBriLb5sr9U7Vjs7+4dO8cWlouQbMwBVYghk2vr508bBdkHJeP/z9HVjAIKmAgMQKxmtbgDd3gc5wA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.44.3"
    rc-virtual-list "^3.14.2"

rc-tabs@~11.10.0:
  version "11.10.8"
  resolved "https://npm.corp.kuaishou.com/rc-tabs/-/rc-tabs-11.10.8.tgz#832d3425bde232b9c4447075b5deef3e2fefa48f"
  integrity sha512-uK+x+eJ8WM4jiXoqGa+P+JUQX2Wlkj9f0o/5dyOw42B6YLnHJN80uTVcCeAmtA1N0xjPW0GNSZvUm4SU3jAYpw==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "2.x"
    rc-dropdown "^3.2.0"
    rc-menu "~9.3.2"
    rc-resize-observer "^1.0.0"
    rc-util "^5.5.0"

rc-tabs@~15.5.1:
  version "15.5.1"
  resolved "https://npm.corp.kuaishou.com/rc-tabs/-/rc-tabs-15.5.1.tgz#9460ae1fd75e2a217e30e596c26425a661a2a4c6"
  integrity sha512-yiWivLAjEo5d1v2xlseB2dQocsOhkoVSfo1krS8v8r+02K+TBUjSjXIf7dgyVSxp6wRIPv5pMi5hanNUlQMgUA==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "2.x"
    rc-dropdown "~4.2.0"
    rc-menu "~9.16.0"
    rc-motion "^2.6.2"
    rc-resize-observer "^1.0.0"
    rc-util "^5.34.1"

rc-textarea@^0.3.0:
  version "0.3.7"
  resolved "https://npm.corp.kuaishou.com/rc-textarea/-/rc-textarea-0.3.7.tgz#987142891efdedb774883c07e2f51b318fde5a11"
  integrity sha512-yCdZ6binKmAQB13hc/oehh0E/QRwoPP1pjF21aHBxlgXO3RzPF6dUu4LG2R4FZ1zx/fQd2L1faktulrXOM/2rw==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.7.0"
    shallowequal "^1.1.0"

rc-textarea@~0.4.5:
  version "0.4.7"
  resolved "https://npm.corp.kuaishou.com/rc-textarea/-/rc-textarea-0.4.7.tgz#627f662d46f99e0059d1c1ebc8db40c65339fe90"
  integrity sha512-IQPd1CDI3mnMlkFyzt2O4gQ2lxUsnBAeJEoZGJnkkXgORNqyM9qovdrCj9NzcRfpHgLdzaEbU3AmobNFGUznwQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.24.4"
    shallowequal "^1.1.0"

rc-textarea@~1.9.0:
  version "1.9.0"
  resolved "https://npm.corp.kuaishou.com/rc-textarea/-/rc-textarea-1.9.0.tgz#d807194ebef90f25f0b9501cddf5e8f2968d598a"
  integrity sha512-dQW/Bc/MriPBTugj2Kx9PMS5eXCCGn2cxoIaichjbNvOiARlaHdI99j4DTxLl/V8+PIfW06uFy7kjfUIDDKyxQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-input "~1.7.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.27.0"

rc-tooltip@^5.0.1:
  version "5.3.1"
  resolved "https://npm.corp.kuaishou.com/rc-tooltip/-/rc-tooltip-5.3.1.tgz#3dde4e1865f79cd23f202bba4e585c2a1173024b"
  integrity sha512-e6H0dMD38EPaSPD2XC8dRfct27VvT2TkPdoBSuNl3RRZ5tspiY/c5xYEmGC0IrABvMBgque4Mr2SMZuliCvoiQ==
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "^2.3.1"
    rc-trigger "^5.3.1"

rc-tooltip@~5.1.1:
  version "5.1.1"
  resolved "https://npm.corp.kuaishou.com/rc-tooltip/-/rc-tooltip-5.1.1.tgz#94178ed162d0252bc4993b725f5dc2ac0fccf154"
  integrity sha512-alt8eGMJulio6+4/uDm7nvV+rJq9bsfxFDCI0ljPdbuoygUscbsMYb6EQgwib/uqsXQUvzk+S7A59uYHmEgmDA==
  dependencies:
    "@babel/runtime" "^7.11.2"
    rc-trigger "^5.0.0"

rc-tooltip@~6.4.0:
  version "6.4.0"
  resolved "https://npm.corp.kuaishou.com/rc-tooltip/-/rc-tooltip-6.4.0.tgz#e832ed0392872025e59928cfc1ad9045656467fd"
  integrity sha512-kqyivim5cp8I5RkHmpsp1Nn/Wk+1oeloMv9c7LXNgDxUpGm+RbXJGL+OPvDlcRnx9DBeOe4wyOIl4OKUERyH1g==
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.1"
    rc-util "^5.44.3"

rc-tree-select@~4.8.0:
  version "4.8.0"
  resolved "https://npm.corp.kuaishou.com/rc-tree-select/-/rc-tree-select-4.8.0.tgz#bcbcfb45553f84a878e4ff037ff00b526a4afa62"
  integrity sha512-evuVIF7GHCGDdvISdBWl4ZYmG/8foof/RDtzCu/WFLA1tFKZD77RRC3khEsjh4WgsB0vllLe7j+ODJ7jHRcDRQ==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-select "~13.2.1"
    rc-tree "~5.3.0"
    rc-util "^5.7.0"

rc-tree-select@~5.27.0:
  version "5.27.0"
  resolved "https://npm.corp.kuaishou.com/rc-tree-select/-/rc-tree-select-5.27.0.tgz#3daa62972ae80846dac96bf4776d1a9dc9c7c4c6"
  integrity sha512-2qTBTzwIT7LRI1o7zLyrCzmo5tQanmyGbSaGTIf7sYimCklAToVVfpMC6OAldSKolcnjorBYPNSKQqJmN3TCww==
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "2.x"
    rc-select "~14.16.2"
    rc-tree "~5.13.0"
    rc-util "^5.43.0"

rc-tree@~5.13.0:
  version "5.13.0"
  resolved "https://npm.corp.kuaishou.com/rc-tree/-/rc-tree-5.13.0.tgz#ae34768c1463fd1fb19d73549c29b219c8891296"
  integrity sha512-2+lFvoVRnvHQ1trlpXMOWtF8BUgF+3TiipG72uOfhpL5CUdXCk931kvDdUkTL/IZVtNEDQKwEEmJbAYJSA5NnA==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.1"

rc-tree@~5.3.0:
  version "5.3.8"
  resolved "https://npm.corp.kuaishou.com/rc-tree/-/rc-tree-5.3.8.tgz#91c9d6c13e446644d4655e5304aeebc24c913073"
  integrity sha512-YuobEryPymqPmHFUOvsoOrYdm24psaj0CrGEUuDUQUeG/nNcTGw6FA2YmF4NsEaNBvNSJUSzwfZnFHrKa/xv0A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.4.1"

rc-tree@~5.6.3, rc-tree@~5.6.5:
  version "5.6.9"
  resolved "https://npm.corp.kuaishou.com/rc-tree/-/rc-tree-5.6.9.tgz#b73290a6dcad65e4ed5d8dc21cb198b30316404b"
  integrity sha512-si8aGuWQ2/sh2Ibk+WdUdDeAxoviT/+kDY+NLtJ+RhqfySqPFqWM5uHTwgFRrWUvKCqEeE/PjCYuuhHrK7Y7+A==
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.4.8"

rc-trigger@^5.0.0, rc-trigger@^5.0.4, rc-trigger@^5.1.2, rc-trigger@^5.2.10, rc-trigger@^5.3.1:
  version "5.3.4"
  resolved "https://npm.corp.kuaishou.com/rc-trigger/-/rc-trigger-5.3.4.tgz#6b4b26e32825677c837d1eb4d7085035eecf9a61"
  integrity sha512-mQv+vas0TwKcjAO2izNPkqR4j86OemLRmvL2nOzdP9OWNWA1ivoTt5hzFqYNW9zACwmTezRiN8bttrC7cZzYSw==
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.6"
    rc-align "^4.0.0"
    rc-motion "^2.0.0"
    rc-util "^5.19.2"

rc-upload@~4.3.0:
  version "4.3.6"
  resolved "https://npm.corp.kuaishou.com/rc-upload/-/rc-upload-4.3.6.tgz#6a87397315cee065a04bee4103d6de9dbe2e377a"
  integrity sha512-Bt7ESeG5tT3IY82fZcP+s0tQU2xmo1W6P3S8NboUUliquJLQYLkUcsaExi3IlBVr43GQMCjo30RA2o0i70+NjA==
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.5"
    rc-util "^5.2.0"

rc-upload@~4.8.1:
  version "4.8.1"
  resolved "https://npm.corp.kuaishou.com/rc-upload/-/rc-upload-4.8.1.tgz#ac55f2bc101b95b52a6e47f3c18f0f55b54e16d2"
  integrity sha512-toEAhwl4hjLAI1u8/CgKWt30BR06ulPa4iGQSMvSXoHzO88gPCslxqV/mnn4gJU7PDoltGIC9Eh+wkeudqgHyw==
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.5"
    rc-util "^5.2.0"

rc-util@^5.0.1, rc-util@^5.0.6, rc-util@^5.12.0, rc-util@^5.14.0, rc-util@^5.16.1, rc-util@^5.17.0, rc-util@^5.18.1, rc-util@^5.19.2, rc-util@^5.2.0, rc-util@^5.2.1, rc-util@^5.20.1, rc-util@^5.21.0, rc-util@^5.23.0, rc-util@^5.24.4, rc-util@^5.25.2, rc-util@^5.26.0, rc-util@^5.27.0, rc-util@^5.30.0, rc-util@^5.31.1, rc-util@^5.32.2, rc-util@^5.34.1, rc-util@^5.35.0, rc-util@^5.36.0, rc-util@^5.37.0, rc-util@^5.38.0, rc-util@^5.38.1, rc-util@^5.39.1, rc-util@^5.4.0, rc-util@^5.40.1, rc-util@^5.43.0, rc-util@^5.44.0, rc-util@^5.44.1, rc-util@^5.44.3, rc-util@^5.44.4, rc-util@^5.5.0, rc-util@^5.6.1, rc-util@^5.7.0, rc-util@^5.8.0, rc-util@^5.9.4, rc-util@^5.9.8:
  version "5.44.4"
  resolved "https://npm.corp.kuaishou.com/rc-util/-/rc-util-5.44.4.tgz#89ee9037683cca01cd60f1a6bbda761457dd6ba5"
  integrity sha512-resueRJzmHG9Q6rI/DfK6Kdv9/Lfls05vzMs1Sk3M2P+3cJa+MakaZyWY8IPfehVuhPJFKrIY1IK4GqbiaiY5w==
  dependencies:
    "@babel/runtime" "^7.18.3"
    react-is "^18.2.0"

rc-virtual-list@^3.14.2, rc-virtual-list@^3.2.0, rc-virtual-list@^3.4.1, rc-virtual-list@^3.4.8, rc-virtual-list@^3.5.1, rc-virtual-list@^3.5.2:
  version "3.18.1"
  resolved "https://npm.corp.kuaishou.com/rc-virtual-list/-/rc-virtual-list-3.18.1.tgz#a081237b7e468eaec7e64a896a4261c3ed6f1c4b"
  integrity sha512-ARSsD/dey/I4yNQHFYYUaKLUkD1wnD4lRZIvb3rCLMbTMmoFQJRVrWuSfbNt5P5MzMNooEBDvqrUPM4QN7BMNA==
  dependencies:
    "@babel/runtime" "^7.20.0"
    classnames "^2.2.6"
    rc-resize-observer "^1.0.0"
    rc-util "^5.36.0"

react-dnd-html5-backend@^15.1.2:
  version "15.1.3"
  resolved "https://npm.corp.kuaishou.com/react-dnd-html5-backend/-/react-dnd-html5-backend-15.1.3.tgz#57b4f47e0f23923e7c243d2d0eefe490069115a9"
  integrity sha512-HH/8nOEmrrcRGHMqJR91FOwhnLlx5SRLXmsQwZT3IPcBjx88WT+0pWC5A4tDOYDdoooh9k+KMPvWfxooR5TcOA==
  dependencies:
    dnd-core "15.1.2"

react-dnd@^15.1.1:
  version "15.1.2"
  resolved "https://npm.corp.kuaishou.com/react-dnd/-/react-dnd-15.1.2.tgz#211b30fd842326209c63f26f1bdf1bc52eef4f64"
  integrity sha512-EaSbMD9iFJDY/o48T3c8wn3uWU+2uxfFojhesZN3LhigJoAIvH2iOjxofSA9KbqhAKP6V9P853G6XG8JngKVtA==
  dependencies:
    "@react-dnd/invariant" "3.0.1"
    "@react-dnd/shallowequal" "3.0.1"
    dnd-core "15.1.2"
    fast-deep-equal "^3.1.3"
    hoist-non-react-statics "^3.3.2"

react-dom@^17.0.2:
  version "17.0.2"
  resolved "https://npm.corp.kuaishou.com/react-dom/-/react-dom-17.0.2.tgz#ecffb6845e3ad8dbfcdc498f0d0a939736502c23"
  integrity sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    scheduler "^0.20.2"

react-draggable@^4.0.3, react-draggable@^4.4.5:
  version "4.4.6"
  resolved "https://npm.corp.kuaishou.com/react-draggable/-/react-draggable-4.4.6.tgz#63343ee945770881ca1256a5b6fa5c9f5983fe1e"
  integrity sha512-LtY5Xw1zTPqHkVmtM3X8MUOxNDOUhv/khTgBgrUvwaS064bwVvxT+q5El0uUFNx5IEPKXuRejr7UqLwBIg5pdw==
  dependencies:
    clsx "^1.1.1"
    prop-types "^15.8.1"

react-error-boundary@^3.1.4:
  version "3.1.4"
  resolved "https://npm.corp.kuaishou.com/react-error-boundary/-/react-error-boundary-3.1.4.tgz#255db92b23197108757a888b01e5b729919abde0"
  integrity sha512-uM9uPzZJTF6wRQORmSrvOIgt4lJ9MC1sNgEOj2XGsDTRE4kmpWxg7ENK9EWNKJRMAOY9z0MuF4yIfl6gp4sotA==
  dependencies:
    "@babel/runtime" "^7.12.5"

react-error-overlay@6.0.9:
  version "6.0.9"
  resolved "https://npm.corp.kuaishou.com/react-error-overlay/-/react-error-overlay-6.0.9.tgz#3c743010c9359608c375ecd6bc76f35d93995b0a"
  integrity sha512-nQTTcUu+ATDbrSD1BZHr5kgSD4oF8OFjxun8uAaL8RwPBacGBNPf/yAuVVdx17N8XNzRDMrZ9XcKZHCjPW+9ew==

react-fast-compare@^3.2.2:
  version "3.2.2"
  resolved "https://npm.corp.kuaishou.com/react-fast-compare/-/react-fast-compare-3.2.2.tgz#929a97a532304ce9fee4bcae44234f1ce2c21d49"
  integrity sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==

react-grid-layout@^1.4.4:
  version "1.5.0"
  resolved "https://npm.corp.kuaishou.com/react-grid-layout/-/react-grid-layout-1.5.0.tgz#b6cc9412b58cf8226aebc0df7673d6fa782bdee2"
  integrity sha512-WBKX7w/LsTfI99WskSu6nX2nbJAUD7GD6nIXcwYLyPpnslojtmql2oD3I2g5C3AK8hrxIarYT8awhuDIp7iQ5w==
  dependencies:
    clsx "^2.0.0"
    fast-equals "^4.0.3"
    prop-types "^15.8.1"
    react-draggable "^4.4.5"
    react-resizable "^3.0.5"
    resize-observer-polyfill "^1.5.1"

react-hotkeys-hook@^4.5.0:
  version "4.6.1"
  resolved "https://npm.corp.kuaishou.com/react-hotkeys-hook/-/react-hotkeys-hook-4.6.1.tgz#db9066c07377a1c8be067a238ab16e328266345a"
  integrity sha512-XlZpbKUj9tkfgPgT9gA+1p7Ey6vFIZHttUjPqpTdyT5nqQ8mHL7elxvSbaC+dpSiHUSmr21Ya1mDxBZG3aje4Q==

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://npm.corp.kuaishou.com/react-is/-/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^18.2.0:
  version "18.3.1"
  resolved "https://npm.corp.kuaishou.com/react-is/-/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==

react-loading-skeleton@^3.5.0:
  version "3.5.0"
  resolved "https://npm.corp.kuaishou.com/react-loading-skeleton/-/react-loading-skeleton-3.5.0.tgz#da2090355b4dedcad5c53cb3f0ed364e3a76d6ca"
  integrity sha512-gxxSyLbrEAdXTKgfbpBEFZCO/P153DnqSCQau2+o6lNy1jgMRr2MmRmOzMmyrwSaSYLRB8g7b0waYPmUjz7IhQ==

react-refresh@0.14.0:
  version "0.14.0"
  resolved "https://npm.corp.kuaishou.com/react-refresh/-/react-refresh-0.14.0.tgz#4e02825378a5f227079554d4284889354e5f553e"
  integrity sha512-wViHqhAd8OHeLS/IRMJjTSDHF3U9eWi62F/MledQGPdJGDhodXJ9PBLNGr6WWL7qlH12Mt3TyTpbS+hGXMjCzQ==

react-resizable@^3.0.5:
  version "3.0.5"
  resolved "https://npm.corp.kuaishou.com/react-resizable/-/react-resizable-3.0.5.tgz#362721f2efbd094976f1780ae13f1ad7739786c1"
  integrity sha512-vKpeHhI5OZvYn82kXOs1bC8aOXktGU5AmKAgaZS4F5JPburCtbmDPqE7Pzp+1kN4+Wb81LlF33VpGwWwtXem+w==
  dependencies:
    prop-types "15.x"
    react-draggable "^4.0.3"

react-router-config@5.1.1:
  version "5.1.1"
  resolved "https://npm.corp.kuaishou.com/react-router-config/-/react-router-config-5.1.1.tgz#0f4263d1a80c6b2dc7b9c1902c9526478194a988"
  integrity sha512-DuanZjaD8mQp1ppHjgnnUnyOlqYXZVjnov/JzFhjLEwd3Z4dYjMSnqrEzzGThH47vpCOqPPwJM2FtthLeJ8Pbg==
  dependencies:
    "@babel/runtime" "^7.1.2"

react-router-dom@6.4.5:
  version "6.4.5"
  resolved "https://npm.corp.kuaishou.com/react-router-dom/-/react-router-dom-6.4.5.tgz#4fdb12efef4f3848c693a76afbeaed1f6ca02047"
  integrity sha512-a7HsgikBR0wNfroBHcZUCd9+mLRqZS8R5U1Z1mzLWxFXEkUT3vR1XXmSIVoVpxVX8Bar0nQYYYc9Yipq8dWwAA==
  dependencies:
    "@remix-run/router" "1.0.5"
    react-router "6.4.5"

react-router-dom@^6.4.3:
  version "6.29.0"
  resolved "https://npm.corp.kuaishou.com/react-router-dom/-/react-router-dom-6.29.0.tgz#2ffb56b03ef3d6d6daafcfad9f3922132d2ced94"
  integrity sha512-pkEbJPATRJ2iotK+wUwHfy0xs2T59YPEN8BQxVCPeBZvK7kfPESRc/nyxzdcxR17hXgUPYx2whMwl+eo9cUdnQ==
  dependencies:
    "@remix-run/router" "1.22.0"
    react-router "6.29.0"

react-router@6.29.0:
  version "6.29.0"
  resolved "https://npm.corp.kuaishou.com/react-router/-/react-router-6.29.0.tgz#14a329ca838b4de048fc5cca82874b727ee546b7"
  integrity sha512-DXZJoE0q+KyeVw75Ck6GkPxFak63C4fGqZGNijnWgzB/HzSP1ZfTlBj5COaGWwhrMQ/R8bXiq5Ooy4KG+ReyjQ==
  dependencies:
    "@remix-run/router" "1.22.0"

react-router@6.4.5:
  version "6.4.5"
  resolved "https://npm.corp.kuaishou.com/react-router/-/react-router-6.4.5.tgz#73f382af2c8b9a86d74e761a7c5fc3ce7cb0024d"
  integrity sha512-1RQJ8bM70YEumHIlNUYc6mFfUDoWa5EgPDenK/fq0bxD8DYpQUi/S6Zoft+9DBrh2xmtg92N5HMAJgGWDhKJ5Q==
  dependencies:
    "@remix-run/router" "1.0.5"

react-sticky-box@^2.0.5:
  version "2.0.5"
  resolved "https://npm.corp.kuaishou.com/react-sticky-box/-/react-sticky-box-2.0.5.tgz#518c555457c58ebea127aa7d80df3d93e16a1ce8"
  integrity sha512-JEIuN7Q3razU1NNS0vddC12GXCUkj7T4aCpC9ZSTjgBddFsBdTcN1RhYXFLjCE/KDOQqX2Ix308BcdrqtSJbkQ==

react-universal-interface@^0.6.2:
  version "0.6.2"
  resolved "https://npm.corp.kuaishou.com/react-universal-interface/-/react-universal-interface-0.6.2.tgz#5e8d438a01729a4dbbcbeeceb0b86be146fe2b3b"
  integrity sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw==

react-use@^17.4.0:
  version "17.6.0"
  resolved "https://npm.corp.kuaishou.com/react-use/-/react-use-17.6.0.tgz#2101a3a79dc965a25866b21f5d6de4b128488a14"
  integrity sha512-OmedEScUMKFfzn1Ir8dBxiLLSOzhKe/dPZwVxcujweSj45aNM7BEGPb9BEVIgVEqEXx6f3/TsXzwIktNgUR02g==
  dependencies:
    "@types/js-cookie" "^2.2.6"
    "@xobotyi/scrollbar-width" "^1.9.5"
    copy-to-clipboard "^3.3.1"
    fast-deep-equal "^3.1.3"
    fast-shallow-equal "^1.0.0"
    js-cookie "^2.2.1"
    nano-css "^5.6.2"
    react-universal-interface "^0.6.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.1.0"
    set-harmonic-interval "^1.0.1"
    throttle-debounce "^3.0.1"
    ts-easing "^0.2.0"
    tslib "^2.1.0"

react-window@^1.8.10:
  version "1.8.11"
  resolved "https://npm.corp.kuaishou.com/react-window/-/react-window-1.8.11.tgz#a857b48fa85bd77042d59cc460964ff2e0648525"
  integrity sha512-+SRbUVT2scadgFSWx+R1P754xHPEqvcfSfVX10QYg6POOz+WNgkN48pS+BtZNIMGiL1HYrSEiCkwsMS15QogEQ==
  dependencies:
    "@babel/runtime" "^7.0.0"
    memoize-one ">=3.1.1 <6"

react@^17.0.2:
  version "17.0.2"
  resolved "https://npm.corp.kuaishou.com/react/-/react-17.0.2.tgz#d0b5cc516d29eb3eee383f75b62864cfb6800037"
  integrity sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

readable-stream@^2.3.8:
  version "2.3.8"
  resolved "https://npm.corp.kuaishou.com/readable-stream/-/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.1.1, readable-stream@^3.5.0, readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://npm.corp.kuaishou.com/readable-stream/-/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

real-require@^0.1.0:
  version "0.1.0"
  resolved "https://npm.corp.kuaishou.com/real-require/-/real-require-0.1.0.tgz#736ac214caa20632847b7ca8c1056a0767df9381"
  integrity sha512-r/H9MzAWtrv8aSVjPCMFpDMl5q66GqtmmRkRjpHTsp4zBAa+snZyiQNlMONiUmEJcsnaw0wCauJ2GWODr/aFkg==

redux@^4.1.2:
  version "4.2.1"
  resolved "https://npm.corp.kuaishou.com/redux/-/redux-4.2.1.tgz#c08f4306826c49b5e9dc901dee0452ea8fce6197"
  integrity sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==
  dependencies:
    "@babel/runtime" "^7.9.2"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "https://npm.corp.kuaishou.com/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz#c629219e78a3316d8b604c765ef68996964e7bf9"
  integrity sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regenerate-unicode-properties@10.2.0:
  version "10.2.0"
  resolved "https://npm.corp.kuaishou.com/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz#626e39df8c372338ea9b8028d1f99dc3fd9c3db0"
  integrity sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==
  dependencies:
    regenerate "^1.4.2"

regenerate@1.4.2, regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://npm.corp.kuaishou.com/regenerate/-/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==

regenerator-runtime@0.13.11:
  version "0.13.11"
  resolved "https://npm.corp.kuaishou.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://npm.corp.kuaishou.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz#356ade10263f685dda125100cd862c1db895327f"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.3:
  version "1.5.4"
  resolved "https://npm.corp.kuaishou.com/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz#1ad6c62d44a259007e55b3970e00f746efbcaa19"
  integrity sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "https://npm.corp.kuaishou.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==

resize-observer-polyfill@^1.5.0, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://npm.corp.kuaishou.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve@^1.17.0, resolve@~1.22.6:
  version "1.22.10"
  resolved "https://npm.corp.kuaishou.com/resolve/-/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

right-align@^0.1.1:
  version "0.1.3"
  resolved "https://npm.corp.kuaishou.com/right-align/-/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  integrity sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg==
  dependencies:
    align-text "^0.1.1"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "https://npm.corp.kuaishou.com/ripemd160/-/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rollup@^0.25.8:
  version "0.25.8"
  resolved "https://npm.corp.kuaishou.com/rollup/-/rollup-0.25.8.tgz#bf6ce83b87510d163446eeaa577ed6a6fc5835e0"
  integrity sha512-a2S4Bh3bgrdO4BhKr2E4nZkjTvrJ2m2bWjMTzVYtoqSCn0HnuxosXnaJUHrMEziOWr3CzL9GjilQQKcyCQpJoA==
  dependencies:
    chalk "^1.1.1"
    minimist "^1.2.0"
    source-map-support "^0.3.2"

rs-module-lexer@2.5.0:
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/rs-module-lexer/-/rs-module-lexer-2.5.0.tgz#fde2226ebb700794ccf0d34bbcebcab632148a91"
  integrity sha512-ACmfiSoXYtAG9F3ySSvLwRrLHhfEaQCsk4mytaSFEHI5mKYXZUzsQI9aF4Wy4R8U7VMFLm4DgccEWbDpw0hs6g==
  optionalDependencies:
    "@xn-sakina/rml-darwin-arm64" "2.5.0"
    "@xn-sakina/rml-darwin-x64" "2.5.0"
    "@xn-sakina/rml-linux-arm-gnueabihf" "2.5.0"
    "@xn-sakina/rml-linux-arm64-gnu" "2.5.0"
    "@xn-sakina/rml-linux-arm64-musl" "2.5.0"
    "@xn-sakina/rml-linux-x64-gnu" "2.5.0"
    "@xn-sakina/rml-linux-x64-musl" "2.5.0"
    "@xn-sakina/rml-win32-arm64-msvc" "2.5.0"
    "@xn-sakina/rml-win32-x64-msvc" "2.5.0"

rtl-css-js@^1.16.1:
  version "1.16.1"
  resolved "https://npm.corp.kuaishou.com/rtl-css-js/-/rtl-css-js-1.16.1.tgz#4b48b4354b0ff917a30488d95100fbf7219a3e80"
  integrity sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==
  dependencies:
    "@babel/runtime" "^7.1.2"

rw@1, rw@^1.3.2:
  version "1.3.3"
  resolved "https://npm.corp.kuaishou.com/rw/-/rw-1.3.3.tgz#3f862dfa91ab766b14885ef4d01124bfda074fb4"
  integrity sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "https://npm.corp.kuaishou.com/safe-array-concat/-/safe-array-concat-1.1.3.tgz#c9e54ec4f603b0bbb8e7e5007a5ee7aecd1538c3"
  integrity sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@^5.2.0, safe-buffer@^5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://npm.corp.kuaishou.com/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://npm.corp.kuaishou.com/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-json-parse-and-stringify@^0.2.0:
  version "0.2.0"
  resolved "https://npm.corp.kuaishou.com/safe-json-parse-and-stringify/-/safe-json-parse-and-stringify-0.2.0.tgz#34c3e9fbf7d160bef0a2d3830153e15d787fd086"
  integrity sha512-lj46Mn8eIiZBfOiETp11bbyG7V8Lj19JihL1CTfV8wXuWOZrQ64NpfCKye/tuOJvN0jdw+AQ4mLx+bzJGfrWgQ==
  dependencies:
    "@types/json-stringify-safe" "^5.0.0"
    "@types/lodash.clonedeep" "^4.5.6"
    json-stringify-safe "^5.0.1"
    lodash.clonedeep "^4.5.0"

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/safe-push-apply/-/safe-push-apply-1.0.0.tgz#01850e981c1602d398c85081f360e4e6d03d27f5"
  integrity sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/safe-regex-test/-/safe-regex-test-1.1.0.tgz#7f87dfb67a3150782eaaf18583ff5d1711ac10c1"
  integrity sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

safe-stable-stringify@^2.1.0:
  version "2.5.0"
  resolved "https://npm.corp.kuaishou.com/safe-stable-stringify/-/safe-stable-stringify-2.5.0.tgz#4ca2f8e385f2831c432a719b108a3bf7af42a1dd"
  integrity sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://npm.corp.kuaishou.com/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

scheduler@^0.20.2:
  version "0.20.2"
  resolved "https://npm.corp.kuaishou.com/scheduler/-/scheduler-0.20.2.tgz#4baee39436e34aa93b4874bddcbf0fe8b8b50e91"
  integrity sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

screenfull@^5.0.0, screenfull@^5.1.0:
  version "5.2.0"
  resolved "https://npm.corp.kuaishou.com/screenfull/-/screenfull-5.2.0.tgz#6533d524d30621fc1283b9692146f3f13a93d1ba"
  integrity sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==

scroll-into-view-if-needed@^2.2.25:
  version "2.2.31"
  resolved "https://npm.corp.kuaishou.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz#d3c482959dc483e37962d1521254e3295d0d1587"
  integrity sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA==
  dependencies:
    compute-scroll-into-view "^1.0.20"

scroll-into-view-if-needed@^3.1.0:
  version "3.1.0"
  resolved "https://npm.corp.kuaishou.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-3.1.0.tgz#fa9524518c799b45a2ef6bbffb92bcad0296d01f"
  integrity sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ==
  dependencies:
    compute-scroll-into-view "^3.0.2"

semver@^6.3.1:
  version "6.3.1"
  resolved "https://npm.corp.kuaishou.com/semver/-/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.5.4:
  version "7.7.1"
  resolved "https://npm.corp.kuaishou.com/semver/-/semver-7.7.1.tgz#abd5098d82b18c6c81f6074ff2647fd3e7220c9f"
  integrity sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://npm.corp.kuaishou.com/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://npm.corp.kuaishou.com/set-function-name/-/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-harmonic-interval@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/set-harmonic-interval/-/set-harmonic-interval-1.0.1.tgz#e1773705539cdfb80ce1c3d99e7f298bb3995249"
  integrity sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g==

set-proto@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/set-proto/-/set-proto-1.0.0.tgz#0760dbcff30b2d7e801fd6e19983e56da337565e"
  integrity sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

setimmediate@^1.0.4, setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://npm.corp.kuaishou.com/setimmediate/-/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "https://npm.corp.kuaishou.com/sha.js/-/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallowequal@1.1.0, shallowequal@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/shallowequal/-/shallowequal-1.1.0.tgz#188d521de95b9087404fd4dcb68b13df0ae4e7f8"
  integrity sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==

shell-exec@1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/shell-exec/-/shell-exec-1.0.2.tgz#2e9361b0fde1d73f476c4b6671fa17785f696756"
  integrity sha512-jyVd+kU2X+mWKMmGhx4fpWbPsjvD53k9ivqetutVW/BQ+WIZoDoP4d8vUMGezV6saZsiNoW2f9GIhg9Dondohg==

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/side-channel-list/-/side-channel-list-1.0.0.tgz#10cb5984263115d3b7a0e336591e290a830af8ad"
  integrity sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://npm.corp.kuaishou.com/side-channel-map/-/side-channel-map-1.0.1.tgz#d6bb6b37902c6fef5174e5f533fab4c732a26f42"
  integrity sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz#11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea"
  integrity sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/side-channel/-/side-channel-1.1.0.tgz#c3fcff9c4da932784873335ec9765fa94ff66bc9"
  integrity sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://npm.corp.kuaishou.com/simple-swizzle/-/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

single-spa@^5.9.2:
  version "5.9.5"
  resolved "https://npm.corp.kuaishou.com/single-spa/-/single-spa-5.9.5.tgz#f47b3c91b009ebc3b224dd1086ef2b2dac524373"
  integrity sha512-9SQdmsyz4HSP+3gs6PJzhkaMEg+6zTlu9oxIghnwUX3eq+ajq4ft5egl0iyR55LAmO/UwvU8NgIWs/ZyQMa6dw==

size-sensor@^1.0.1:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/size-sensor/-/size-sensor-1.0.2.tgz#b8f8da029683cf2b4e22f12bf8b8f0a1145e8471"
  integrity sha512-2NCmWxY7A9pYKGXNBfteo4hy14gWu47rg5692peVMst6lQLPKrVjhY+UTEsPI5ceFRJSl3gVgMYaUi/hKuaiKw==

socket.io-client@^4.7.5:
  version "4.8.1"
  resolved "https://npm.corp.kuaishou.com/socket.io-client/-/socket.io-client-4.8.1.tgz#1941eca135a5490b94281d0323fe2a35f6f291cb"
  integrity sha512-hJVXfu3E28NmzGk8o1sHhN3om52tRvwYeidbj7xKy2eIIse5IoKX3USlS6Tqt3BHAtflLIkCQBkzVrEEfWUyYQ==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.2"
    engine.io-client "~6.6.1"
    socket.io-parser "~4.2.4"

socket.io-parser@~4.2.4:
  version "4.2.4"
  resolved "https://npm.corp.kuaishou.com/socket.io-parser/-/socket.io-parser-4.2.4.tgz#c806966cf7270601e47469ddeec30fbdfda44c83"
  integrity sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"

sonic-boom@^2.2.1:
  version "2.8.0"
  resolved "https://npm.corp.kuaishou.com/sonic-boom/-/sonic-boom-2.8.0.tgz#c1def62a77425090e6ad7516aad8eb402e047611"
  integrity sha512-kuonw1YOYYNOve5iHdSahXPOK49GqwA+LZhI6Wz/l0rP57iKyXXIHaRagOBHAPmGwJC6od2Z9zgvZ5loSgMlVg==
  dependencies:
    atomic-sleep "^1.0.0"

source-map-js@^1.0.2, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/source-map-js/-/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map-support@^0.3.2:
  version "0.3.3"
  resolved "https://npm.corp.kuaishou.com/source-map-support/-/source-map-support-0.3.3.tgz#34900977d5ba3f07c7757ee72e73bb1a9b53754f"
  integrity sha512-9O4+y9n64RewmFoKUZ/5Tx9IHIcXM6Q+RTSw6ehnqybUz4a7iwR3Eaw80uLtqqQ5D0C+5H03D4KKGo9PdP33Gg==
  dependencies:
    source-map "0.1.32"

source-map@0.1.32:
  version "0.1.32"
  resolved "https://npm.corp.kuaishou.com/source-map/-/source-map-0.1.32.tgz#c8b6c167797ba4740a8ea33252162ff08591b266"
  integrity sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ==
  dependencies:
    amdefine ">=0.0.4"

source-map@0.5.6:
  version "0.5.6"
  resolved "https://npm.corp.kuaishou.com/source-map/-/source-map-0.5.6.tgz#75ce38f52bf0733c5a7f0c118d81334a2bb5f412"
  integrity sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==

source-map@^0.6.1:
  version "0.6.1"
  resolved "https://npm.corp.kuaishou.com/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@~0.5.1:
  version "0.5.7"
  resolved "https://npm.corp.kuaishou.com/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==

split2@^4.0.0:
  version "4.2.0"
  resolved "https://npm.corp.kuaishou.com/split2/-/split2-4.2.0.tgz#c9c5920904d148bab0b9f67145f245a86aadbfa4"
  integrity sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==

stack-generator@^2.0.5:
  version "2.0.10"
  resolved "https://npm.corp.kuaishou.com/stack-generator/-/stack-generator-2.0.10.tgz#8ae171e985ed62287d4f1ed55a1633b3fb53bb4d"
  integrity sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==
  dependencies:
    stackframe "^1.3.4"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "https://npm.corp.kuaishou.com/stackframe/-/stackframe-1.3.4.tgz#b881a004c8c149a5e8efef37d51b16e412943310"
  integrity sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==

stacktrace-gps@^3.0.4:
  version "3.1.2"
  resolved "https://npm.corp.kuaishou.com/stacktrace-gps/-/stacktrace-gps-3.1.2.tgz#0c40b24a9b119b20da4525c398795338966a2fb0"
  integrity sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==
  dependencies:
    source-map "0.5.6"
    stackframe "^1.3.4"

stacktrace-js@^2.0.2:
  version "2.0.2"
  resolved "https://npm.corp.kuaishou.com/stacktrace-js/-/stacktrace-js-2.0.2.tgz#4ca93ea9f494752d55709a081d400fdaebee897b"
  integrity sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==
  dependencies:
    error-stack-parser "^2.0.6"
    stack-generator "^2.0.5"
    stacktrace-gps "^3.0.4"

state-local@^1.0.6:
  version "1.0.7"
  resolved "https://npm.corp.kuaishou.com/state-local/-/state-local-1.0.7.tgz#da50211d07f05748d53009bee46307a37db386d5"
  integrity sha512-HTEHMNieakEnoe33shBYcZ7NX83ACUjCu8c40iOGEZsngj9zRnkqS9j1pqQPXwobB0ZcVTk27REb7COQ0UR59w==

stop-iteration-iterator@^1.0.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz#f481ff70a548f6124d0312c3aa14cbfa7aa542ad"
  integrity sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

stream-browserify@^3.0.0:
  version "3.0.0"
  resolved "https://npm.corp.kuaishou.com/stream-browserify/-/stream-browserify-3.0.0.tgz#22b0a2850cdf6503e73085da1fc7b7d0c2122f2f"
  integrity sha512-H73RAHsVBapbim0tU2JwwOiXUj+fikfiaoYAKHF3VJfA0pe2BCzkhAHBlLG6REzE+2WNZcxOXjK7lkso+9euLA==
  dependencies:
    inherits "~2.0.4"
    readable-stream "^3.5.0"

stream-http@^3.2.0:
  version "3.2.0"
  resolved "https://npm.corp.kuaishou.com/stream-http/-/stream-http-3.2.0.tgz#1872dfcf24cb15752677e40e5c3f9cc1926028b5"
  integrity sha512-Oq1bLqisTyK3TSCXpPbT4sdeYNdmyZJv1LxpEm2vu1ZhK89kSE5YXwZc3cWk0MagGaKriBh9mCFbVGtO+vY29A==
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.4"
    readable-stream "^3.6.0"
    xtend "^4.0.2"

stream-shift@^1.0.2:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/stream-shift/-/stream-shift-1.0.3.tgz#85b8fab4d71010fc3ba8772e8046cc49b8a3864b"
  integrity sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==

string-convert@^0.2.0:
  version "0.2.1"
  resolved "https://npm.corp.kuaishou.com/string-convert/-/string-convert-0.2.1.tgz#6982cc3049fbb4cd85f8b24568b9d9bf39eeff97"
  integrity sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==

string.prototype.trim@^1.2.10, string.prototype.trim@~1.2.8:
  version "1.2.10"
  resolved "https://npm.corp.kuaishou.com/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz#40b2dd5ee94c959b4dcfb1d65ce72e90da480c81"
  integrity sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "https://npm.corp.kuaishou.com/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz#62e2731272cd285041b36596054e9f66569b6942"
  integrity sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://npm.corp.kuaishou.com/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz#7ee834dda8c7c17eff3118472bb35bfedaa34dde"
  integrity sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.0.0, string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://npm.corp.kuaishou.com/string_decoder/-/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==
  dependencies:
    ansi-regex "^2.0.0"

styled-components@^6.1.15:
  version "6.1.18"
  resolved "https://npm.corp.kuaishou.com/styled-components/-/styled-components-6.1.18.tgz#9647497a92326ba9d758051c914f15004d524bb9"
  integrity sha512-Mvf3gJFzZCkhjY2Y/Fx9z1m3dxbza0uI9H1CbNZm/jSHCojzJhQ0R7bByrlFJINnMzz/gPulpoFFGymNwrsMcw==
  dependencies:
    "@emotion/is-prop-valid" "1.2.2"
    "@emotion/unitless" "0.8.1"
    "@types/stylis" "4.2.5"
    css-to-react-native "3.2.0"
    csstype "3.1.3"
    postcss "8.4.49"
    shallowequal "1.1.0"
    stylis "4.3.2"
    tslib "2.6.2"

stylis@4.3.2:
  version "4.3.2"
  resolved "https://npm.corp.kuaishou.com/stylis/-/stylis-4.3.2.tgz#8f76b70777dd53eb669c6f58c997bf0a9972e444"
  integrity sha512-bhtUjWd/z6ltJiQwg0dUfxEJ+W+jdqQd8TbWLWyeIJHlnsqmGLRFFd8e5mA0AZi/zx90smXRlN66YMTcaSFifg==

stylis@^4.3.0, stylis@^4.3.4:
  version "4.3.6"
  resolved "https://npm.corp.kuaishou.com/stylis/-/stylis-4.3.6.tgz#7c7b97191cb4f195f03ecab7d52f7902ed378320"
  integrity sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://npm.corp.kuaishou.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://npm.corp.kuaishou.com/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://npm.corp.kuaishou.com/supports-color/-/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://npm.corp.kuaishou.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-path-parser@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/svg-path-parser/-/svg-path-parser-1.1.0.tgz#e16b4b39df0d2b0d39e8347db79fdda1453a6046"
  integrity sha512-jGCUqcQyXpfe38R7RFfhrMyfXcBmpMNJI/B+4CE9/Unkh98UporAc461GTthv+TVDuZXsBx7/WiwJb1Oh4tt4A==

tape@^4.5.1:
  version "4.17.0"
  resolved "https://npm.corp.kuaishou.com/tape/-/tape-4.17.0.tgz#de89f3671ddc5dad178d04c28dc6b0183f42268e"
  integrity sha512-KCuXjYxCZ3ru40dmND+oCLsXyuA8hoseu2SS404Px5ouyS0A99v8X/mdiLqsR5MTAyamMBN7PRwt2Dv3+xGIxw==
  dependencies:
    "@ljharb/resumer" "~0.0.1"
    "@ljharb/through" "~2.3.9"
    call-bind "~1.0.2"
    deep-equal "~1.1.1"
    defined "~1.0.1"
    dotignore "~0.1.2"
    for-each "~0.3.3"
    glob "~7.2.3"
    has "~1.0.3"
    inherits "~2.0.4"
    is-regex "~1.1.4"
    minimist "~1.2.8"
    mock-property "~1.0.0"
    object-inspect "~1.12.3"
    resolve "~1.22.6"
    string.prototype.trim "~1.2.8"

thread-stream@^0.15.1:
  version "0.15.2"
  resolved "https://npm.corp.kuaishou.com/thread-stream/-/thread-stream-0.15.2.tgz#fb95ad87d2f1e28f07116eb23d85aba3bc0425f4"
  integrity sha512-UkEhKIg2pD+fjkHQKyJO3yoIvAP3N6RlNFt2dUhcS1FGvCD1cQa1M/PGknCLFIyZdtJOWQjejp7bdNqmN7zwdA==
  dependencies:
    real-require "^0.1.0"

throttle-debounce@^3.0.1:
  version "3.0.1"
  resolved "https://npm.corp.kuaishou.com/throttle-debounce/-/throttle-debounce-3.0.1.tgz#32f94d84dfa894f786c9a1f290e7a645b6a19abb"
  integrity sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==

throttle-debounce@^5.0.0, throttle-debounce@^5.0.2:
  version "5.0.2"
  resolved "https://npm.corp.kuaishou.com/throttle-debounce/-/throttle-debounce-5.0.2.tgz#ec5549d84e053f043c9fd0f2a6dd892ff84456b1"
  integrity sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A==

timers-browserify@^2.0.4:
  version "2.0.12"
  resolved "https://npm.corp.kuaishou.com/timers-browserify/-/timers-browserify-2.0.12.tgz#44a45c11fbf407f34f97bccd1577c652361b00ee"
  integrity sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==
  dependencies:
    setimmediate "^1.0.4"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://npm.corp.kuaishou.com/toggle-selection/-/toggle-selection-1.0.6.tgz#6e45b1263f2017fa0acc7d89d78b15b8bf77da32"
  integrity sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==

ts-easing@^0.2.0:
  version "0.2.0"
  resolved "https://npm.corp.kuaishou.com/ts-easing/-/ts-easing-0.2.0.tgz#c8a8a35025105566588d87dbda05dd7fbfa5a4ec"
  integrity sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ==

tslib@2.3.0:
  version "2.3.0"
  resolved "https://npm.corp.kuaishou.com/tslib/-/tslib-2.3.0.tgz#803b8cdab3e12ba581a4ca41c8839bbb0dacb09e"
  integrity sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==

tslib@2.6.2:
  version "2.6.2"
  resolved "https://npm.corp.kuaishou.com/tslib/-/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

tslib@^1.10.0:
  version "1.14.1"
  resolved "https://npm.corp.kuaishou.com/tslib/-/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.0.0, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.1, tslib@^2.4.0, tslib@^2.4.1, tslib@^2.5.0, tslib@^2.5.3, tslib@^2.8.0:
  version "2.8.1"
  resolved "https://npm.corp.kuaishou.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tty-browserify@0.0.1:
  version "0.0.1"
  resolved "https://npm.corp.kuaishou.com/tty-browserify/-/tty-browserify-0.0.1.tgz#3f05251ee17904dfd0677546670db9651682b811"
  integrity sha512-C3TaO7K81YvjCgQH9Q1S3R3P3BtN3RIM8n+OvX4il1K1zgE8ZhI0op7kClgkxtutIE8hQrcrHBXvIheqKUUCxw==

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz#a72395450a4869ec033fd549371b47af3a2ee536"
  integrity sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "https://npm.corp.kuaishou.com/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz#8407a04f7d78684f3d252aa1a143d2b77b4160ce"
  integrity sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "https://npm.corp.kuaishou.com/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz#ae3698b8ec91a8ab945016108aef00d5bff12355"
  integrity sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "https://npm.corp.kuaishou.com/typed-array-length/-/typed-array-length-1.0.7.tgz#ee4deff984b64be1e118b0de8c9c877d5ce73d3d"
  integrity sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript@4.5.5:
  version "4.5.5"
  resolved "https://npm.corp.kuaishou.com/typescript/-/typescript-4.5.5.tgz#d8c953832d28924a9e3d37c73d729c846c5896f3"
  integrity sha512-TCTIul70LyWe6IJWT8QSYeA54WQe8EjQFU4wY52Fasj5UKx88LNYKCgBEHcOMOrFF1rKGbD8v/xcNWVUq9SymA==

typescript@^4.2.4:
  version "4.9.5"
  resolved "https://npm.corp.kuaishou.com/typescript/-/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==

typescript@^5.4.5:
  version "5.7.3"
  resolved "https://npm.corp.kuaishou.com/typescript/-/typescript-5.7.3.tgz#919b44a7dbb8583a9b856d162be24a54bf80073e"
  integrity sha512-84MVSjMEHP+FQRPy3pX9sTVV/INIex71s9TL2Gm5FG/WG1SqXeKyZ0k7/blY/4FdOzI12CBy1vGc4og/eus0fw==

ua-parser-js@^0.7.30:
  version "0.7.40"
  resolved "https://npm.corp.kuaishou.com/ua-parser-js/-/ua-parser-js-0.7.40.tgz#c87d83b7bb25822ecfa6397a0da5903934ea1562"
  integrity sha512-us1E3K+3jJppDBa3Tl0L3MOJiGhe1C6P0+nIvQAFYbxlMAx0h81eOwLmU57xgqToduDDPx3y5QsdjPfDu+FgOQ==

uglify-js@^2.6.2:
  version "2.8.29"
  resolved "https://npm.corp.kuaishou.com/uglify-js/-/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  integrity sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w==
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-js@^3.17.4:
  version "3.19.3"
  resolved "https://npm.corp.kuaishou.com/uglify-js/-/uglify-js-3.19.3.tgz#82315e9bbc6f2b25888858acd1fff8441035b77f"
  integrity sha512-v3Xu+yuwBXisp6QYTcH4UbH+xYJXqnq2m/LtQVWKWzYc1iehYnLixoQDN9FH6/j9/oybfd6W9Ghwkl8+UMKTKQ==

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"
  integrity sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q==

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "https://npm.corp.kuaishou.com/unbox-primitive/-/unbox-primitive-1.1.0.tgz#8d9d2c9edeea8460c7f35033a88867944934d1e2"
  integrity sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~6.21.0:
  version "6.21.0"
  resolved "https://npm.corp.kuaishou.com/undici-types/-/undici-types-6.21.0.tgz#691d00af3909be93a7faa13be61b3a5b50ef12cb"
  integrity sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==

update-browserslist-db@^1.1.1:
  version "1.1.2"
  resolved "https://npm.corp.kuaishou.com/update-browserslist-db/-/update-browserslist-db-1.1.2.tgz#97e9c96ab0ae7bcac08e9ae5151d26e6bc6b5580"
  integrity sha512-PPypAm5qvlD7XMZC3BujecnaOxwhrtoFR+Dqkk5Aa/6DssiH0ibKoketaj9w8LP7Bont1rYeoV5plxD7RTEPRg==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

url@^0.11.4:
  version "0.11.4"
  resolved "https://npm.corp.kuaishou.com/url/-/url-0.11.4.tgz#adca77b3562d56b72746e76b330b7f27b6721f3c"
  integrity sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==
  dependencies:
    punycode "^1.4.1"
    qs "^6.12.3"

use-sync-external-store@^1.4.0:
  version "1.4.0"
  resolved "https://npm.corp.kuaishou.com/use-sync-external-store/-/use-sync-external-store-1.4.0.tgz#adbc795d8eeb47029963016cefdf89dc799fcebc"
  integrity sha512-9WXSPC5fMv61vaupRkCKCxsPxBocVnwakBEkMIHHpkTTg6icbJtg6jzgtLDm4bl3cSHAca52rYWih0k4K3PfHw==

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

util@^0.12.4, util@^0.12.5:
  version "0.12.5"
  resolved "https://npm.corp.kuaishou.com/util/-/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utility-types@^3.9.0:
  version "3.11.0"
  resolved "https://npm.corp.kuaishou.com/utility-types/-/utility-types-3.11.0.tgz#607c40edb4f258915e901ea7995607fdf319424c"
  integrity sha512-6Z7Ma2aVEWisaL6TvBCy7P8rm2LQoPv6dJ7ecIaIixHcwfbJ0x7mWdbcwlIM5IGQxPZSFYeqRCqlOOeKoJYMkw==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://npm.corp.kuaishou.com/uuid/-/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

vm-browserify@^1.0.1, vm-browserify@^1.1.2:
  version "1.1.2"
  resolved "https://npm.corp.kuaishou.com/vm-browserify/-/vm-browserify-1.1.2.tgz#78641c488b8e6ca91a75f511e7a3b32a86e5dda0"
  integrity sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==

web-vitals@^0.2.4:
  version "0.2.4"
  resolved "https://npm.corp.kuaishou.com/web-vitals/-/web-vitals-0.2.4.tgz#ec3df43c834a207fd7cdefd732b2987896e08511"
  integrity sha512-6BjspCO9VriYy12z356nL6JBS0GYeEcA457YyRzD+dD6XYCQ75NKhcOHUMHentOE7OcVCIXXDvOm0jKFfQG2Gg==

whatwg-fetch@>=0.10.0, whatwg-fetch@^3.6.2:
  version "3.6.20"
  resolved "https://npm.corp.kuaishou.com/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz#580ce6d791facec91d37c72890995a0b48d31c70"
  integrity sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "https://npm.corp.kuaishou.com/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz#d76ec27df7fa165f18d5808374a5fe23c29b176e"
  integrity sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "https://npm.corp.kuaishou.com/which-builtin-type/-/which-builtin-type-1.2.1.tgz#89183da1b4907ab089a6b02029cc5d8d6574270e"
  integrity sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/which-collection/-/which-collection-1.0.2.tgz#627ef76243920a107e7ce8e96191debe4b16c2a0"
  integrity sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.18:
  version "1.1.18"
  resolved "https://npm.corp.kuaishou.com/which-typed-array/-/which-typed-array-1.1.18.tgz#df2389ebf3fbb246a71390e90730a9edb6ce17ad"
  integrity sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which-typed-array@^1.1.2:
  version "1.1.19"
  resolved "https://npm.corp.kuaishou.com/which-typed-array/-/which-typed-array-1.1.19.tgz#df03842e870b6b88e117524a4b364b6fc689f956"
  integrity sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

window-size@0.1.0:
  version "0.1.0"
  resolved "https://npm.corp.kuaishou.com/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"
  integrity sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg==

wordwrap@0.0.2:
  version "0.0.2"
  resolved "https://npm.corp.kuaishou.com/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"
  integrity sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==

wrappy@1:
  version "1.0.2"
  resolved "https://npm.corp.kuaishou.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@~8.17.1:
  version "8.17.1"
  resolved "https://npm.corp.kuaishou.com/ws/-/ws-8.17.1.tgz#9293da530bb548febc95371d90f9c878727d919b"
  integrity sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==

xmlhttprequest-ssl@~2.1.1:
  version "2.1.2"
  resolved "https://npm.corp.kuaishou.com/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.1.2.tgz#e9e8023b3f29ef34b97a859f584c5e6c61418e23"
  integrity sha512-TEU+nJVUUnA4CYJFLvK5X9AOeH4KvDvhIfm0vV1GaQRtchnG0hgK5p8hw/xjv8cunWYCsiPCSDzObPyhEwq3KQ==

xss@^1.0.15:
  version "1.0.15"
  resolved "https://npm.corp.kuaishou.com/xss/-/xss-1.0.15.tgz#96a0e13886f0661063028b410ed1b18670f4e59a"
  integrity sha512-FVdlVVC67WOIPvfOwhoMETV72f6GbW7aOabBC3WxN/oUdoEMDyLz4OgRv5/gck2ZeNqEQu+Tb0kloovXOfpYVg==
  dependencies:
    commander "^2.20.3"
    cssfilter "0.0.10"

xtend@^4.0.2:
  version "4.0.2"
  resolved "https://npm.corp.kuaishou.com/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://npm.corp.kuaishou.com/yallist/-/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yargs@~3.10.0:
  version "3.10.0"
  resolved "https://npm.corp.kuaishou.com/yargs/-/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  integrity sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A==
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://npm.corp.kuaishou.com/yocto-queue/-/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

zrender@5.6.1:
  version "5.6.1"
  resolved "https://npm.corp.kuaishou.com/zrender/-/zrender-5.6.1.tgz#e08d57ecf4acac708c4fcb7481eb201df7f10a6b"
  integrity sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==
  dependencies:
    tslib "2.3.0"
