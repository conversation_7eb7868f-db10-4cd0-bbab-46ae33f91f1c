{"compilerOptions": {"outDir": "./dist", "module": "esnext", "target": "es6", "lib": ["es6", "dom"], "sourceMap": true, "allowJs": true, "jsx": "react", "moduleResolution": "node", "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "strictNullChecks": true, "resolveJsonModule": true, "baseUrl": "./", "rootDir": "./", "typeRoots": ["./node_modules/@types", "./src/types"], "paths": {"@/*": ["src/*"], "@@/*": [".kmi/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "importsNotUsedAsValues": "preserve", "incremental": true}, "include": ["src", "src/types", "jia.config.ts", "kmi.config.ts"], "exclude": ["**/node_modules", "**/.*/"]}